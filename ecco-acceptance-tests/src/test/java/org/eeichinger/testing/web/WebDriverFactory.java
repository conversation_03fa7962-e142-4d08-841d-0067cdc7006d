package org.eeichinger.testing.web;

import com.ecco.acceptancetests.ui.steps.webdriver.DebugLoggingEventListener;
import com.ecco.data.client.WebApiSettings;
import org.junit.runner.Description;
import org.openqa.selenium.Dimension;
import org.openqa.selenium.Platform;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.chrome.ChromeDriver;
import org.openqa.selenium.chrome.ChromeOptions;
import org.openqa.selenium.firefox.FirefoxDriver;
import org.openqa.selenium.firefox.FirefoxOptions;
import org.openqa.selenium.ie.InternetExplorerDriver;
import org.openqa.selenium.remote.CapabilityType;
import org.openqa.selenium.remote.DesiredCapabilities;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.openqa.selenium.safari.SafariDriver;
import org.openqa.selenium.support.events.EventFiringWebDriver;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.MalformedURLException;
import java.net.URL;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static org.junit.internal.runners.statements.FailOnTimeoutWithThreadKey.getParentThread;

/**
 * Factory to create webdriver instances
 *
 * <AUTHOR> Upstone/OpenCredo
 * <AUTHOR> Eichinger/OpenCredo
 */
public class WebDriverFactory {

    private final Logger log = LoggerFactory.getLogger(getClass());

    private static WebDriverFactory instance = new WebDriverFactory();

    /**
     * To avoid creating unnecessary UI driver instances, we cache them per thread and clean up
     * after class.<p>
     * TODO: If we get to running multiple suites in parallel, then this collection will be being shared
     * by multiple subclasses having @AfterClass called.  We'll need to remove UserUI entries from the
     * map when that happens, or look at providing a pool.
     */
    private final Map<Thread,WebDriver> webDriverPerThread = new HashMap<>();

    /**
     * If required set your own WebDriverFactory implementation
     */
    public static void setInstance(WebDriverFactory instance) {
        if (instance == null) {
            throw new IllegalArgumentException("instance must not be null");
        }
        WebDriverFactory.instance = instance;
    }

    public static WebDriverFactory getInstance() {
        return instance;
    }

    public String getBaseUrl() {
        return WebApiSettings.APPLICATION_URL;
    }

    public void closeAllInstances() {
        log.debug("Closing WebDriver instances...");

        // Clone as quit removes each instance
        new ArrayList<>(webDriverPerThread.values()).forEach(WebDriver::quit);
        webDriverPerThread.clear();
        log.debug("Closed WebDriver instances");
    }

    /**
     * Force (re-)initialization of the current thread's webdriver instance.
     * Any previously existing webdriver instance will be quit()ed
     *
     * @return the newly initialized webdriver instance
     */
    WebDriver initializeWebDriver(Description description) {
        synchronized (webDriverPerThread) {
            Thread instanceKey = resolveParentThread();
            WebDriver webDriver = webDriverPerThread.get(instanceKey);
            if (webDriver != null) {
                return webDriver;
            }

            log.info("Creating WebDriver instance for parent thread {}", instanceKey.getName());

            try {
                webDriver = createNewWebDriver(instanceKey, description);
                webDriverPerThread.put(instanceKey, webDriver);
            } catch (Exception e) {
                log.error("Failed creating WebDriver instance for thread {} {}", instanceKey.getName(), e.getMessage(), e);
            }


            return webDriver;
        }
    }

    private Thread resolveParentThread() {
        Thread thisThread = Thread.currentThread();
        return getParentThread(thisThread);
    }

    public WebDriver getCurrentWebDriver() {
        synchronized (webDriverPerThread) {
            Thread instanceKey = resolveParentThread();
            WebDriver webDriver = webDriverPerThread.get(instanceKey);
            if (webDriver == null) {
                throw new UnsupportedOperationException("must initialize driver first");
            }
            return webDriver;
        }
    }

    private WebDriver createNewWebDriver(Thread instanceKey, Description description) {

        WebDriver webDriver = appropriateWebDriverForSettings(description);

        // make sure the driver is removed from the map once quit() or close() are called
        EventFiringWebDriver eventFiringWebDriver = new EventFiringWebDriver(webDriver) {
            @Override
            public void close() {
                quit();
            }

            @Override
            public void quit() {
                synchronized (webDriverPerThread) {
                    webDriverPerThread.remove(instanceKey);
                }
                super.quit();
                log.info("{} WebDriver instance closed on thread {}", getClass().getSimpleName(), instanceKey.getName());
            }
        };
        eventFiringWebDriver.register(new DebugLoggingEventListener());
        return eventFiringWebDriver;
    }

    public void closeAndRemoveCurrentWebDriver() {
        getInstance().getCurrentWebDriver().quit();
    }


    private WebDriver appropriateWebDriverForSettings(Description description) {
        WebDriver webDriver;
        switch (Settings.BROWSER) {
        case CHROME:
            webDriver = new ChromeDriver(); // - bah! doesn't work for me
            break;
        case CHROME_HEADLESS:
            ChromeOptions chromeOptions = new ChromeOptions();
            chromeOptions.addArguments("--headless");
            webDriver = new ChromeDriver(chromeOptions);
            break;
        case DEFAULT: // fallthru
        case FIREFOX:
            if (Settings.FIREFOX_PATH == null) {
                webDriver = new FirefoxDriver();
            }
            else {
                FirefoxOptions firefoxOptions = new FirefoxOptions();
                firefoxOptions.setBinary(Settings.FIREFOX_PATH);
                webDriver = new FirefoxDriver(firefoxOptions);
            }
            break;
        case REMOTE_CHROME:
            webDriver = new RemoteWebDriver(DesiredCapabilities.chrome());
            break;
        case REMOTE_FIREFOX:
            webDriver = new RemoteWebDriver(DesiredCapabilities.firefox());
            break;
        case SAFARI:
            webDriver = new SafariDriver();
            break;
        case IE:
            webDriver = new InternetExplorerDriver();
            break;
        case SAUCE:
            webDriver = createSauceWebDriver(description);
            break;
        default:
            throw new IllegalArgumentException("Unsupported option:" + Settings.BROWSER);
        }

        configureDefaults(webDriver);
        return webDriver;
    }

    private void configureDefaults(WebDriver webDriver) {

        webDriver.manage().window().setSize(new Dimension(960, 700));

        try {
            // If you are considering introducing implicit waits here, consider that any negative checks (such as
            // performed by BasePageObject.waitWhileElementDisplayed() will wait for the implicit timeout before returning.
            // You should therefore ensure that negative checks are replaced by positive checks using findElement or
            // findElements so that the implicit timeout takes the place fo the explicit negative check.
            // Or maybe just don't introduce implicit waits.
            // See also BasePageObject.findElementSoon and findElementsSoon as explicit substitutes.
            webDriver.manage().timeouts()
                .pageLoadTimeout(30, TimeUnit.SECONDS)
                .setScriptTimeout(30, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.warn("Ignoring error configuring timeouts: " + e.getMessage());
        }
    }

    private WebDriver createSauceWebDriver(Description description) {

        // if we have -DsaucePlatform= defined, then use that for settings, else
        // use env vars that Jenkins plugin provides
        DesiredCapabilities desiredCapabilities;
        if (System.getProperty("saucePlatform") == null) {
            desiredCapabilities = getJenkinsSauceCapabilities();
        }
        else {
            desiredCapabilities = capabilitiesFor(SauceLabsPlatform.valueOf(System.getProperty("saucePlatform")));
        }

        desiredCapabilities.setCapability("record-video", false);
        desiredCapabilities.setCapability("name", description.getMethodName());

        String userName = "cb_ecco"; // System.getenv("SAUCE_ONDEMAND_USERNAME");
        String accessKey = "6f85a3ff-3b64-4b9f-8bb8-701bb570901b"; // System.getenv("SAUCE_ONDEMAND_ACCESS_KEY");

        try {
            return new RemoteWebDriver(
                        new URL("http://" + userName + ":" + accessKey  + "@ondemand.saucelabs.com:80/wd/hub"),
                        desiredCapabilities);
        } catch (MalformedURLException e) {
            throw new RuntimeException(e);
        }
    }

    private DesiredCapabilities capabilitiesFor(SauceLabsPlatform platform) {
        DesiredCapabilities capabilities = platform.desiredCapabilities();
        if (platform.browserName() != null) capabilities.setBrowserName(platform.browserName());
        if (platform.version() != null) capabilities.setVersion(platform.version());
        if (platform.platform() != null) capabilities.setCapability(CapabilityType.PLATFORM, platform.platform());
        if (platform.deviceType() != null) capabilities.setCapability("device-type", platform.deviceType());
        if (platform.orientation() != null) capabilities.setCapability("device-orientation", platform.orientation());
        return capabilities;
    }

    private DesiredCapabilities getJenkinsSauceCapabilities() {
        DesiredCapabilities desiredCapabilities = new DesiredCapabilities();
        String browser = System.getenv("SELENIUM_BROWSER");
        String platform = System.getenv("SELENIUM_PLATFORM");
        String browserVersion = System.getenv("SELENIUM_VERSION");

        // Default to Firefox 22 on Linux
        browser = browser == null ? "firefox" : browser;
        platform = platform == null ? Platform.LINUX.name() : platform;
        browserVersion = browserVersion == null ? "22" : browserVersion;

        desiredCapabilities.setBrowserName(browser);
        desiredCapabilities.setVersion(browserVersion);
        desiredCapabilities.setCapability(CapabilityType.PLATFORM, platform);
        desiredCapabilities.setCapability("selenium.version", "2.30.0");
        return desiredCapabilities;
    }
}
