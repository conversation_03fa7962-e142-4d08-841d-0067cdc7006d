package org.eeichinger.testing.web;

import com.ecco.data.client.WebApiSettings;
import com.ecco.infrastructure.rest.RestClient;
import com.ecco.test.support.RetryRule;
import org.junit.Rule;
import org.junit.rules.RuleChain;
import org.junit.rules.TestRule;
import org.junit.rules.TimeoutWithThreadKey;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.TimeUnit;

/**
 * Convenience base class for webdriver test cases
 *
 * <AUTHOR>
 * <AUTHOR>
 */
public abstract class BaseWebDriverTest {

    private final String baseUrl = WebApiSettings.APPLICATION_URL;

    private final TestTracerRule loggerRule;
    private final TestRule screenshotRule = new ScreenshotCaptureRule();
    private final TestRule driverLifecycle = new DriverLifecycleRule();
    private final TimeoutWithThreadKey timeoutRule = new TimeoutWithThreadKey(Settings.TIMEOUT_SECS, TimeUnit.SECONDS);
    private final RetryRule retryRule = new RetryRule(Settings.MAX_RETRIES);

    protected RestClient restClient;


    @Rule
    public final RuleChain chain;

    protected final Logger log = LoggerFactory.getLogger(getClass());

    protected BaseWebDriverTest() {
        restClient = new RestClient(baseUrl);
        loggerRule = new TestTracerRule(restClient);
        chain = RuleChain
                .outerRule(retryRule)
                .around(timeoutRule) // has to be outside driverLifecycle rule as it executes inners in different thread
                .around(driverLifecycle)
                .around(screenshotRule)
                .around(loggerRule);
    }
}
