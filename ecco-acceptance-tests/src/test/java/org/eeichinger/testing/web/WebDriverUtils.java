package org.eeichinger.testing.web;

import org.jspecify.annotations.Nullable;

import org.openqa.selenium.JavascriptExecutor;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.support.ui.ExpectedCondition;
import org.openqa.selenium.support.ui.Wait;
import org.openqa.selenium.support.ui.WebDriverWait;

import static org.junit.Assert.fail;

/**
 * <AUTHOR>
 * <AUTHOR>
 * @since 26/01/12
 */
public class WebDriverUtils {

    public static void waitForPageLoaded(WebDriver webDriver, int maxPageTimeoutSeconds) {
        ExpectedCondition<Boolean> expectation = new ExpectedCondition<>() {
            @Override
            public Boolean apply(@Nullable WebDriver driver) {
                return ((JavascriptExecutor) driver).executeScript(
                        "return document.readyState").equals("complete");
            }

            @Override
            public boolean equals(@Nullable Object obj) {
                return super.equals(obj);
            }
        };

        Wait<WebDriver> wait = new WebDriverWait(webDriver, maxPageTimeoutSeconds);
        try {
            wait.until(expectation::apply);
        } catch (Throwable error) {
            fail("Timeout waiting for Page Load Request to complete.");
        }
    }
}
