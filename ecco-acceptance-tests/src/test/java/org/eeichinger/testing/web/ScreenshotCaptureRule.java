package org.eeichinger.testing.web;

import org.apache.commons.io.FileUtils;
import org.junit.rules.TestWatcher;
import org.junit.runner.Description;
import org.openqa.selenium.By;
import org.openqa.selenium.NoSuchElementException;
import org.openqa.selenium.OutputType;
import org.openqa.selenium.TakesScreenshot;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.logging.LogEntries;
import org.openqa.selenium.logging.LogEntry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;

/**
 * Capture screenshots on test failure and save them to
 * System.getProperty("user.dir") + "/target/surefire-reports"
 *
 * <AUTHOR>
 * <AUTHOR>
 */
public final class ScreenshotCaptureRule extends TestWatcher {

    private final static String SLASH = System.getProperty("file.separator");

    protected final Logger log = LoggerFactory.getLogger(getClass());

    private final String screenshotPath;


    public ScreenshotCaptureRule() {
        this(System.getProperty("user.dir") + SLASH + "target" + SLASH + "surefire-reports");
    }

    public ScreenshotCaptureRule(String screenshotPath) {
        this.screenshotPath = screenshotPath;
    }

    @Override
    public void failed(Throwable e, Description description) {
        String methodName = description.getMethodName();
        log.error(String.format("Failure in %s.%s", description.getClassName(), methodName), e);
        logJavascriptConsole();
        if (Settings.TAKE_ERROR_SCREENSHOTS) {
            captureScreen(methodName);
        }
    }

    private void logJavascriptConsole() {
        try {
            LogEntries entries = WebDriverFactory.getInstance().getCurrentWebDriver().manage().logs().get("browser");
            entries.getAll().stream()
                    .filter(this::shouldIncludeEntry)
                    .limit(10)
                    .forEach(logEntry -> {
                log.info("JS CONSOLE: " + logEntry.getMessage());
            });
        } catch (Exception e) {
            log.debug("Failed to capture JS Console:", e);
        }
    }

    private boolean shouldIncludeEntry(LogEntry logEntry) {
        return !(
                logEntry.getMessage().contains("Declaration dropped.") ||
                logEntry.getMessage().contains("addons.")
        );
    }

    public void captureScreen(String methodName) {
        captureEntirePageScreenshot(screenshotPath + SLASH + methodName + "_" + "top.png");
    }

    public void captureEntirePageScreenshot(String fileName) {
        if (!Settings.TAKE_ERROR_SCREENSHOTS) {
            return;
        }

        WebDriver driver = WebDriverFactory.getInstance().getCurrentWebDriver();
        if (!(driver instanceof TakesScreenshot)) {
            log.warn("Driver {} doesn't support screenshots - page source was:\n {}", driver.getClass().getName(), driver.getPageSource());
            return;
        }

        // Client side error
        try {
            WebElement expander = driver.findElement(By.cssSelector("button#errorDetailTrigger"));
            expander.click();

            WebElement errorStack = driver.findElement(By.cssSelector("div#errorDetail pre"));
            log.error("Client stack trace:\n" + errorStack.getText());
        } catch (NoSuchElementException e) {
            // do nothing
        }

        // server side error
        try {
            WebElement expander = driver.findElement(By.xpath("//button[contains(text(),'show details...')]"));
            expander.click();

            WebElement errorStack = driver.findElement(By.cssSelector("pre.error-stack"));
            log.error("Server stack trace:\n" + errorStack.getText());
        } catch (NoSuchElementException e) {
            // do nothing
        }

        File screenshotAs;
        try {
            screenshotAs = ((TakesScreenshot) driver).getScreenshotAs(OutputType.FILE);
        } catch (UnsupportedOperationException e) {
            log.warn("Driver {} doesn't support screenshots - page source was:\n {}", driver.getClass().getName(), driver.getPageSource());
            return;
        }
        try {
            FileUtils.copyFile(screenshotAs, new File(fileName));
            log.info("Screenshot saved to {}", fileName);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

}
