package org.eeichinger.testing.web;

import org.openqa.selenium.Platform;
import org.openqa.selenium.remote.DesiredCapabilities;

public enum SauceLabsPlatform {

    LINUX_FIREFOX(new DesiredCapabilities(), Platform.LINUX.name(), "firefox", "26", null, null ),
    IPAD_IOS7(DesiredCapabilities.ipad(), "OS X 10.9", null, "7", "portrait", null ),
    ANDROID4_TABLET(DesiredCapabilities.android(), "Linux", null, "4.0", "portrait", "tablet");

    private DesiredCapabilities desiredCapabilities;
    private String platform;
    private String browserName;
    private String version;
    private String orientation;
    private String deviceType;


    private SauceLabsPlatform(DesiredCapabilities desiredCapabilities, String platform, String browserName,
            String version, String orientation, String deviceType) {
        this.desiredCapabilities = desiredCapabilities;
        this.platform = platform;
        this.browserName = browserName;
        this.version = version;
        this.orientation = orientation;
        this.deviceType = deviceType;
    }

    public DesiredCapabilities desiredCapabilities() {
        return desiredCapabilities;
    }

    public String platform() {
        return platform;
    }

    public String browserName() {
        return browserName;
    }

    /** Browser version, but sometimes the version of the operating system (e.g. on Android/iOS) */
    public String version() {
        return version;
    }

    public String deviceType() {
        return deviceType;
    }

    public String orientation() {
        return orientation;
    }

}
