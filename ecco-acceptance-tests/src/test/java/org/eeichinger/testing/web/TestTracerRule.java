package org.eeichinger.testing.web;

import com.ecco.data.client.WebApiSettings;
import org.junit.rules.TestWatcher;
import org.junit.runner.Description;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.client.RestTemplate;

import com.ecco.infrastructure.rest.RestClient;
import com.google.common.collect.ImmutableMap;

/**
 * Logs test method execution
 *
 * <AUTHOR>
 */
public class TestTracerRule extends TestWatcher {

    protected final Logger log = LoggerFactory.getLogger(getClass());

    private RestTemplate template;

    public TestTracerRule(RestClient restClient) {
        this.template = restClient.template();
    }

    @Override
    public void starting(Description description) {
        log.info("Starting: {}", description.getMethodName());
        template.getForEntity(WebApiSettings.APPLICATION_URL + "/api/stats/tests/start/{className}/{methodName}/",
                String.class, ImmutableMap.of("className", description.getClassName(),
                        "methodName", description.getMethodName()));
    }

    @Override
    public void failed(Throwable e, Description description) {
        log.error("Failed: {}", description.getMethodName(), e);
    }

    @Override
    public void succeeded(Description description) {
        log.info("Success: {}", description.getMethodName());
    }
}
