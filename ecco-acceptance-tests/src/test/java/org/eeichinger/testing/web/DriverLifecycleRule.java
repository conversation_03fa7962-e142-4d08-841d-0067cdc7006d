package org.eeichinger.testing.web;

import org.junit.rules.TestWatcher;
import org.junit.runner.Description;

import lombok.extern.slf4j.Slf4j;

/**
 * Manages the WebDriver lifecycle for plain unit tests
 *
 * <AUTHOR>
 * @since 26/01/12
 */
@Slf4j
public class DriverLifecycleRule extends TestWatcher {

    @Override
    protected void starting(Description description) {
        WebDriverFactory.getInstance().initializeWebDriver(description);
    }

    @Override
    protected void failed(Throwable e, Description description) {
        // On-failure quit this instance and ensure it isn't reused
        WebDriverFactory.getInstance().closeAndRemoveCurrentWebDriver();
    }

    @Override
    protected void finished(Description description) {
//        WebDriverFactory.getInstance().getCurrentWebDriver().quit();
    }
}
