package org.eeichinger.testing.web;

import com.ecco.data.client.WebApiSettings;
import org.springframework.util.Assert;

import java.io.IOException;
import java.net.URL;
import java.util.Properties;

/**
 * Settings for webdriver tests
 *
 * <AUTHOR>
 * <AUTHOR>
 */
public class Settings {


    public static final boolean TAKE_ERROR_SCREENSHOTS;
    public static final Browser BROWSER;
    public static final String TEST_HARNESS_URL;
    public static final int WAIT_FOR_PAGE_LOADED_DELAY;
    public static final int MAX_RETRIES;
    public static final int TIMEOUT_SECS;
    public static final String FIREFOX_PATH;

    static {
        // Read properties file.
        Properties properties = new Properties();
        try {
            URL propUrl = Thread.currentThread().getContextClassLoader().getResource("test-environment.properties");
            properties.load(propUrl.openStream());
            properties.putAll(System.getProperties()); // merge -Dname=val in
        } catch (IOException e) {
            throw new RuntimeException(e); // can't do much else
        }

        // allow browser= but also support old tests.browser=FIREFOX etc.
        BROWSER = Browser.valueOf(properties.getProperty("browser", properties.getProperty("tests.browser", "DEFAULT")).toUpperCase());
        TAKE_ERROR_SCREENSHOTS = Boolean.parseBoolean(properties.getProperty("tests.takeerrorscreenshots", "true"));
        TEST_HARNESS_URL = trimTrailingSlash(properties.getProperty("testharness.host.url"));
        WAIT_FOR_PAGE_LOADED_DELAY = Integer.parseInt(properties.getProperty("debug.page.delay", "0")); // milliseconds
        System.err.println("*** APPLICATION_URL = " + WebApiSettings.APPLICATION_URL + " ***");
        MAX_RETRIES = Integer.valueOf(properties.getProperty("tests.retries", "5"));
        Assert.state(MAX_RETRIES >= 1, "minimum supplied 'tests.retries' argument must be >= 1 (think of it as a maxAttempts!)");
        TIMEOUT_SECS = Settings.BROWSER == Browser.SAUCE ? 500 : 200; // tests are slower on SauceLabs (due to video etc)
        FIREFOX_PATH = properties.getProperty("webdriver.firefox.bin");
    }

    private static String trimTrailingSlash(String url) {
        if (url.endsWith("/")){
            url = url.substring(0, url.length() - 1);
        }
        return url;
    }

}
