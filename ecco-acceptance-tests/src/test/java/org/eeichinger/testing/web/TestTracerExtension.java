package org.eeichinger.testing.web;

import com.ecco.data.client.WebApiSettings;
import com.ecco.infrastructure.rest.RestClient;
import com.google.common.collect.ImmutableMap;
import org.junit.jupiter.api.extension.AfterTestExecutionCallback;
import org.junit.jupiter.api.extension.BeforeTestExecutionCallback;
import org.junit.jupiter.api.extension.ExtensionContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.client.RestTemplate;

/**
 * JUnit 5 extension that provides the same functionality as TestTracerRule
 * for logging test method execution.
 */
public class TestTracerExtension implements BeforeTestExecutionCallback, AfterTestExecutionCallback {

    protected final Logger log = LoggerFactory.getLogger(getClass());
    private final RestTemplate template;

    public TestTracerExtension(RestClient restClient) {
        this.template = restClient.template();
    }

    @Override
    public void beforeTestExecution(ExtensionContext context) {
        String methodName = context.getRequiredTestMethod().getName();
        String className = context.getRequiredTestClass().getName();

        log.info("Starting: {}", methodName);
        template.getForEntity(WebApiSettings.APPLICATION_URL + "/api/stats/tests/start/{className}/{methodName}/",
                String.class, ImmutableMap.of("className", className, "methodName", methodName));
    }

    @Override
    public void afterTestExecution(ExtensionContext context) {
        String methodName = context.getRequiredTestMethod().getName();

        if (context.getExecutionException().isPresent()) {
            log.error("Failed: {}", methodName, context.getExecutionException().get());
        } else {
            log.info("Success: {}", methodName);
        }
    }
}
