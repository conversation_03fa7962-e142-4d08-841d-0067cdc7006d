package org.openqa.selenium.browserlaunchers;
import org.openqa.selenium.Capabilities;
import org.openqa.selenium.Proxy;

/**
 * Deals with lack of support for Selenium after 2.44ish and before 3
 *
 * See https://github.com/detro/ghostdriver/pull/399#issuecomment-63202396
 */
public class Proxies {
    public static Proxy extractProxy(Capabilities capabilities) {
        return Proxy.extractFrom(capabilities);
    }
}