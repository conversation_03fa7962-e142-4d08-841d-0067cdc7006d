package com.ecco.acceptancetests.fixtures

import com.ecco.acceptancetests.ui.pages.supportplan.RiskManagementPage
import java.util.*

class RiskManagementFixture(private val page: RiskManagementPage) {
    fun setOutcomeLevelRed(): RiskManagementFixture {
        page.setOutcomeLevelRed()
        return this
    }

    fun setOutcomeLevelAmber(): RiskManagementFixture {
        page.setOutcomeLevelAmber()
        return this
    }

    fun setOutcomeLevelGreen(): RiskManagementFixture {
        page.setOutcomeLevelGreen()
        return this
    }

    fun setTrigger(trigger: String): RiskManagementFixture {
        page.setTrigger(trigger)
        return this
    }

    fun setControl(control: String): RiskManagementFixture {
        page.setControl(control)
        return this
    }

    fun setDate(date: Date): RiskManagementFixture {
        page.setWorkDate(date)
        return this
    }

    fun setComment(comment: String): RiskManagementFixture {
        page.setComment(comment)
        return this
    }

    fun addAction(action: String): RiskManagementFixture {
        page.clickStatus(action)
        return this
    }

    fun addTarget(target: String, date: Date): RiskManagementFixture {
        page.setTarget(target, date)
        return this
    }

    fun addLink(link: String): RiskManagementFixture {
        page.clickLinkAction(link)
        return this
    }

    fun changeTab(outcome: String): RiskManagementFixture {
        page.outcome(outcome)
        return this
    }

    fun save(): RiskManagementFixture {
        page.save()
        return this
    }

    fun clickClient(client: String): RiskManagementFixture {
        page.clickNameBack(client)
        return this
    }
}