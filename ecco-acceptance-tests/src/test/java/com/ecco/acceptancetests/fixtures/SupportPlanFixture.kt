package com.ecco.acceptancetests.fixtures

import com.ecco.acceptancetests.ui.pages.supportplan.SupportPlanBasePage
import java.util.*

class SupportPlanFixture(private val page: SupportPlanBasePage) {
    fun setDate(date: Date): SupportPlanFixture {
        page.setWorkDate(date)
        return this
    }

    fun setComment(comment: String): SupportPlanFixture {
        page.setComment(comment)
        return this
    }

    fun addAction(action: String): SupportPlanFixture {
        page.clickStatus(action)
        return this
    }

    fun addTarget(target: String, date: Date): SupportPlanFixture {
        page.setTarget(target, date)
        return this
    }

    fun addLink(link: String): SupportPlanFixture {
        page.clickLinkAction(link)
        return this
    }

    fun changeTab(outcome: String): SupportPlanFixture {
        page.outcome(outcome)
        return this
    }

    fun save(): SupportPlanFixture {
        page.save()
        return this
    }

    fun clickClient(client: String): SupportPlanFixture {
        page.clickNameBack(client)
        return this
    }
}