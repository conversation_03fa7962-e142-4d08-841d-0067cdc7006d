package com.ecco.acceptancetests.givens

import com.ecco.acceptancetests.steps.RiskManagementContext
import com.ecco.acceptancetests.ui.smoke.RiskManagementTests

/**
 * @param testsInstance
 * the test object being run
 */
class RiskManagementGiven(private val context: RiskManagementContext, testInstance: RiskManagementTests) :
    BaseGiven<RiskManagementGiven>(testInstance) {
    fun `when`(): RiskManagementContext = context
}