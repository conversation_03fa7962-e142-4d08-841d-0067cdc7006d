package com.ecco.acceptancetests.givens

import com.ecco.acceptancetests.steps.SupportPlanContext
import com.ecco.acceptancetests.ui.smoke.SupportPlanTests
import com.ecco.data.client.ServiceOptions
import com.ecco.webApi.evidence.ReferralViewModel
import org.joda.time.LocalDate

class SupportPlanGiven(private val context: SupportPlanContext, private val testClass: SupportPlanTests) {
    private var referral: ReferralViewModel? = null

    fun createReferral(firstName: String, lastName: String, date: LocalDate, service: ServiceOptions): SupportPlanGiven {
        referral = testClass.referralActor.createReferralAsStarted(firstName, lastName, date, service)
        return this
    }

    fun `when`(): SupportPlanContext = context

    // hacky - the createObject .. as
    fun `as`(fieldName: String): SupportPlanGiven {
        try {
            val field = testClass.javaClass.getDeclaredField(fieldName)
            field.isAccessible = true
            field.set(
                testClass,
                referral,
            ) // this is the issue here. if we need more than one object set on the test instance
            return this
        } catch (e: Exception) {
            throw RuntimeException(e)
        }
    }
}