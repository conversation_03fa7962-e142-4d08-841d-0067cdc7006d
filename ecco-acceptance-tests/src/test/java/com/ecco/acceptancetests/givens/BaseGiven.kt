package com.ecco.acceptancetests.givens

import com.ecco.acceptancetests.ui.BaseSeleniumTest
import com.ecco.data.client.ReferralOptions
import com.ecco.data.client.ServiceOptions
import com.ecco.webApi.evidence.ReferralViewModel

abstract class BaseGiven<CONCRETE_TYPE : BaseGiven<CONCRETE_TYPE>>(private val testInstance: BaseSeleniumTest) {
    private var referral: ReferralViewModel? = null

    fun ensureReferral(
        code: String,
        firstName: String,
        lastName: String,
        service: ServiceOptions,
        referralOptions: ReferralOptions,
    ): BaseGiven<CONCRETE_TYPE> {
        // check for existence
        val response = testInstance.referralActor.getReferralByCode(code)
        this.referral = response.body
        if (this.referral == null || !response.statusCode.is2xxSuccessful) {
            createReferral(firstName, lastName, null, service, referralOptions)
            // apply the code to the id so we can find it again
            testInstance.referralActor.applyCode(referral!!.referralId, code)
            this.referral!!.referralCode = code
        }
        return this
    }

    fun createReferral(
        firstName: String,
        lastName: String,
        date: String?,
        service: ServiceOptions,
        referralOptions: ReferralOptions,
    ): BaseGiven<CONCRETE_TYPE> {
        // create a new referral

        val id =
            testInstance.referralStepsWebApi.processReferralToId(
                referralOptions,
                service,
                firstName,
                lastName,
                "AB123456C",
                "this is a new referral comment 1",
            )

        this.referral = testInstance.referralActor.getReferralById(id).body
        return this
    }

    fun `as`(fieldName: String): BaseGiven<CONCRETE_TYPE> {
        try {
            val field = testInstance.javaClass.getDeclaredField(fieldName)
            field.isAccessible = true
            field.set(
                testInstance,
                referral,
            ) // this is the issue here. if we need more than one object set on the test instance
            return this
        } catch (e: Exception) {
            throw RuntimeException(e)
        }
    }
}