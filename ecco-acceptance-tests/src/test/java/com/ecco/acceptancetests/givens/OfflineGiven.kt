package com.ecco.acceptancetests.givens

import com.ecco.acceptancetests.steps.OfflineContext
import com.ecco.acceptancetests.ui.BaseSeleniumTest

class OfflineGiven(private val context: OfflineContext, testInstance: BaseSeleniumTest) : BaseGiven<OfflineGiven>(testInstance) {
    fun `when`(): OfflineContext = context

    fun offlineIsEnabled() {
        // we're assuming it is because we ran with -Ddb.extraContexts=acceptanceTests
    }
}