package com.ecco.acceptancetests.givens

import com.ecco.acceptancetests.api.groupsupport.GroupSupportAPITests
import com.ecco.acceptancetests.steps.GroupSupportContext
import com.ecco.acceptancetests.ui.smoke.GroupSupportTests

class GroupSupportGiven(private val context: GroupSupportContext, testInstance: GroupSupportTests) :
    BaseGiven<GroupSupportGiven>(testInstance) {
    private val apiTests: GroupSupportAPITests = GroupSupportAPITests()

    // TODO test creating using the ui
    fun ensureGroupSupportActivity(): GroupSupportContext {
        apiTests.loginAsSysadmin() // required since setting up config
        apiTests.createGroupSupportActivityRequirements()
        apiTests.canCreateActivityAndInviteAndFindInLists()
        apiTests.logout()
        return context
    }

    fun `when`(): GroupSupportContext = context
}