package com.ecco.acceptancetests;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import org.assertj.core.api.Assertions;

public class TestUtils {

    public static void listContains(List<String> superset, String[] expectedSubset) {
        List<String> expectedList = new ArrayList<>();
        Collections.addAll(expectedList, expectedSubset);
        Assertions.assertThat(superset).containsAll(expectedList);
    }

    public static void listContains(List<String> superset, String expectedEntry) {
        Assertions.assertThat(superset).contains(expectedEntry);
    }

    public static void stringsMatch(String expected, String actual) {
        Assertions.assertThat(actual).isEqualTo(expected);
    }

}
