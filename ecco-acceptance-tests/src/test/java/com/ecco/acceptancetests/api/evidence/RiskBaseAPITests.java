package com.ecco.acceptancetests.api.evidence;

import com.ecco.data.client.ServiceOptions;
import com.ecco.dom.EvidenceGroup;
import com.ecco.dto.ChangeViewModel;
import com.ecco.evidence.EvidenceTask;
import com.ecco.serviceConfig.viewModel.OutcomeViewModel;
import com.ecco.webApi.evidence.*;
import org.joda.time.LocalDate;

import java.util.List;
import java.util.Objects;
import java.util.Random;
import java.util.UUID;
import java.util.stream.Stream;

import static com.ecco.dto.ChangeViewModel.changeNullTo;
import static org.assertj.core.api.Assertions.assertThat;

public abstract class RiskBaseAPITests extends BaseEvidenceCommandAPITests<EvidenceThreatWorkViewModel> {
    protected static Long riskAreaDefId;
    protected static Integer riskActionDefId;
    protected static Long fileId;

    public RiskBaseAPITests(EvidenceGroup evidenceGroup, EvidenceTask defaultEvidenceTask, ServiceOptions service) {
        super(evidenceGroup, defaultEvidenceTask, service);
    }

    @Override
    protected void ensureDefinitionIds() {
        if (riskAreaDefId == null || riskActionDefId == null) {
            List<OutcomeViewModel> threatOutcomes = outcomeActor.findAllOutcomeThreatsByServiceName(service.getServiceName()).getBody();
            assert threatOutcomes != null;
            Stream<OutcomeViewModel> filteredOutcomes = threatOutcomes.stream()
                    .filter(input -> "substance misuse".equals(input.name));
            //noinspection OptionalGetWithoutIsPresent
            OutcomeViewModel outcome = filteredOutcomes.findFirst().get();
            riskAreaDefId = outcome.id;
            riskActionDefId = outcome.actionGroups.get(0).actions.get(0).id;
        }
    }

    protected void createWorkItemWithRiskRequired(List<UUID> supportWorkUuids, UUID supportWorkUuid) {
        supportWorkUuids.add(supportWorkUuid);
        CommentCommandViewModel supportComment = createCommentCommandViewModel(supportWorkUuid, getServiceRecipientId(),
                EvidenceGroup.NEEDS, EvidenceTask.NEEDS_ASSESSMENT, "work with risk management", workDateUpTo50DaysInPast, 10 + new Random().nextInt(145));
        supportComment.riskManagementRequired = ChangeViewModel.create(null,  true);
        commandActor.executeCommand(supportComment );
    }

    protected EvidenceThreatWorkViewModel getAndVerifyRiskUpdateGotApplied(BaseGoalUpdateCommandViewModel riskUpdateCmd) {
        EvidenceThreatWorkViewModel evidence = findWorkByUuid(riskUpdateCmd.workUuid);
        assert evidence.riskActions.get(0).targetDateTime != null;
        assert riskUpdateCmd.targetDateChange != null;
        assert evidence.riskActions.get(0).targetDateTime != null;
        assertThat(evidence.riskActions.get(0).targetDateTime.toLocalDate()).is(resultOfChange(riskUpdateCmd.targetDateChange));
        return evidence;
    }

    protected EvidenceThreatWorkViewModel getAndVerifyAreaUpdateGotApplied(AreaUpdateCommandViewModel areaUpdateCmd) {
        EvidenceThreatWorkViewModel evidence = findWorkByUuid(areaUpdateCmd.workUuid);
        assert areaUpdateCmd.levelChange != null;
        assertThat(evidence.riskAreas.get(0).level).is(resultOfChange(areaUpdateCmd.levelChange));
        return evidence;
    }

    protected GoalUpdateCommandViewModel sendRiskUpdateCommand() {
        GoalUpdateCommandViewModel riskUpdateCmd = createGoalRiskUpdateCommandViewModel(defaultEvidenceTask);
        commandActor.executeCommand(riskUpdateCmd);
        return riskUpdateCmd;
    }

    private GoalUpdateCommandViewModel createGoalRiskUpdateCommandViewModel(EvidenceTask evidenceTask) {
        GoalUpdateCommandViewModel vm =
                new GoalUpdateCommandViewModel(workUuid, getServiceRecipientId(), evidenceGroup,
                        evidenceTask, riskActionDefId, UUID.randomUUID(), null);

        vm.targetDateChange = changeNullTo(new LocalDate(2015,4,7));
        vm.likelihoodChange = changeNullTo(4);
        vm.severityChange = changeNullTo(7);
        vm.triggerChange = changeNullTo("trigger");
        vm.controlChange = changeNullTo("control");

        return vm;
    }

    protected AreaUpdateCommandViewModel sendRiskAreaUpdateCommand() {
        AreaUpdateCommandViewModel areaUpdateCmd = createAreaUpdateCommandViewModel(defaultEvidenceTask);
        commandActor.executeCommand(areaUpdateCmd);
        return areaUpdateCmd;
    }

    private AreaUpdateCommandViewModel createAreaUpdateCommandViewModel(EvidenceTask evidenceTask) {
        AreaUpdateCommandViewModel vm =
                new AreaUpdateCommandViewModel(workUuid, getServiceRecipientId(), evidenceGroup, evidenceTask, riskAreaDefId);

        vm.levelChange = changeNullTo(1);
        return vm;
    }

    @Override
    protected List<EvidenceThreatWorkViewModel> findWorkSummaryByServiceRecipientId() {
        return Objects.requireNonNull(riskEvidenceActor.findAllThreatWorkSummaryByServiceRecipientId(getServiceRecipientId()).getBody()).getContent();
    }

    protected List<EvidenceThreatWorkViewModel> findWorkSummaryWithAttachmentsByServiceRecipientId() {
        return Objects.requireNonNull(riskEvidenceActor.findAttachmentsThreatWorkSummaryByServiceRecipientId(getServiceRecipientId()).getBody()).getContent();
    }

    protected void verifyLikelihoodAndSeverity(EvidenceThreatWorkViewModel evidence, GoalUpdateCommandViewModel riskUpdateCmd) {
        assert riskUpdateCmd.likelihoodChange != null;
        assertThat(evidence.riskActions.get(0).likelihood).is(resultOfChange(riskUpdateCmd.likelihoodChange));
        assert riskUpdateCmd.severityChange != null;
        assertThat(evidence.riskActions.get(0).severity).is(resultOfChange(riskUpdateCmd.severityChange));
    }

    protected void verifyTriggerAndControl(EvidenceThreatWorkViewModel evidence, GoalUpdateCommandViewModel riskUpdateCmd) {
        assert riskUpdateCmd.triggerChange != null;
        assertThat(evidence.riskActions.get(0).hazard).is(resultOfChange(riskUpdateCmd.triggerChange));
        assert riskUpdateCmd.controlChange != null;
        assertThat(evidence.riskActions.get(0).intervention).is(resultOfChange(riskUpdateCmd.controlChange));
    }
}
