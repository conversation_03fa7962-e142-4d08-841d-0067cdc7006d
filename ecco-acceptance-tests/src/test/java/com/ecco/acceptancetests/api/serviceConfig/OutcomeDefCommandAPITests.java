package com.ecco.acceptancetests.api.serviceConfig;

import com.ecco.acceptancetests.api.BaseJsonTest;
import com.ecco.test.support.UniqueDataService;
import com.ecco.serviceConfig.viewModel.OutcomeViewModel;
import com.ecco.serviceConfig.viewModel.RiskAreaViewModel;
import com.ecco.webApi.evidence.BaseCommandViewModel;
import com.ecco.dto.ChangeViewModel;
import com.ecco.webApi.serviceConfig.OutcomeDefCommandViewModel;
import com.ecco.webApi.serviceConfig.OutcomeThreatDefCommandViewModel;
import org.junit.jupiter.api.Test;
import org.springframework.web.client.HttpClientErrorException.BadRequest;

import java.util.UUID;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;

public class OutcomeDefCommandAPITests extends BaseJsonTest {

    private static String ADD = BaseCommandViewModel.OPERATION_ADD;
    private static String UPDATE = BaseCommandViewModel.OPERATION_UPDATE;
    private static String REMOVE = BaseCommandViewModel.OPERATION_REMOVE;

    private static String[] outcomeNames = new String[] {
            UniqueDataService.instance.nameFor("outcome to add"),
            UniqueDataService.instance.nameFor("outcome to remove"),
            UniqueDataService.instance.nameFor("outcome to update"),
            UniqueDataService.instance.nameFor("outcome updated")};

    @Test
    public void canAddNeeds() { canAddNeeds(outcomeNames[0]); }

    @Test
    public void canAddThreat() {
        canAddThreat(outcomeNames[0]);
    }

    // HTTP 400 - java.lang.IllegalArgumentException: invalid operation: remove
    @Test
    public void canAddCantRemove() {
        OutcomeViewModel vm = canAddNeeds(outcomeNames[1]);

        OutcomeDefCommandViewModel cmd = new OutcomeDefCommandViewModel(REMOVE, vm.uuid);
        assertThrows(BadRequest.class, () -> commandActor.executeCommand(cmd));

        OutcomeViewModel retrieved = outcomeActor.findNeedByUUID(vm.uuid).getBody();
        assertNotNull(retrieved);
        assertEquals(outcomeNames[1], retrieved.name);
        assertEquals(vm.uuid, retrieved.uuid);
    }

    @Test
    public void canAddThenUpdate() {
        OutcomeViewModel vm = canAddNeeds(outcomeNames[2]);

        String newName = outcomeNames[3];
        OutcomeDefCommandViewModel cmd = new OutcomeDefCommandViewModel(UPDATE, vm.uuid);
        cmd.nameChange = ChangeViewModel.create(vm.name, newName);
        commandActor.executeCommand(cmd);

        OutcomeViewModel retrieved = outcomeActor.findNeedByUUID(vm.uuid).getBody();
        assertNotNull(retrieved);
        assertEquals(newName, retrieved.name);
    }

    private OutcomeViewModel canAddNeeds(String name) {
        OutcomeDefCommandViewModel cvm = new OutcomeDefCommandViewModel(ADD, UUID.randomUUID());
        return canAddOutcome(cvm, name);
    }

    private OutcomeViewModel canAddOutcome(OutcomeDefCommandViewModel cvm, String name) {
        cvm.nameChange = ChangeViewModel.changeNullTo(name);
        commandActor.executeCommand(cvm);

        OutcomeViewModel retrieved = outcomeActor.findNeedByUUID(cvm.outcomeDefUuid).getBody();
        assertNotNull(retrieved);
        assertEquals(cvm.outcomeDefUuid, retrieved.uuid);
        assertEquals(0, retrieved.actionGroups.size());
        //noinspection ConstantConditions
        assertEquals(cvm.nameChange.to, retrieved.name);
        return retrieved;
    }

    private RiskAreaViewModel canAddThreat(String name) {
        OutcomeThreatDefCommandViewModel cvm = new OutcomeThreatDefCommandViewModel(ADD, UUID.randomUUID());
        cvm.nameChange = ChangeViewModel.changeNullTo(name);
        commandActor.executeCommand(cvm);

        RiskAreaViewModel retrieved = outcomeActor.findThreatByUUID(cvm.outcomeDefUuid).getBody();
        assertNotNull(retrieved);
        assertEquals(cvm.outcomeDefUuid, retrieved.uuid);
        assertEquals(0, retrieved.actionGroups.size());
        //noinspection ConstantConditions
        assertEquals(cvm.nameChange.to, retrieved.name);
        return retrieved;
    }

}
