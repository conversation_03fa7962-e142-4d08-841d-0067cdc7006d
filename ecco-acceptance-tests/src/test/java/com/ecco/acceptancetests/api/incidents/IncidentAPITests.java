package com.ecco.acceptancetests.api.incidents;

import com.ecco.acceptancetests.api.BaseJsonTest;
import com.ecco.dto.ChangeViewModel;
import com.ecco.test.support.UniqueDataService;
import com.ecco.webApi.incidents.IncidentDetailCommandViewModel;
import com.ecco.webApi.incidents.IncidentViewModel;
import org.junit.jupiter.api.Test;

import java.time.LocalDate;

import static org.junit.Assert.assertEquals;

public class IncidentAPITests extends BaseJsonTest {

    private final UniqueDataService unique = UniqueDataService.instance;

    @Test
    public void configSetupOnly() {
        /*
        INCIDENTS
        --------
        enable feature: menu.incidents
        list - permission ‘incidents’ isn't used, we're based on service/projects of the incident
        /<instance>/p/r/incident?svcCatId=3000 (svcCat to extract the serviceId - needs a null project?)
        category list name - incident-category, but can create children lists (data import?) (eg 'incident-category-child' / child2) and point each to its parent
        link client list name - incident-link-client
        referralDetails formDefinitions - 1,2,3 for key(first step),form(main step),full(both for view in file)
        typical flow:
            - overview with tabOrder overview,tasks,attachments,supportFwd,contacts,audit / !radarChart / incidents_commenttype
            - referralDetails
            - case notes
            - decideFinal renamed incident approved
            - threatAssessmentReduction - incident-flags
        */
    }

    @Test
    public void canEditIncident() {

        // GIVEN a job and a worker
        var dte = LocalDate.now().minusYears(1);
        var incidentId = incidentActor.createIncident("inc-1", dte);
        var incident = incidentActor.getIncidentById(incidentId);

        // WHEN update the incident
        var updatedDte = LocalDate.now().minusMonths(1);
        {
            IncidentDetailCommandViewModel cmd = new IncidentDetailCommandViewModel(incident.getServiceRecipientId(), null);
            //cmd.setName(ChangeViewModel.create("inc-1", "inc-2"));
            cmd.setReceivedDate(ChangeViewModel.create(dte, updatedDte));

            commandActor.executeCommand(cmd);
        }

        // THEN we get the updated details back
        {
            IncidentViewModel wvmUpdated = incidentActor.getIncidentById(incident.getIncidentId());
            assert wvmUpdated != null;
            assertEquals(wvmUpdated.getReceivedDate(), updatedDte);
        }
    }

}
