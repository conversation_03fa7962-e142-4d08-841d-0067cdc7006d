package com.ecco.acceptancetests.api;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.AfterEachCallback;
import org.junit.jupiter.api.extension.BeforeEachCallback;
import org.junit.jupiter.api.extension.ExtensionContext;
import org.junit.jupiter.api.extension.TestExecutionExceptionHandler;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * JUnit 5 extension that invokes JUnit 4 @Before and @After methods and applies JUnit 4 Rules
 * to support running JUnit 5 tests in classes that extend JUnit 4 test classes.
 */
public class JUnit4LifecycleExtension implements BeforeEachCallback, AfterEachCallback, TestExecutionExceptionHandler {

    @Override
    public void beforeEach(ExtensionContext context) throws Exception {
        Object testInstance = context.getRequiredTestInstance();

        // Run @Before methods
        invokeMethodsWithAnnotation(testInstance, BeforeEach.class);
    }

    @Override
    public void afterEach(ExtensionContext context) throws Exception {
        Object testInstance = context.getRequiredTestInstance();
        invokeMethodsWithAnnotation(testInstance, AfterEach.class);

        // Note: TestRules are applied around the test method execution,
        // so we don't need to explicitly handle them in afterEach
    }

    @Override
    public void handleTestExecutionException(ExtensionContext context, Throwable throwable) throws Throwable {
        // This allows exceptions to be processed by any TestRules that handle exceptions
        throw throwable;
    }

    private void invokeMethodsWithAnnotation(Object testInstance, Class<? extends java.lang.annotation.Annotation> annotationClass) throws Exception {
        // For @Before methods, we need to execute from parent to child
        // For @After methods, we need to execute from child to parent
        boolean isBeforeAnnotation = BeforeEach.class.equals(annotationClass);

        if (isBeforeAnnotation) {
            // Execute @Before methods from parent to child
            invokeMethodsInOrder(testInstance, annotationClass, true);
        } else {
            // Execute @After methods from child to parent
            invokeMethodsInOrder(testInstance, annotationClass, false);
        }
    }

    private void invokeMethodsInOrder(Object testInstance, Class<? extends java.lang.annotation.Annotation> annotationClass, boolean parentToChild) throws Exception {
        Class<?> testClass = testInstance.getClass();

        // Collect all classes in the hierarchy
        List<Class<?>> classHierarchy = new ArrayList<>();
        while (testClass != null && testClass != Object.class) {
            classHierarchy.add(testClass);
            testClass = testClass.getSuperclass();
        }

        // If we want parent to child order, reverse the list
        if (parentToChild) {
            Collections.reverse(classHierarchy);
        }

        // Execute methods in the desired order
        for (Class<?> clazz : classHierarchy) {
            for (Method method : clazz.getDeclaredMethods()) {
                if (method.isAnnotationPresent(annotationClass)) {
                    method.setAccessible(true);
                    method.invoke(testInstance);
                }
            }
        }
    }
}
