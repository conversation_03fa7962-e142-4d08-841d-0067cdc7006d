package com.ecco.acceptancetests.api.referral;

import com.ecco.acceptancetests.api.BaseJsonTest;
import com.ecco.calendar.core.util.DateTimeUtils;
import com.ecco.calendar.core.webapi.EventAttendee;
import com.ecco.calendar.core.webapi.EventResource;
import com.ecco.data.client.ServiceOptions;
import com.ecco.data.client.actors.ReferralActor;
import com.ecco.data.client.steps.ReferralData;
import com.ecco.dom.*;
import com.ecco.dom.commands.UserAccessAuditLevel;
import com.ecco.dto.ChangeViewModel;
import com.ecco.evidence.EvidenceTask;
import com.ecco.evidence.dom.TaskStatus;
import com.ecco.infrastructure.config.web.ConvertersConfig;
import com.ecco.infrastructure.time.Clock;
import com.ecco.service.LinearWorkflowService;
import com.ecco.servicerecipient.AcceptState;
import com.ecco.test.support.UniqueDataService;
import com.ecco.webApi.clients.InboundReferralParams;
import com.ecco.webApi.clients.InboundReferralResource;
import com.ecco.webApi.contacts.AgencyViewModel;
import com.ecco.webApi.contacts.IndividualViewModel;
import com.ecco.webApi.evidence.BaseCommandViewModel;
import com.ecco.webApi.evidence.ReferralSummaryViewModel;
import com.ecco.webApi.evidence.ReferralViewModel;
import com.ecco.webApi.featureConfig.SessionDataViewModel;
import com.ecco.webApi.listsConfig.ListDefinitionEntryViewModel;
import com.ecco.webApi.serviceConfig.TaskDefinitionEntryCommandViewModel;
import com.ecco.webApi.taskFlow.*;
import com.ecco.webApi.users.UserAccessAuditCommandDto;
import com.ecco.webApi.viewModels.Result;
import com.ecco.webApi.viewModels.WorkflowTaskViewModel;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.google.common.collect.ImmutableMap;
import lombok.RequiredArgsConstructor;
import org.assertj.core.api.Assertions;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;

import org.jspecify.annotations.NonNull;
import java.io.IOException;
import java.util.*;
import java.util.function.BiFunction;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import java.util.stream.StreamSupport;

import static com.ecco.data.client.steps.ReferralStepDefinitions.*;
import static com.ecco.dom.commands.UserAccessAuditLevel.FULL_HISTORY;
import static com.ecco.dto.ChangeViewModel.changeNullTo;
import static java.lang.Boolean.TRUE;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.Matchers.containsInAnyOrder;
import static org.junit.Assert.*;
import static org.springframework.http.HttpStatus.CREATED;

@SuppressWarnings("ConstantConditions")
public class ReferralAPITests extends BaseJsonTest {

    private final UniqueDataService unique = UniqueDataService.instance;
    private final Clock clock = Clock.DEFAULT;
    public final DateTime nowUtc = clock.now();
    private final DateTime nowLondonWithDST = nowUtc.withZone(DateTimeZone.forID("Europe/London"));

    @RequiredArgsConstructor
    public static class Command {

        public static Command from(ReferralData rd) {
            return new Command(rd);
        }

        private final ReferralData rd;


        ReferralTaskScheduleReviewsCommandViewModel scheduleReviews() {
            return new ReferralTaskScheduleReviewsCommandViewModel(rd.referralViewModel.serviceRecipientId,
                    rd.taskHandleFor(SCHEDULE_REVIEWS));
        }

        ReferralTaskAcceptOnServiceCommandViewModel acceptOnService() {
            return new ReferralTaskAcceptOnServiceCommandViewModel(rd.referralViewModel.serviceRecipientId,
                    rd.taskHandleFor(ACCEPT_ON_SERVICE));
        }

        ReferralTaskAppropriateReferralCommandViewModel appropriateReferral() {
            return new ReferralTaskAppropriateReferralCommandViewModel(rd.referralViewModel.serviceRecipientId,
                    rd.taskHandleFor(APPROPRIATE_REFERRAL));
        }

        ReferralTaskExitCommandViewModel close() {
            return new ReferralTaskExitCommandViewModel(rd.referralViewModel.serviceRecipientId,
                    rd.taskHandleFor(EXIT));
        }

        UserAccessAuditCommandDto userAccessAudit(UserAccessAuditLevel level) {
            return new UserAccessAuditCommandDto(rd.referralViewModel.serviceRecipientId,
                    NEEDS_REDUCTION,
                    rd.taskHandleFor(NEEDS_REDUCTION), level);
        }
    }

    private final DateTime now;

    private final LocalDate receivingServiceDate = LocalDate.now().plusDays(2);

    private final ImmutableMap<String, BiFunction<ReferralData, String, ReferralData>> commands;

    private static SessionDataViewModel sdvm;

    public ReferralAPITests() {
        final ImmutableMap.Builder<String, BiFunction<ReferralData, String, ReferralData>> builder = ImmutableMap.builder();
        ReferralStepsWebApiSupport stepDefinitions = new ReferralStepsWebApiSupport(agreementActor, sessionDataActor,
                referralActor, clientActor, contactActor, calendarActor, userActor, listDefActor, serviceActor, cacheActor);
        stepDefinitions.registerStepDefinitions(builder);
        commands = builder.build();
        now = stepDefinitions.nowUtc;
    }

    /**
     JOINing CONFIG
     Joining referrals allow linking files together when there is ambiguity about which to record work on.
     For example, when supporting a family where members each have their own referral (for reporting) and so appear in the search.
     The way the linking is done currently is via the relationship star - showing those members who are eligible in the 'join status'.
     So, in fact, there is no config for joining, but there is for RELATIONSHIP STAR CONFIG, below.

     The UI joins to a referral by setting parentReferralId. (N.B. This does not in
     itself trigger central processing since the ServiceType is not set as a
     childService). When used with 'openParentReferralInstead' on the Service it
     opens the file pointed to.
     This work does not actually rely on the configuration of multiple/family
     services for family members - it picks the ServiceType of the root client and
     looks for matches on it for the client clicked on.
     Code starting location: RelationshipNodeContext.ts
     (see 20160825_rainbow_siblingWork.txt for specifics)

     NB It appears there may have been some config which is no longer obeyed because we can simply use the main file's servicetype.
        TODO remove serviceType.primaryRelationshipId -since RelationshipNodeContext 'join status' doesn't need it
        From FamilySupport_Relationship.pdf:
         restrict 'join's on the primary service (see ..ForSiblings.pdf)
         update servicetype set primaryRelationshipId=<primary servicetypeId>
         ui cache clear
         &
         open primary file
         If wanted, we can set the other service to have
         parameters='{"openParentReferralInstead": "true"}' but only ‘join’ed files take
         part in this, as they set the parentReferralId.
         ui cache clear

     NB copied from FamilySupport_Relationship.pdf - which includes a user guide
     */
    public void configSetupJoining() {
    }

    /**
     * RELATIONSHIP STAR CONFIG
     * on 'accommodation' service
     * NB config from FamilySupport_Relationship.pdf
     */
    @Test
    @Disabled("only for local setup for now")
    public void configSetupRelationship() {
        if (sdvm == null) {
            sdvm = sessionDataActor.getSessionData().getBody();
        }

        // check the existing accommodation servicetype first
        var accomService = StreamSupport.stream(sdvm.services.spliterator(), false)
                .filter(s -> s.name.equalsIgnoreCase(ServiceOptions.ACCOMMODATION.getServiceName()))
                .findFirst().get();
        var accomServiceType = sdvm.serviceTypesById.get(accomService.serviceTypeId.intValue());
        var accomServiceTypeHasRelation = accomServiceType.taskDefinitionEntries.stream()
                .anyMatch(t -> t.name.equalsIgnoreCase("newMultipleReferral"));
        if (accomServiceTypeHasRelation) {
            return;
        }

        // create a relationship service type
        String relationshipTypeName = UniqueDataService.instance.nameFor("relationshipType");
        // NB endFlow means finding next tasks don't complain
        String[] taskNames = new String[]{"newMultipleReferral", "clientWithContact", "referralView", "endFlow"};
        var serviceTypeId = serviceTypeActor.createServiceType(relationshipTypeName, false, taskNames).id;
        // TODO set hideOnList=1, hideOnNew=1
        // TODO ?? set multipleReferrals=1 until its removed with ECCO-1877
        // create a relationship service
        String relationshipServiceName = UniqueDataService.instance.nameFor("relationshipService-all");
        var serviceId = serviceActor.createService(relationshipServiceName, serviceTypeId.longValue()).id;
        // clear the caches
        cacheActor.clearCaches();
        // configure the new servicetype with the serviceId just created (although the current referral's serviceId is assumed)
        // NB 'newMultipleReferral' is 'new party'
        serviceTypeActor.changeTaskSetting(serviceTypeId, "newMultipleReferral", "multipleReferralService", serviceId.toString());


        // configure an existing servicetype to have relationships
        serviceTypeActor.addTaskDefinitionEntry(accomServiceType.id, 0, "newMultipleReferral");
        // configure serviceId just created (otherwise the current referral's serviceId is assumed)
        serviceTypeActor.changeTaskSetting(accomService.serviceTypeId.intValue(), "newMultipleReferral", "multipleReferralService", serviceId.toString());

        // NB To find relations, be sure to set acl permissions
        // NB It may then be desirable to set parameters='{"openParentReferralInstead": "true"}' to open the main file and
        // not the relation, but only joined files currently set the parentReferralId - see 'JOINing config' above.

        /*
         OPTIONAL - different type of relationships - eg siblings, parents etc
             Configure more relationship servicetypes and services as required (possibly with more 'tasks' etc).
             In the existing servicetype's newMultipleReferral, configure multipleReferralService as normal for the default,
             but also add eg 'multipleReferralService_5' for the id of RELATIONSHIPASSOCIATIONTYPE_LISTNAME
         */
    }

    @Test
    public void postClientThenReferral() {
        long clientId = clientActor.createClient("Billy", "Turner");
        createReferral(referralActor, clientId, now);
    }

    @Test
    public void createExternalReferral() {
        String reason = "She needs support around homelessness having been sleeping rough for 6 months";

        InboundReferralResource r = InboundReferralResource.builder()
                .firstName("Ingrid")
                .lastName("Rowley")
                .birthDate(LocalDate.now().minusYears(20))
                .serviceId(ServiceOptions.ACCOMMODATION.getServiceViewModel().getId().longValue())
                .referralReason(reason)
                .agencyName("The referring agency")
                .referrerName("Referrer name")
                .referrerPhoneNumber("+44 7890 123456")
                .referrerEmail("<EMAIL>")
                .build();
        InboundReferralParams p = new InboundReferralParams();
        p.setDto(r);
        ResponseEntity<Result> responseEntity = referralActor.createInboundReferral(p);
        assertThat(responseEntity.getStatusCode()).isEqualTo(CREATED);
        long rId = Long.parseLong(responseEntity.getBody().getId());
        ResponseEntity<ReferralViewModel> referral = referralActor.getReferralById(rId);
        assertThat(referral.getBody().clientLastName).isEqualTo("Rowley");
        assertThat(referral.getBody().referralReason).isEqualTo(reason);
        assertThat(referral.getBody().sourceAgency.companyName).isEqualTo("The referring agency");

        assertThat(referral.getBody().referrerIndividualId).isNotNull();
        IndividualViewModel person = contactActor.getContactById(referral.getBody().referrerIndividualId).getBody();
        assertThat(person.firstName).isEqualTo("Referrer");
        assertThat(person.lastName).isEqualTo("name");
        assertThat(person.phoneNumber).isEqualTo("+44 7890 123456");
        assertThat(person.email).isEqualTo("<EMAIL>");
        assertThat(person.organisationId).isEqualTo(referral.getBody().referrerAgencyId);
    }

    @Test
    public void postReferralAggregate() {

        String firstName = "Dup";
        String lastName = "Licate";
        // LocalDate dob = new LocalDate(1995, 11, 7);
        LocalDate referralDate = LocalDate.now().minusDays(12);

        //noinspection deprecation
        referralActor.createReferralAsStarted(firstName, lastName, referralDate, ServiceOptions.ACCOMMODATION);
    }

    @Test
    public void createReferral_cannotAcceptedAndSignpostTogether() {
        long clientId = clientActor.createClient("Bobby", "Otter");
        // create a referral using commands, on accommodation service
        long rId = createReferral(referralActor, clientId, now);
        ReferralViewModel rvm = referralActor.getReferralById(rId).getBody();

        // get the full service we are testing, so we get the projects too
        com.ecco.dto.ServiceViewModel service = serviceActor.getAllServicesWithProjects().getBody().
                services.stream().filter(svm -> svm.id.equals(ServiceOptions.ACCOMMODATION.getServiceViewModel().id)).findFirst().get();
        ServiceOptions options = new ServiceOptions(service);
        ReferralData referralData = new ReferralData(rvm, options, null, workflowActor.getTasksByTaskName(rvm));


        // ACCEPT
        LocalDate acceptedDate = nowLondonWithDST.minusDays(1).toLocalDate();
        ReferralTaskAcceptOnServiceCommandViewModel cmd = new ReferralTaskAcceptOnServiceCommandViewModel(
                referralData.referralViewModel.serviceRecipientId,
                referralData.taskHandleFor(ACCEPT_ON_SERVICE));
        cmd.acceptedState = changeNullTo(AcceptState.ACCEPTED);
        cmd.acceptedDate = changeNullTo(acceptedDate);

        // SIGNPOST
        final ListDefinitionEntryViewModel signpostReason = getFirstSignpostReason();
        cmd.signpostedReason = changeNullTo(signpostReason.id);

        // TEST
        referralData = executeCmdAndGetResult(cmd, referralData);
        assertThat(referralData.referralViewModel.appropriateReferralState).isEqualByComparingTo(AcceptState.ACCEPTED); // assumes was previously accepted
        assertThat(referralData.referralViewModel.acceptOnServiceState).isEqualByComparingTo(AcceptState.ACCEPTED);
        assertThat(referralData.referralViewModel.signpostedBack).isFalse();
        assertThat(referralData.referralViewModel.signpostedReasonId).isNull();
        assertThat(referralData.referralViewModel.signpostedCommentId).isNull();
        assertThat(referralData.referralViewModel.signpostedAgencyId).isNull();
        assertThat(referralData.referralViewModel.exitedDate).isNull();
        assertThat(referralData.referralViewModel.decisionMadeOn).isEqualByComparingTo(acceptedDate); // TODO: clarify. timezone/BST probable issue
    }

    @Test
    public void createReferral_cannotSignpostAndBeLive() {
        long clientId = clientActor.createClient("Jimmy", "Liver");
        // create a referral using commands, on accommodation service
        long rId = createReferral(referralActor, clientId, now);
        ReferralViewModel rvm = referralActor.getReferralById(rId).getBody();

        // get the full service we are testing, so we get the projects too
        com.ecco.dto.ServiceViewModel service = serviceActor.getAllServicesWithProjects().getBody().
                services.stream().filter(svm -> svm.id.equals(ServiceOptions.ACCOMMODATION.getServiceViewModel().id)).findFirst().get();
        ServiceOptions options = new ServiceOptions(service);
        ReferralData referralData = new ReferralData(rvm, options, null, workflowActor.getTasksByTaskName(rvm));

        // SIGNPOST at appropriate referral WITHOUT any comment or reason, but perhaps 'signposted back tickbox'
        LocalDate acceptedDate = nowLondonWithDST.minusDays(1).toLocalDate();
        ReferralTaskAcceptOnServiceCommandViewModel cmd = new ReferralTaskAcceptOnServiceCommandViewModel(
                referralData.referralViewModel.serviceRecipientId,
                referralData.taskHandleFor(ACCEPT_ON_SERVICE));
        cmd.acceptedState = changeNullTo(AcceptState.SIGNPOSTED);
        cmd.signpostedBack = changeNullTo(TRUE);
        cmd.acceptedDate = changeNullTo(acceptedDate);

        // TEST individual referral
        referralData = executeCmdAndGetResult(cmd, referralData);
        assertThat(referralData.referralViewModel.appropriateReferralState).isEqualByComparingTo(AcceptState.UNSET);
        assertThat(referralData.referralViewModel.acceptOnServiceState).isEqualByComparingTo(AcceptState.SIGNPOSTED);
        assertThat(referralData.referralViewModel.signpostedBack).isTrue();
        assertThat(referralData.referralViewModel.signpostedReasonId).isNull();
        assertThat(referralData.referralViewModel.signpostedCommentId).isNull();
        assertThat(referralData.referralViewModel.signpostedAgencyId).isNull();
        assertThat(referralData.referralViewModel.decisionMadeOn).isEqualByComparingTo(acceptedDate); // TODO: clarify. timezone/BST probable issue

        // TEST list referrals
        ReportCriteriaDto dto = new ReportCriteriaDto();
        dto.setReferralStatus(ReferralStatusName.Signposted.toString());
        ResponseEntity<ReferralViewModel[]> responseReport = reportActor.getReportReferrals(dto, 0);
        org.junit.Assert.assertThat(responseReport.getStatusCode(), is(HttpStatus.OK));
        assertNotNull(responseReport.getBody());
        final ReferralData finalReferralData = referralData;
        assertTrue(Arrays.stream(responseReport.getBody()).anyMatch(r -> r.serviceRecipientId.equals(finalReferralData.referralViewModel.serviceRecipientId)));
    }

    @Test
    public void createReferral_startOnServiceIsAccepted() {

        // CREATE SUPPORT WORKER (needs to be user for assigning tasks - see determineBestUserForTask)
        var workerIndividual1 = userActor.createIndividualWithUser(unique.userNameFor("wkr-start-acc1"), "password", unique.firstNameFor("Start"), unique.lastNameFor("Accepted1"), null);
        var workerIndividual2 = userActor.createIndividualWithUser(unique.userNameFor("wkr-start-acc2"), "password", unique.firstNameFor("Start"), unique.lastNameFor("Accepted2"), null);

        // CREATE CLIENT
        long clientId = clientActor.createClient("Start", "Accepted");
        // create a referral using commands, on accommodation service
        long rId = createReferral(referralActor, clientId, now);
        ReferralViewModel rvm = referralActor.getReferralById(rId).getBody();

        // get the full service we are testing, so we get the projects too
        com.ecco.dto.ServiceViewModel service = serviceActor.getAllServicesWithProjects().getBody().
                services.stream().filter(svm -> svm.id.equals(ServiceOptions.ACCOMMODATION.getServiceViewModel().id)).findFirst().get();
        ServiceOptions options = new ServiceOptions(service);
        ReferralData referralData = new ReferralData(rvm, options, null, workflowActor.getTasksByTaskName(rvm));

        // TEST next task, details of referral, is available after a client is created (but there are no allowNext/schedules due dates)
        var tasks = taskStatusActor.getTasksByServiceRecipient(rvm.serviceRecipientId).getBody();
        assertThat(tasks.length).isEqualTo(1);
        assertThat(Arrays.stream(tasks).filter(t -> t.dueDate != null).toList().size()).isEqualTo(0);

        // THEN START ON SERVICE
        ReferralTaskEditStartOnServiceCommandViewModel cmd = new ReferralTaskEditStartOnServiceCommandViewModel(rvm.serviceRecipientId, LinearWorkflowService.toHandle(rvm.serviceRecipientId, Integer.valueOf(TaskDefinitionNameIdMappings.Task.START_ON_SERVICE_ACCOMM.taskDefinitionId).longValue()).toString());
        cmd.receivingServiceDate = changeNullTo(receivingServiceDate);
        cmd.allocatedWorkerContactId = changeNullTo(workerIndividual1.id);

        // TEST START ON SERVICE
        referralData = executeCmdAndGetResult(cmd, referralData);
        assertThat(referralData.referralViewModel.appropriateReferralState).isEqualByComparingTo(AcceptState.ACCEPTED); // assumes was previously accepted
        assertThat(referralData.referralViewModel.acceptOnServiceState).isEqualByComparingTo(AcceptState.ACCEPTED);
        assertThat(referralData.referralViewModel.signpostedBack).isFalse();
        assertThat(referralData.referralViewModel.signpostedReasonId).isNull();
        assertThat(referralData.referralViewModel.signpostedCommentId).isNull();
        assertThat(referralData.referralViewModel.signpostedAgencyId).isNull();
        // decisionMadeOn is a system/utc date - see ReferralStatusCommonPredicates#checkTzAssumptions and ReferralTaskAppropriateReferralCommandHandler
        // and the view model to local date - ReferralSummaryToViewModel and ReferralToViewModel
        assertThat(referralData.referralViewModel.decisionMadeOn).isEqualByComparingTo(DateTimeUtils.convertFromUtcToUsersLocalDate(new DateTime()));

        // TEST needsReduction and needsAssessmentReductionReview tasks are available after start, and assigned
        // along with the pre-existing referralDetails and allowNext scheduleReviews, exit-sp_data
        tasks = taskStatusActor.getTasksByServiceRecipient(rvm.serviceRecipientId).getBody();
        assertThat(tasks.length).isEqualTo(5);
        // the 2 new tasks are assigned, but the 3 existing tasks are not assigned as not due - see "DEV-2524 Transfer tasks when setting first worker"
        assertThat(Arrays.stream(tasks).filter(t -> t.assignee == null).toList().size()).isEqualTo(3);
        assertThat(Arrays.stream(tasks).filter(t -> t.assignee != null).toList().size()).isEqualTo(2);
        assertThat(Arrays.stream(tasks).filter(t -> t.assignee != null).map(t -> t.assignee)).containsOnly(workerIndividual1.userId, workerIndividual1.userId);

        // THEN CHANGE WORKER
        // NB new cmd else same uuid is skipped
        cmd = new ReferralTaskEditStartOnServiceCommandViewModel(rvm.serviceRecipientId, LinearWorkflowService.toHandle(rvm.serviceRecipientId, Integer.valueOf(TaskDefinitionNameIdMappings.Task.START_ON_SERVICE_ACCOMM.taskDefinitionId).longValue()).toString());
        cmd.allocatedWorkerContactId = ChangeViewModel.create(workerIndividual1.id, workerIndividual2.id);
        executeCmdAndGetResult(cmd, referralData);
        // TEST
        tasks = taskStatusActor.getTasksByServiceRecipient(rvm.serviceRecipientId).getBody();
        assertThat(tasks.length).isEqualTo(5);
        assertThat(Arrays.stream(tasks).filter(t -> t.assignee == null).toList().size()).isEqualTo(3);
        assertThat(Arrays.stream(tasks).filter(t -> t.assignee != null).toList().size()).isEqualTo(2);
        assertThat(Arrays.stream(tasks).filter(t -> t.assignee != null).map(t -> t.assignee)).containsOnly(workerIndividual2.userId, workerIndividual2.userId);
    }

    /** This tests a workflow on the demo all service.  We don't submit any data for needs assessment as it
     * is already done */
    @Test
    public void canCreateActivitiBasedReferralAndUseCommandsToProcessIt() {

        // TODO: Consider how this relates to ReferralSteps?? We could have an
        // impl that talks API for those
        ReferralViewModel rvm = referralActor.createMinimalReferralAndClient(null, "Mini", "Driver", ServiceOptions.DEMO_ALL);

        // === Source (from) ===
        // NOTE: No claim/completeTask, as task is already completed as part of referral wizard
        ReferralData referralData = new ReferralData(rvm, null, null, workflowActor.getTasksByTaskName(rvm));

        // complete a step without tasks - mimic the referral wizard
        referralData = commands.get(SOURCE_OF_REFERRAL).apply(referralData, referralData.taskHandleFor(SOURCE_OF_REFERRAL));
        // NB we're testing DEMO_ALL here, and demo-all-workflow doesn't have 'destination of referral'
        // otherwise we could use the DESTINATION step, and provide a ServiceOptions with projects in ServiceViewModel
        //referralData = commands.get(DESTINATION).apply(referralData);

        // test the steps at this point
        Map<String, WorkflowTaskViewModel> map = getTasksAssertingCompleteAvailableIncomplete(rvm,
                new String[] {SOURCE_OF_REFERRAL},
                new String[] {DATA_PROTECTION},
                new String[] {DATA_PROTECTION, EMERGENCY_DETAILS, REFERRAL_DETAILS, PENDING_STATUS,
                        DELIVERED_BY, APPROPRIATE_REFERRAL, SETUP_INITIAL_ASSESSMENT, NEEDS_ASSESSMENT, ACCEPT_ON_SERVICE, FUNDING,
                        THREAT_ASSESSMENT_REDUCTION, START, REFERRAL_ACTIVITIES, AGREEMENT_OF_APPOINTMENTS, CASE_NOTES,
                        GENERALQUESTIONNAIRE_OUTCOMESTAR, ROTA_VISIT, NEEDS_REDUCTION, SCHEDULE_REVIEWS, NEEDS_ASSESSMENT_REDUCTION_REVIEW, EXIT});

        // === Data Protection ===
        testTaskCommand(referralData, map, DATA_PROTECTION);

        // test the steps at this point
        map = getTasksAssertingCompleteAvailableIncomplete(rvm,
                new String[] {SOURCE_OF_REFERRAL, DATA_PROTECTION},
                new String[] {EMERGENCY_DETAILS},
                new String[] {EMERGENCY_DETAILS, REFERRAL_DETAILS, PENDING_STATUS,
                DELIVERED_BY, APPROPRIATE_REFERRAL, SETUP_INITIAL_ASSESSMENT, NEEDS_ASSESSMENT, ACCEPT_ON_SERVICE, FUNDING,
                THREAT_ASSESSMENT_REDUCTION, START, REFERRAL_ACTIVITIES, AGREEMENT_OF_APPOINTMENTS, CASE_NOTES,
                GENERALQUESTIONNAIRE_OUTCOMESTAR, ROTA_VISIT, NEEDS_REDUCTION, SCHEDULE_REVIEWS, NEEDS_ASSESSMENT_REDUCTION_REVIEW, EXIT});

        // complete the rest of the steps
        map = testTaskCommand(referralData, map, EMERGENCY_DETAILS);
        map = testTaskCommand(referralData, map, REFERRAL_DETAILS);
        map = testTaskCommand(referralData, map, PENDING_STATUS);
        map = testTaskCommand(referralData, map, DELIVERED_BY);
        map = testTaskCommand(referralData, map, APPROPRIATE_REFERRAL, this::submitAndCheckAppropriateReferralCommand);
        map = testTaskCommand(referralData, map, SETUP_INITIAL_ASSESSMENT);
        map = testTaskCommand(referralData, map, NEEDS_ASSESSMENT, this::dummyCommand);
        map = testTaskCommand(referralData, map, ACCEPT_ON_SERVICE, this::submitAndCheckAcceptOnServiceCommand);
        map = testTaskCommand(referralData, map, FUNDING);
        map = testTaskCommand(referralData, map, THREAT_ASSESSMENT_REDUCTION, this::dummyCommand); // Evidence screen so leave as dummy
        map = testTaskCommand(referralData, map, START);
        map = testTaskCommand(referralData, map, REFERRAL_ACTIVITIES, this::dummyCommand);
        map = testTaskCommand(referralData, map, AGREEMENT_OF_APPOINTMENTS, this::dummyCommand);
        map = testTaskCommand(referralData, map, CASE_NOTES, this::dummyCommand);
        map = testTaskCommand(referralData, map, GENERALQUESTIONNAIRE_OUTCOMESTAR, this::dummyCommand);
        map = testTaskCommand(referralData, map, ROTA_VISIT, this::dummyCommand);
        map = testTaskCommand(referralData, map, NEEDS_REDUCTION, this::dummyCommand); // Evidence screen tested elsewhere so leave as dummy
        map = testTaskCommand(referralData, map, SCHEDULE_REVIEWS, this::submitAndCheckScheduleReviews);
        map = testTaskCommand(referralData, map, NEEDS_ASSESSMENT_REDUCTION_REVIEW, this::dummyCommand);

        // FINALLY, everything should be complete
        testTaskCommand(referralData, map, EXIT, this::dummyCommand);
        getTasksAssertingCompleteAvailableIncomplete(rvm,
                new String[] {SOURCE_OF_REFERRAL, DATA_PROTECTION, EMERGENCY_DETAILS, REFERRAL_DETAILS, PENDING_STATUS,
                        DELIVERED_BY, APPROPRIATE_REFERRAL, SETUP_INITIAL_ASSESSMENT, NEEDS_ASSESSMENT, ACCEPT_ON_SERVICE, FUNDING,
                        THREAT_ASSESSMENT_REDUCTION, START, REFERRAL_ACTIVITIES, AGREEMENT_OF_APPOINTMENTS, CASE_NOTES,
                        GENERALQUESTIONNAIRE_OUTCOMESTAR, ROTA_VISIT, NEEDS_REDUCTION, SCHEDULE_REVIEWS, NEEDS_ASSESSMENT_REDUCTION_REVIEW, EXIT},
                new String[] {},
                new String[] {});
    }


    @Test
    public void canCreateLinearWorkflowReferralAndUseCommandsToProcessIt() throws IOException {

        // impl that talks API for those
        String firstNameUnique = unique.clientFirstNameFor("Haus");
        String lastNameUnique = unique.clientLastNameFor("Needed");

        // get the full service we are testing, so we get the projects too
        int serviceId = ServiceOptions.ACCOMMODATION.getServiceViewModel().id;
        com.ecco.dto.ServiceViewModel service = serviceActor.getAllServicesWithProjects().getBody().
                services.stream().filter(svm -> svm.id == serviceId).findFirst().get();
        ServiceOptions options = new ServiceOptions(service);

        ReferralViewModel rvm = referralActor.createMinimalReferralAndClient(null, firstNameUnique, lastNameUnique, options);

        // === Source (from) ===
        // NOTE: No claim/completeTask, as task is already completed as part of referral wizard
        ReferralData referralData = new ReferralData(rvm, options, null, workflowActor.getTasksByTaskName(rvm));

        var map = getTasksAssertingCompleteAvailableIncompleteUnavailable(rvm,
                new String[] {CLIENT_WITH_CONTACT, DESTINATION, SOURCE_OF_REFERRAL, REFERRAL_VIEW},
                new String[] {REFERRAL_DETAILS, ACCEPT_ON_SERVICE},
                new String[] {REFERRAL_DETAILS, PENDING_STATUS,
                        APPROPRIATE_REFERRAL, ACCOMMODATION, SETUP_INITIAL_ASSESSMENT, INITIAL_SP_DATA,
                        NEEDS_ASSESSMENT, ACCEPT_ON_SERVICE, THREAT_ASSESSMENT_REDUCTION, START_ACCOMMODATION,
                        NEEDS_REDUCTION, SCHEDULE_REVIEWS, NEEDS_ASSESSMENT_REDUCTION_REVIEW, EXIT_SP_DATA, EXIT, END},
                new String[] {PENDING_STATUS,
                        APPROPRIATE_REFERRAL, ACCOMMODATION, SETUP_INITIAL_ASSESSMENT, INITIAL_SP_DATA,
                        NEEDS_ASSESSMENT, THREAT_ASSESSMENT_REDUCTION, START_ACCOMMODATION,
                        NEEDS_REDUCTION, SCHEDULE_REVIEWS, NEEDS_ASSESSMENT_REDUCTION_REVIEW, EXIT_SP_DATA, EXIT, END}
        );

        // REFERRAL_DETAILS & ACCEPT_ON_SERVICE are available but without a due date
        assertThat(referralData.tasks.get(REFERRAL_DETAILS).dueDate).isNull();
        assertThat(referralData.tasks.get(ACCEPT_ON_SERVICE).dueDate).isNull();

        // Execute a command with a task claim/complete
        map = testTaskCommand(referralData, map, SOURCE_OF_REFERRAL);
        map = testTaskCommand(referralData, map, DESTINATION);
        // NB Execute a command without a task - mimic the referral wizard
        // referralData = commands.get(SOURCE_OF_REFERRAL).apply(referralData, referralData.taskHandleFor(SOURCE_OF_REFERRAL));

        // test the steps at this point
        map = getTasksAssertingCompleteAvailableIncomplete(rvm,
                new String[] {CLIENT_WITH_CONTACT, DESTINATION, SOURCE_OF_REFERRAL, REFERRAL_VIEW},
                new String[] {REFERRAL_DETAILS, ACCEPT_ON_SERVICE},
                new String[] {REFERRAL_DETAILS, PENDING_STATUS,
                        APPROPRIATE_REFERRAL, ACCOMMODATION, SETUP_INITIAL_ASSESSMENT, INITIAL_SP_DATA,
                        NEEDS_ASSESSMENT, ACCEPT_ON_SERVICE, THREAT_ASSESSMENT_REDUCTION, START_ACCOMMODATION,
                        NEEDS_REDUCTION, SCHEDULE_REVIEWS, NEEDS_ASSESSMENT_REDUCTION_REVIEW, EXIT_SP_DATA, EXIT, END});

        // === Referral details should move things on ===
        map = testTaskCommand(referralData, map, REFERRAL_DETAILS);

        // test the steps at this point
        map = getTasksAssertingCompleteAvailableIncomplete(rvm,
                new String[] {CLIENT_WITH_CONTACT, DESTINATION, SOURCE_OF_REFERRAL, REFERRAL_VIEW, REFERRAL_DETAILS},
                new String[] {PENDING_STATUS, APPROPRIATE_REFERRAL, ACCEPT_ON_SERVICE},
                new String[] {PENDING_STATUS,
                        APPROPRIATE_REFERRAL, ACCOMMODATION, SETUP_INITIAL_ASSESSMENT, INITIAL_SP_DATA,
                        NEEDS_ASSESSMENT, ACCEPT_ON_SERVICE, THREAT_ASSESSMENT_REDUCTION, START_ACCOMMODATION,
                        NEEDS_REDUCTION, SCHEDULE_REVIEWS, NEEDS_ASSESSMENT_REDUCTION_REVIEW, EXIT_SP_DATA, EXIT, END});

        // complete the rest of the steps
        map = testTaskCommand(referralData, map, PENDING_STATUS);
        // NB this actually signposts then accepts - and signposting closes tasks
        map = testTaskCommand(referralData, map, APPROPRIATE_REFERRAL, this::submitAndCheckAppropriateReferralCommand);

        // verify incomplete interview task 'assessmentDate' has a due date
        // we had to wait for referralAccept as allowNext is false, but accommodation after is allowNext true
        var actualAssessment = map.get(SETUP_INITIAL_ASSESSMENT).dueDate;
        var expectedAssessment = TaskStatus.calculateNextDueDate("7d,end", java.time.LocalDate.now());
        Assertions.assertThat(actualAssessment).isEqualTo(expectedAssessment);

        map = testTaskCommand(referralData, map, SETUP_INITIAL_ASSESSMENT); // see ReferralStepsWebApiSupport#validateInitialAssessment

        // verify incomplete accept on service task is due when expected (from the config of 9 days)
        // NB this has been 'available' for a while, but not got a due date until it was the next SLA
        var actualAccept = map.get(ACCEPT_ON_SERVICE).dueDate;
        var expectedAccept = TaskStatus.calculateNextDueDate("9d,end", java.time.LocalDate.now());
        Assertions.assertThat(actualAccept).isEqualTo(expectedAccept);

        map = testTaskCommand(referralData, map, NEEDS_ASSESSMENT, this::dummyCommand);
        // NB this actually signposts then accepts - and signposting closes tasks
        map = testTaskCommand(referralData, map, ACCEPT_ON_SERVICE, this::submitAndCheckAcceptOnServiceCommand);
        map = testTaskCommand(referralData, map, THREAT_ASSESSMENT_REDUCTION, this::dummyCommand); // Evidence screen so leave as dummy

        // Once START_ACCOMMODATION is complete, these are available via allowNext:
        //      SCHEDULE_REVIEWS / NEEDS_REDUCTION / NEEDS_ASSESSMENT_REDUCTION_REVIEW
        // of which, NEEDS_REDUCTION and NEEDS_ASSESSMENT_REDUCTION_REVIEW have dueDateSchedule's
        map = testTaskCommand(referralData, map, START_ACCOMMODATION);
        // NB 'submitAndCheck's above actually signposts then accepts - and signposting closes tasks, so 'ACCOMMODATION, INITIAL_SP_DATA' are complete
        getTasksAssertingCompleteAvailableIncomplete(rvm,
                new String[] {CLIENT_WITH_CONTACT, DESTINATION, SOURCE_OF_REFERRAL, REFERRAL_VIEW,
                        REFERRAL_DETAILS, PENDING_STATUS, APPROPRIATE_REFERRAL, SETUP_INITIAL_ASSESSMENT,
                        NEEDS_ASSESSMENT, ACCEPT_ON_SERVICE, THREAT_ASSESSMENT_REDUCTION, START_ACCOMMODATION,
                        ACCOMMODATION, INITIAL_SP_DATA},
                new String[] {EXIT, EXIT_SP_DATA, NEEDS_REDUCTION, SCHEDULE_REVIEWS, NEEDS_ASSESSMENT_REDUCTION_REVIEW},
                new String[] {EXIT, END, EXIT_SP_DATA, NEEDS_REDUCTION, SCHEDULE_REVIEWS, NEEDS_ASSESSMENT_REDUCTION_REVIEW});

        // verify incomplete review task is due when expected (from the config of 3 months)
        // NB this has been 'available' for a while, but not got a due date until it was the next SLA
        var actualReview = map.get(NEEDS_ASSESSMENT_REDUCTION_REVIEW).dueDate;
        var expectedReview = TaskStatus.calculateNextDueDate("3m", java.time.LocalDate.now());
        Assertions.assertThat(actualReview).isEqualTo(expectedReview);
        // verify the review task
        var allTasks = taskStatusActor.getTasksByServiceRecipient(referralData.referralViewModel.serviceRecipientId).getBody();
        var reviewTasks = Arrays.stream(allTasks).filter(t -> t.taskDefinitionId == 54L).collect(Collectors.toList());
        Assertions.assertThat(reviewTasks.size()).isEqualTo(1);

        map = testTaskCommand(referralData, map, NEEDS_REDUCTION, this::dummyCommand); // Evidence screen tested elsewhere so leave as dummy
        map = testTaskCommand(referralData, map, SCHEDULE_REVIEWS, this::submitAndCheckScheduleReviews);
        // complete the review
        map = testTaskCommand(referralData, map, NEEDS_ASSESSMENT_REDUCTION_REVIEW, this::dummyCommand);
        // NB this triggers a close of the tasks
        map = testTaskCommand(referralData, map, EXIT, this::submitAndCheckCloseCommand);

        // FINALLY, everything should be complete, with CLOSE completed and END available
        // and NEEDS_ASSESSMENT_REDUCTION_REVIEW available as a new date is setup
        getTasksAssertingCompleteAvailableIncomplete(rvm,
                new String[] {CLIENT_WITH_CONTACT, DESTINATION, SOURCE_OF_REFERRAL, REFERRAL_VIEW,
                        REFERRAL_DETAILS, PENDING_STATUS,
                        APPROPRIATE_REFERRAL, SETUP_INITIAL_ASSESSMENT,
                        NEEDS_ASSESSMENT, ACCEPT_ON_SERVICE, THREAT_ASSESSMENT_REDUCTION, START_ACCOMMODATION,
                        NEEDS_REDUCTION, SCHEDULE_REVIEWS, EXIT,
                        END, NEEDS_ASSESSMENT_REDUCTION_REVIEW, ACCOMMODATION, INITIAL_SP_DATA, EXIT_SP_DATA},
                new String[] {},
                new String[] {});

        validateSummaryMatches(referralActor.getReferralById(rvm.referralId).getBody(),
                referralActor.getReferralSummaryById(rvm.referralId).getBody());

        // verify the tasks are closed since we executed CLOSE, although its still available else we can't re-edit things
        Assertions.assertThat(map.get(NEEDS_ASSESSMENT_REDUCTION_REVIEW).isCompleted).isTrue();
        Assertions.assertThat(map.get(NEEDS_ASSESSMENT_REDUCTION_REVIEW).isAvailable).isTrue();

        // verify there is a second review, and that this created > previous created (higher wins in getWorkflowTasks - see getLatest)
        var allTasksAgain = taskStatusActor.getTasksByServiceRecipient(referralData.referralViewModel.serviceRecipientId).getBody();
        var reviewTasksAgain = Arrays.stream(allTasksAgain).filter(t -> t.taskDefinitionId == 54L).collect(Collectors.toList());
        Assertions.assertThat(reviewTasksAgain.size()).isEqualTo(2);
        var reviewTaskFirst = reviewTasks.get(0);
        var reviewTaskSecond = reviewTasksAgain.stream().filter(t -> !t.taskInstanceUuid.equals(reviewTaskFirst.taskInstanceUuid)).findFirst().get();
        Assertions.assertThat(reviewTaskSecond.taskInstanceUuid).isNotEqualTo(reviewTaskFirst.taskInstanceUuid);
        Assertions.assertThat(reviewTaskSecond.created).isAfter(reviewTaskFirst.created);

        // verify the next review dueDate
        var actualReview2 = map.get(NEEDS_ASSESSMENT_REDUCTION_REVIEW).dueDate;
        var expectedReview2 = TaskStatus.calculateNextDueDate("3m", java.time.LocalDate.now(), null, java.time.LocalDate.now().plusMonths(3));
        Assertions.assertThat(actualReview2).isEqualTo(expectedReview2);

        //=== Pretend we looked at the full support history twice ===
        UserAccessAuditCommandDto cmd = Command.from(referralData).userAccessAudit(FULL_HISTORY);
        var result = referralActor.executeCommand(cmd);
        Assert.state(result.getStatusCode().is2xxSuccessful());
        cmd = Command.from(referralData).userAccessAudit(FULL_HISTORY); // Do twice
        result = referralActor.executeCommand(cmd);
        Assert.state(result.getStatusCode().is2xxSuccessful());

        var audits = serviceRecipientActor.findCommands(rvm.serviceRecipientId).getBody();
        Assertions.assertThat((audits.get(0).get("commandUri").toString()).endsWith("audit")).isTrue();
        Assertions.assertThat((audits.get(1).get("commandUri").toString()).endsWith("audit")).isFalse();
    }

    // see ServiceTypeChangeCommandAPITests canAddWithOutcome
    @Test
    public void createReferral_completeRecurringTask() throws InterruptedException {

        // GIVEN new service type with 'threat' timer for every other day
        var name = UniqueDataService.instance.nameFor("serviceTypeTimer");
        var serviceType = serviceTypeActor.createServiceTypeWithBasicWorkflow(name);
        // add 'threatAssessmentReduction' with
        var taskDefCmd = new TaskDefinitionEntryCommandViewModel(TaskDefinitionEntryCommandViewModel.OPERATION_ADD, serviceType.id, EvidenceTask.THREAT_ASSESSMENT_REDUCTION.getTaskName());
        taskDefCmd.dueDateScheduleChange = ChangeViewModel.changeNullTo("2d");
        taskDefCmd.orderbyChange = ChangeViewModel.changeNullTo(12);
        commandActor.executeCommand(taskDefCmd);
        serviceActor.createService(name, serviceType.id.longValue());

        // GIVEN referral on new service, which has threatAssessmentReduction every 2d
        long clientId = clientActor.createClient("Recurring", "Task");
        var service = serviceActor.getAllServicesWithProjects().getBody().services.stream()
                .filter(s -> s.getName().equalsIgnoreCase(name)).findFirst().get();
        var serviceOption = new ServiceOptions(service);
        long rId = createReferral(referralActor, clientId, now, serviceOption);

        // GIVEN accept is done, threatAssessmentReduction will have a due date in 2d
        ReferralViewModel rvm = referralActor.getReferralById(rId).getBody();
        Thread.sleep(1000); // because testing against MySQL we can get dirty reads, and clearing caches takes time (not a synchronous operation)
        ReferralData referralData = new ReferralData(rvm, serviceOption, null, workflowActor.getTasksByTaskName(rvm));
        Map<String, WorkflowTaskViewModel> map = workflowActor.getTasksByTaskName(rvm);
        var task = map.get(THREAT_ASSESSMENT_REDUCTION);
        Assertions.assertThat(task.dueDate.toLocalDate()).isEqualTo(java.time.LocalDate.now().plusDays(2));

        /*
        // fudge the dueDate to be before today, as this tests that we go after today/complete date
        // we can't really fudge the 'created' date when the schedule starts from though
        ReferralTaskStatusCommandViewModel update = new ReferralTaskStatusCommandViewModel(BaseCommandViewModel.OPERATION_UPDATE, task.taskHandle, rvm.serviceRecipientId);
        update.dueDate = ChangeViewModel.create(null, LocalDateTime.now().minusWeeks(2));
        var taskInstanceUuid = LinearWorkflowService.getTaskInstanceId(WorkflowTask.Handle.fromString(task.taskHandle));
        update.taskInstanceId = taskInstanceUuid.toString();
        commandActor.executeCommand(update);
        */

        referralData = acceptOnService(referralData);
        map = getTasksAssertingCompleteAvailableIncomplete(rvm,
                new String[] {CLIENT_WITH_CONTACT, REFERRAL_VIEW},
                new String[] {EXIT, ACCEPT_ON_SERVICE, THREAT_ASSESSMENT_REDUCTION},
                new String[] {EXIT, ACCEPT_ON_SERVICE, THREAT_ASSESSMENT_REDUCTION, END});
        // verify an old due date, before we then complete it
        var due = map.get(THREAT_ASSESSMENT_REDUCTION).dueDate;
        //Assertions.assertThat(due.toLocalDate()).isEqualTo(update.dueDate.to.toLocalDate());

        // THEN when complete the threatAssessmentReduction, the next is set for 4 days time (2d + 2)
        map = testTaskCommand(referralData, map, THREAT_ASSESSMENT_REDUCTION, this::dummyCommand); // Evidence screen so leave as dummy

        // verify incomplete interview task 'assessmentDate' has a due date
        // we had to wait for referralAccept as allowNext is false, but accommodation after is allowNext true
        var actualDue = map.get(THREAT_ASSESSMENT_REDUCTION).dueDate;
        var expectedDue = java.time.LocalDate.now().plusDays(4);
        Assertions.assertThat(actualDue.toLocalDate()).isEqualTo(expectedDue);
    }

    private void validateSummaryMatches(ReferralViewModel rvm, ReferralSummaryViewModel rsvm) throws IOException {
        ObjectMapper mapper = ConvertersConfig.getObjectMapper();
        mapper.enable(SerializationFeature.INDENT_OUTPUT);

        // FIXME: Will use above when we really get RSVM not RVM passed as RSVM
        ReferralSummaryViewModel rsvmSubset = convertToRSVM(rsvm, mapper);
        ReferralSummaryViewModel rvmSubset = convertToRSVM(rvm, mapper);
        assertThat(mapper.writeValueAsString(rsvmSubset)).isEqualTo(mapper.writeValueAsString(rvmSubset));
    }

    private ReferralSummaryViewModel convertToRSVM(ReferralSummaryViewModel rsvm, ObjectMapper mapper) throws IOException {
        String json = mapper.writeValueAsString(rsvm);
        return mapper.readValue(json, ReferralSummaryViewModel.class);
    }

    private Map<String, WorkflowTaskViewModel> getTasksAssertingCompleteAvailableIncomplete(ReferralViewModel rvm,
                                                                                            String[] complete,
                                                                                            String[] available,
                                                                                            String[] incomplete) {
        return getTasksAssertingCompleteAvailableIncompleteUnavailable(rvm, complete, available, incomplete, null);
    }

    private Map<String, WorkflowTaskViewModel> getTasksAssertingCompleteAvailableIncompleteUnavailable(ReferralViewModel rvm,
                                                                                                       String[] complete,
                                                                                                       String[] available,
                                                                                                       String[] incomplete,
                                                                                                       String[] unavailable) {
        Map<String, WorkflowTaskViewModel> map = workflowActor.getTasksByTaskName(rvm);
        Stream<String> completedTasks = map.values().stream().filter(task -> task.isCompleted).map(task -> task.taskName);

        Stream<String> availableTasks = map.values().stream().filter(task -> task.isAvailable && !task.isCompleted)
                .map(task -> task.taskName);
        Stream<String> incompleteTasks = map.values().stream().filter(task -> !task.isCompleted).map(task -> task.taskName);
        Stream<String> unavailableTasks = map.values().stream().filter(task -> !task.isAvailable).map(task -> task.taskName);
        assertThat(completedTasks).containsOnly(complete);
        assertThat(availableTasks).containsOnly(available);
        assertThat(incompleteTasks).containsOnly(incomplete);
        if (unavailable != null) {
            assertThat(unavailableTasks).containsOnly(unavailable);
        }
        return map;
    }

    /**
     * Claim task, do commandOperation and mark task as complete and then...
     * @return new task status having completed this step
     */
    private Map<String, WorkflowTaskViewModel> testTaskCommand(ReferralData rvm,
                                                               Map<String, WorkflowTaskViewModel> map,
                                                               final String taskName) {
        return testTaskCommand(rvm, map, taskName, commands.get(taskName));
    }

    private Map<String, WorkflowTaskViewModel> testTaskCommand(ReferralData rvm,
                                                               Map<String, WorkflowTaskViewModel> taskStateByName, final String taskName,
                                                               BiFunction<ReferralData, String, ReferralData> commandOperation) {
        workflowActor.claimTask(taskStateByName.get(taskName));
        commandOperation.apply(rvm, taskStateByName.get(taskName).taskHandle);
        taskStateByName = workflowActor.getTasksByTaskName(rvm.referralViewModel);
        var dueDateOfIncompleteTask = taskStateByName.get(taskName).dueDate;
        if (!taskStateByName.get(taskName).isCompleted) {
            workflowActor.markTaskCompleted(taskStateByName.get(taskName));
            taskStateByName = workflowActor.getTasksByTaskName(rvm.referralViewModel);
        }
        // the task may not be complete, it may be a newly created task with new dueDate
        var dueDateOfCompleteTask = taskStateByName.get(taskName).dueDate;
        if (dueDateOfIncompleteTask == null || dueDateOfIncompleteTask.equals(dueDateOfCompleteTask)) {
            assertThat(taskStateByName.get(taskName)).matches(task -> task.isCompleted, "is completed");
        } else {
            assertThat(taskStateByName.get(taskName)).matches(task -> task.dueDate != null, "is re-created");
        }
        return taskStateByName;
    }

    private ReferralData dummyCommand(ReferralData rvm, String taskHandle) {
        return rvm;
    }


    /** aka 'referralAccepted' - do signpost and test then accept */
    private ReferralData submitAndCheckAppropriateReferralCommand(ReferralData referralData, String taskHandle) {

        assertThat(referralData.referralViewModel.appropriateReferralState).isEqualByComparingTo(AcceptState.UNSET);
        assertThat(referralData.referralViewModel.acceptOnServiceState).isEqualByComparingTo(AcceptState.UNSET);

        // 1: check signposting works
        {
            final AgencyViewModel signpostedAgency = getFirstAgencyEnsuringAtLeastOneExists(referralData.referralViewModel.serviceRecipientId);
            final ListDefinitionEntryViewModel signpostReason = getFirstSignpostReason();
            // TODO: Confirm whether or not we want to use LocalDateTime or not - probably just want instant irrespective of timezone
            LocalDate signpostedDate = nowLondonWithDST.minusDays(1).toLocalDate(); //

            ReferralTaskAppropriateReferralCommandViewModel cmd = Command.from(referralData).appropriateReferral();
            cmd.acceptedState = changeNullTo(AcceptState.SIGNPOSTED);
            cmd.acceptedDate = changeNullTo(signpostedDate);
            cmd.signpostedAgency = changeNullTo(signpostedAgency.contactId);
            cmd.signpostedComment = changeNullTo("the comment");
            cmd.signpostedReason = changeNullTo(signpostReason.id);

            referralData = executeCmdAndGetResult(cmd, referralData);

            // check 'close' is unavailable
            Map<String, WorkflowTaskViewModel> map = workflowActor.getTasksByTaskName(referralData.referralViewModel);
            Stream<String> unavailableTasks = map.values().stream().filter(task -> !task.isAvailable).map(task -> task.taskName);
            assertThat(unavailableTasks).contains(EXIT);

            assertThat(referralData.referralViewModel.appropriateReferralState).isEqualByComparingTo(AcceptState.SIGNPOSTED);
            assertThat(referralData.referralViewModel.acceptOnServiceState).isEqualByComparingTo(AcceptState.UNSET);
            assertThat(referralData.referralViewModel.signpostedBack).isFalse();
            assertThat(referralData.referralViewModel.signpostedAgencyId).isEqualByComparingTo(signpostedAgency.contactId);
            assertThat(referralData.referralViewModel.signpostedCommentId).isNotNull();
            assertThat(referralData.referralViewModel.signpostedReasonId).isEqualByComparingTo(signpostReason.id);
            assertThat(referralData.referralViewModel.decisionReferralMadeOn).isEqualByComparingTo(signpostedDate); // TODO: clarify. timezone/BST probable issue
        }

        // 2: change to signpostedBack works
        {
            ReferralTaskAppropriateReferralCommandViewModel cmd = Command.from(referralData).appropriateReferral();
            cmd.signpostedBack = changeNullTo(TRUE);

            referralData = executeCmdAndGetResult(cmd, referralData);

            // result should be that
            assertThat(referralData.referralViewModel.appropriateReferralState).isEqualByComparingTo(AcceptState.SIGNPOSTED);
            assertThat(referralData.referralViewModel.acceptOnServiceState).isEqualByComparingTo(AcceptState.UNSET);
            assertThat(referralData.referralViewModel.signpostedBack).isTrue();
            assertThat(referralData.referralViewModel.signpostedAgencyId).isNull();    // should be reset
            assertThat(referralData.referralViewModel.signpostedReasonId).isNotNull(); // should not have been reset as not changed
            assertThat(referralData.referralViewModel.signpostedCommentId).isNotNull(); // should not have been reset as not changed
        }

        // 3: check accept works
        {
            // TODO: Confirm whether or not we want to use LocalDateTime or not - probably just want instant irrespective of timezone
            LocalDate acceptedDate = nowLondonWithDST.minusDays(1).toLocalDate();

            ReferralTaskAppropriateReferralCommandViewModel cmd = Command.from(referralData).appropriateReferral();
            cmd.acceptedState = changeNullTo(AcceptState.ACCEPTED);
            cmd.acceptedDate = changeNullTo(acceptedDate);

            referralData = executeCmdAndGetResult(cmd, referralData);

            assertThat(referralData.referralViewModel.appropriateReferralState).isEqualByComparingTo(AcceptState.ACCEPTED);
            assertThat(referralData.referralViewModel.acceptOnServiceState).isEqualByComparingTo(AcceptState.UNSET);
            assertThat(referralData.referralViewModel.signpostedBack).isFalse();
            assertThat(referralData.referralViewModel.signpostedReasonId).isNull();
            assertThat(referralData.referralViewModel.signpostedAgencyId).isNull();
            assertThat(referralData.referralViewModel.signpostedCommentId).isNull();
            assertThat(referralData.referralViewModel.decisionReferralMadeOn).isEqualByComparingTo(acceptedDate); // TODO: clarify. timezone/BST probable issue
        }
        return referralData;
    }

    /** aka 'decideFinal' - do signpost and test then accept */
    private ReferralData submitAndCheckAcceptOnServiceCommand(ReferralData referralData, String taskHandle) {

        // 1: check signposting works
        {
            final AgencyViewModel signpostedAgency = getFirstAgencyEnsuringAtLeastOneExists(referralData.referralViewModel.serviceRecipientId);
            final ListDefinitionEntryViewModel signpostReason = getFirstSignpostReason();
            // TODO: Confirm whether or not we want to use LocalDateTime or not - probably just want instant irrespective of timezone
            LocalDate signpostedDate = nowLondonWithDST.minusDays(1).toLocalDate();

            ReferralTaskAcceptOnServiceCommandViewModel cmd = Command.from(referralData).acceptOnService();
            cmd.acceptedState = changeNullTo(AcceptState.SIGNPOSTED);
            cmd.acceptedDate = changeNullTo(signpostedDate);
            cmd.signpostedAgency = changeNullTo(signpostedAgency.contactId);
            cmd.signpostedComment = changeNullTo("the comment");
            cmd.signpostedReason = changeNullTo(signpostReason.id);

            referralData = executeCmdAndGetResult(cmd, referralData);

            // check 'close' is unavailable
            Map<String, WorkflowTaskViewModel> map = workflowActor.getTasksByTaskName(referralData.referralViewModel);
            Stream<String> unavailableTasks = map.values().stream().filter(task -> !task.isAvailable).map(task -> task.taskName);
            assertThat(unavailableTasks).contains(EXIT);

            assertThat(referralData.referralViewModel.appropriateReferralState).isEqualByComparingTo(AcceptState.ACCEPTED); // assumes was previously accepted
            assertThat(referralData.referralViewModel.acceptOnServiceState).isEqualByComparingTo(AcceptState.SIGNPOSTED);
            assertThat(referralData.referralViewModel.signpostedBack).isFalse();
            assertThat(referralData.referralViewModel.signpostedAgencyId).isEqualByComparingTo(signpostedAgency.contactId);
            assertThat(referralData.referralViewModel.signpostedCommentId).isNotNull();
            assertThat(referralData.referralViewModel.signpostedReasonId).isEqualByComparingTo(signpostReason.id);
            assertThat(referralData.referralViewModel.decisionMadeOn).isEqualByComparingTo(signpostedDate); // TODO: clarify. timezone/BST probable issue
        }

        // 2: change to signpostedBack works
        {
            ReferralTaskAcceptOnServiceCommandViewModel cmd = Command.from(referralData).acceptOnService();
            cmd.signpostedBack = changeNullTo(TRUE);

            referralData = executeCmdAndGetResult(cmd, referralData);

            // result should be that
            assertThat(referralData.referralViewModel.appropriateReferralState).isEqualByComparingTo(AcceptState.ACCEPTED); // assumes was previously accepted
            assertThat(referralData.referralViewModel.acceptOnServiceState).isEqualByComparingTo(AcceptState.SIGNPOSTED);
            assertThat(referralData.referralViewModel.signpostedBack).isTrue();
            assertThat(referralData.referralViewModel.signpostedAgencyId).isNull();    // should be reset
            assertThat(referralData.referralViewModel.signpostedReasonId).isNotNull(); // should not have been reset as not changed
            assertThat(referralData.referralViewModel.signpostedCommentId).isNotNull(); // should not have been reset as not changed
        }

        // 3: check accept works
        {
            referralData = acceptOnService(referralData);
        }
        return referralData;
    }

    @NonNull
    private ReferralData acceptOnService(ReferralData referralData) {
        // TODO: Confirm whether or not we want to use LocalDateTime or not - probably just want instant irrespective of timezone
        LocalDate acceptedDate = nowLondonWithDST.minusDays(1).toLocalDate();

        ReferralTaskAcceptOnServiceCommandViewModel cmd = Command.from(referralData).acceptOnService();
        cmd.acceptedState = changeNullTo(AcceptState.ACCEPTED);
        cmd.acceptedDate = changeNullTo(acceptedDate);

        referralData = executeCmdAndGetResult(cmd, referralData);

        assertThat(referralData.referralViewModel.appropriateReferralState).isEqualByComparingTo(AcceptState.ACCEPTED); // assumes was previously accepted
        assertThat(referralData.referralViewModel.acceptOnServiceState).isEqualByComparingTo(AcceptState.ACCEPTED);
        assertThat(referralData.referralViewModel.signpostedBack).isFalse();
        assertThat(referralData.referralViewModel.signpostedReasonId).isNull();
        assertThat(referralData.referralViewModel.signpostedCommentId).isNull();
        assertThat(referralData.referralViewModel.signpostedAgencyId).isNull();
        assertThat(referralData.referralViewModel.decisionMadeOn).isEqualByComparingTo(acceptedDate); // TODO: clarify. timezone/BST probable issue
        return referralData;
    }

    private ReferralData submitAndCheckCloseCommand(ReferralData referralData, String taskHandle) {

        // check close works - do without a comment to test referral status issue resolved in b9b27395
        {

            final var exitReason = getFirstExitReason();
            // TODO: Confirm whether or not we want to use LocalDateTime or not - probably just want instant irrespective of timezone
            LocalDate exitedDate = DateTime.now().minusDays(1).toLocalDate();

            ReferralTaskExitCommandViewModel cmd = Command.from(referralData).close();
            cmd.exitedDateChange = changeNullTo(exitedDate);
            cmd.exitedReasonChange = changeNullTo(exitReason.id);

            referralData = executeCmdAndGetResult(cmd, referralData);

            assertThat(referralData.referralViewModel.appropriateReferralState).isEqualByComparingTo(AcceptState.ACCEPTED); // assumes was previously accepted
            assertThat(referralData.referralViewModel.acceptOnServiceState).isEqualByComparingTo(AcceptState.ACCEPTED);
            assertThat(referralData.referralViewModel.exitComment).isNull();
            assertThat(referralData.referralViewModel.exitReasonId).isEqualByComparingTo(exitReason.id);
            assertThat(referralData.referralViewModel.exitedDate).isEqualByComparingTo(exitedDate); // TODO: clarify. timezone/BST probable issue
        }

        return referralData;
    }


    private ListDefinitionEntryViewModel getFirstSignpostReason() {
        SessionDataViewModel svm = sessionDataActor.getSessionData().getBody();

        ListDefinitionEntryViewModel r1 = null;
        if (!svm.listDefinitions.get(Referral.SIGNPOSTREASON_LISTNAME).iterator().hasNext()) {
            r1 = ListDefinitionEntryViewModel.builder().listName(Referral.SIGNPOSTREASON_LISTNAME).name("no spaces on service").build();
            r1 = listDefActor.ensureAndReturnListDefinitionEntry(r1.getListName(), r1).iterator().next();
        } else {
            r1 = svm.listDefinitions.get(Referral.SIGNPOSTREASON_LISTNAME).iterator().next();
        }

        return r1;
    }

    private ListDefinitionEntryViewModel getFirstExitReason() {
        SessionDataViewModel svm = sessionDataActor.getSessionData().getBody();

        ListDefinitionEntryViewModel exitReason = null;
        if (!svm.listDefinitions.get(Referral.EXITREASON_LISTNAME).iterator().hasNext()) {
            exitReason = ListDefinitionEntryViewModel.builder().listName(Referral.EXITREASON_LISTNAME).name("client too chaotic").build();
            exitReason = listDefActor.ensureAndReturnListDefinitionEntry(exitReason.getListName(), exitReason).iterator().next();
        } else {
            exitReason = svm.listDefinitions.get(Referral.EXITREASON_LISTNAME).iterator().next();
        }

        return exitReason;
    }

    private AgencyViewModel getFirstAgencyEnsuringAtLeastOneExists(int contextId) {
        AgencyViewModel[] agencies = contactActor.getAllAgencies().getBody();

        if (agencies.length == 0) {
            AgencyViewModel agency = new AgencyViewModel();
            agency.contextId = contextId;
            agency.companyName = "Hogwarts Police Service";
            contactActor.createAgency(agency);
            agencies = contactActor.getAllAgencies().getBody();
        }
        return agencies[0];
    }


    private ReferralData submitAndCheckScheduleReviews(ReferralData referralData, String taskHandle) {


        { // 1 check custom date change
            final LocalDate reviewDate = LocalDate.now().plusDays(2);

            ReferralTaskScheduleReviewsCommandViewModel cmd = Command.from(referralData).scheduleReviews();
            cmd.customDateChange = changeNullTo(reviewDate);

            referralData = executeCmdAndGetResult(cmd, referralData);
            var choices = referralActor.getReviewChoices(referralData.referralViewModel.serviceRecipientId);
            assertThat(choices.getBody().nextReviewDate.equals(reviewDate));
            // TODO: change .reviewDates to LocalDate not DateTime ??

            // Check that interviews have been added to appropriate calendars
            EventResource[] allEntries = calendarActor.getEntriesByTime(reviewDate,
                    reviewDate.plusDays(1),
                    new Long[]{referralData.referralViewModel.contactId}, Collections.emptyList()).getBody();
            assert allEntries != null;
            List<EventResource> entries = Arrays.stream(allEntries).filter(r -> !r.getTitle().startsWith("Interview")).collect(Collectors.toList());
            assertEquals(entries.size(), 1);
            var reviewEvent = entries.get(0);

            assertFalse(reviewEvent.isRecurrence());
            assertFalse(reviewEvent.isRecurringEntry());
            assertTrue(reviewEvent.isAllDay());
            assertTrue(reviewEvent.hasLink("edit")); // not edit-adhoc because we're controlled from the referral

            List<EventAttendee> eventAttendees = reviewEvent.getAttendees();
            assertEquals(2, eventAttendees.size());
            List<String> names = eventAttendees.stream().map(eventAttendee -> eventAttendee.name).collect(Collectors.toList());
            org.junit.Assert.assertThat("Expected attendees", names, containsInAnyOrder(referralData.referralViewModel.displayName,
                    referralData.referralViewModel.supportWorkerDisplayName));

            // TODO check previous dates are removed

        }
        { // 2: check set dates in diary
            ReferralTaskScheduleReviewsCommandViewModel cmd = Command.from(referralData).scheduleReviews();
            cmd.defaultDatesFromSchedule = "6w,13w";

            referralData = executeCmdAndGetResult(cmd, referralData);
            // 6 weeks after start date
            var choices = referralActor.getReviewChoices(referralData.referralViewModel.serviceRecipientId);
            assertThat(choices.getBody().nextReviewDate.equals(receivingServiceDate.plusDays(42)));

            // TODO check calendar event on that day
        }
        return referralData;
    }

    public static long createReferral(ReferralActor referralActor, long clientId, DateTime receivedDate, ServiceOptions serviceOptions) {
        ReferralViewModel rvm = new ReferralViewModel();
        rvm.setClientId(clientId);
        rvm.setReceivedDate(receivedDate.toLocalDate());
        rvm.setImportServiceName(serviceOptions.getServiceName() != null
                ? serviceOptions.getServiceName()
                : serviceOptions.ACCOMMODATION.getServiceName());

        return referralActor.createReferralFromCommand(rvm);
    }

    public static long createReferral(ReferralActor referralActor, long clientId, DateTime receivedDate) {
        return createReferral(referralActor, clientId, receivedDate, ServiceOptions.ACCOMMODATION);
    }

    private ReferralData executeCmdAndGetResult(BaseCommandViewModel cmd, ReferralData referralData) {
        referralActor.executeCommand(cmd);
        ReferralViewModel rvm = referralActor.getReferralById(referralData.referralViewModel.referralId).getBody();
        return new ReferralData(rvm, referralData.serviceOptions, referralData.referralOptions, referralData.tasks);
    }

}
