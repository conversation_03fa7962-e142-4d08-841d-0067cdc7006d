package com.ecco.acceptancetests.api.referral;

import com.ecco.webApi.contacts.ClientDetailAbstractViewModel;
import com.ecco.dto.ChangeViewModel;
import com.ecco.webApi.taskFlow.TaskClientDetailAbstractCommandViewModel;
import org.joda.time.LocalDate;
import static org.junit.Assert.assertEquals;

public class TaskClientDetailAbstractCommandAPITests {

    public void populateSuccess(TaskClientDetailAbstractCommandViewModel cmd, ClientDetailAbstractViewModel cvm) {
        cmd.birthDateChange = ChangeViewModel.create(cvm.birthDate, new LocalDate());
        cmd.genderChange = ChangeViewModel.create(cvm.genderId, 115); // MALE
        cmd.lastNameChange = ChangeViewModel.create(cvm.lastName, "my last");
        cmd.firstNameChange = ChangeViewModel.create(cvm.firstName, "my first");
        // <PERSON><PERSON> commented for now as we'd need to lookup from a dynamic list
        //cmd.firstLanguageChange = ChangeViewModel.create(cvm.firstLanguageId, );
        //cmd.ethnicOriginChange = ChangeViewModel.create(cvm.ethnicOriginId, );
        //cmd.religionChange = ChangeViewModel.create(cvm.religionId, );
        cmd.disabilityChange = ChangeViewModel.create(cvm.disabilityId, 122);
        cmd.sexualOrientationChange = ChangeViewModel.create(cvm.sexualOrientationId, 127);
        cmd.niChange = ChangeViewModel.create(cvm.ni, "AB123456");
        cmd.nhsChange = ChangeViewModel.create(cvm.nhs, "**********");
    }

    public void checkSuccess(ClientDetailAbstractViewModel cvmUpdated) {
        assertEquals("birthDate not the same", new LocalDate(), cvmUpdated.birthDate);
        assertEquals("gender not the same", Integer.valueOf(115), cvmUpdated.genderId);
        assertEquals("lastName not the same", "my last", cvmUpdated.lastName);
        assertEquals("firstName not the same", "my first", cvmUpdated.firstName);
        assertEquals("disability not the same", Integer.valueOf(122), cvmUpdated.disabilityId);
        assertEquals("sexualOrientation not the same", Integer.valueOf(127), cvmUpdated.sexualOrientationId);
        assertEquals("ni not the same", "AB123456", cvmUpdated.ni);
        assertEquals("nhs not the same", "**********", cvmUpdated.nhs);
    }

    public void populateFail(TaskClientDetailAbstractCommandViewModel cmd, ClientDetailAbstractViewModel cvm) {
        cmd.niChange = ChangeViewModel.create(cvm.ni, "DF123456"); // DF are invalid
        cmd.nhsChange = ChangeViewModel.create(cvm.nhs, "**********123"); // not 10 chars
    }

}
