package com.ecco.acceptancetests.api.serviceConfig;

import com.ecco.acceptancetests.api.BaseJsonTest;
import com.ecco.serviceConfig.dom.TaskDefinition;
import com.ecco.test.support.UniqueDataService;
import com.ecco.webApi.evidence.BaseCommandViewModel;
import com.ecco.dto.ChangeViewModel;
import com.ecco.webApi.serviceConfig.TaskDefinitionViewModel;
import com.ecco.webApi.serviceConfig.TaskDefinitionCommandViewModel;
import org.junit.jupiter.api.Test;
import org.springframework.web.client.HttpServerErrorException;

import java.util.List;
import java.util.stream.Stream;

import static java.util.stream.Collectors.toList;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertThrows;

public class TaskDefinitionCommandAPITests extends BaseJsonTest {

    //protected final static LocalDateTime now = new LocalDateTime();
    private final UniqueDataService unique = UniqueDataService.instance;
    private static final String ADD = BaseCommandViewModel.OPERATION_ADD;
    private static final String UPDATE = BaseCommandViewModel.OPERATION_UPDATE;
    private static final String REMOVE = BaseCommandViewModel.OPERATION_REMOVE;

    // HTTP 400 - Bad Request (Valid command but failed to execute) thrown by spring-data when deleting something that doesn't exist
    @Test
    public void cannotRemoveWhatIsNotThere() {
        TaskDefinitionCommandViewModel vm = new TaskDefinitionCommandViewModel(REMOVE, "unknownName");
        assertThrows(HttpServerErrorException.class, () -> commandActor.executeCommand(vm));

    }

    @Test
    public void cannotRemovePredefined() {
        TaskDefinitionCommandViewModel vm = new TaskDefinitionCommandViewModel(REMOVE, "needsAssessment");
        assertThrows(HttpServerErrorException.class, () -> commandActor.executeCommand(vm));
    }

    @Test
    public void cannotEditPredefined() {
        TaskDefinitionCommandViewModel vm = new TaskDefinitionCommandViewModel(UPDATE, "needsAssessment");
        vm.nameChange = ChangeViewModel.create("needsAssessment", "my needs assessment");
        assertThrows(HttpServerErrorException.class, () -> commandActor.executeCommand(vm));
    }

    @Test
    public void canAdd() {
        TaskDefinitionViewModel vm = canAdd("canAdd", TaskDefinition.Type.EVIDENCE_SUPPORT);

        var filtered = getTaskDef(vm.name);
        assertEquals(1, filtered.size());
    }

    @Test
    public void canAddThenUpdate() {
        TaskDefinitionViewModel vm = canAdd("canAddThenUpdate", TaskDefinition.Type.EVIDENCE_SUPPORT);

        var filtered = getTaskDef(vm.name);
        assertEquals(1, filtered.size());

        TaskDefinitionCommandViewModel cmd = new TaskDefinitionCommandViewModel(UPDATE, null);
        cmd.taskDefinitionId = Long.valueOf(vm.id).intValue();
        String newName = unique.nameFor("my new name");
        cmd.nameChange = ChangeViewModel.create("canAddThenUpdate", newName);
        commandActor.executeCommand(cmd);

        filtered = getTaskDef(newName);
        assertEquals(1, filtered.size());
        assertEquals(vm.id, filtered.get(0).id);
        assertEquals(newName, filtered.get(0).name);
    }

    private TaskDefinitionViewModel canAdd(String name, TaskDefinition.Type type) {
        String uniqueName = unique.nameFor(name);
        TaskDefinitionCommandViewModel vm = new TaskDefinitionCommandViewModel(ADD, uniqueName);
        vm.nameChange = ChangeViewModel.create(null, uniqueName);
        vm.type = type;
        commandActor.executeCommand(vm);

        var filtered = getTaskDef(uniqueName);
        assertEquals(1, filtered.size());
        return filtered.get(0);
    }

    private List<TaskDefinitionViewModel> getTaskDef(String name) {
        return Stream.of(getAllTaskDefinitions())
                .filter(f -> f.name.equals(name))
                .collect(toList());
    }

    private TaskDefinitionViewModel[] getAllTaskDefinitions() {
        return taskDefActor.getAllTaskDefinitions().getBody();
    }

}
