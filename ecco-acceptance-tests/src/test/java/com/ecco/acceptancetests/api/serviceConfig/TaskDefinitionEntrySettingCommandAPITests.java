package com.ecco.acceptancetests.api.serviceConfig;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.notNullValue;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Stream;

import com.ecco.data.client.actors.BaseCommandActor;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import com.ecco.acceptancetests.api.BaseJsonTest;
import com.ecco.serviceConfig.viewModel.OutcomeViewModel;
import com.ecco.serviceConfig.viewModel.QuestionGroupViewModel;
import com.ecco.serviceConfig.viewModel.RiskAreaViewModel;
import com.ecco.serviceConfig.viewModel.ServiceTypeViewModel;
import com.ecco.serviceConfig.viewModel.TaskDefinitionEntryViewModel;
import com.ecco.dto.ChangeViewModel;
import com.ecco.evidence.EvidenceTask;
import com.ecco.webApi.featureConfig.SessionDataViewModel;
import com.ecco.webApi.serviceConfig.TaskDefinitionViewModel;
import com.ecco.webApi.serviceConfig.TaskDefinitionEntryCommandViewModel;
import com.ecco.webApi.serviceConfig.TaskDefinitionEntrySettingCommandViewModel;
import com.ecco.webApi.viewModels.Result;
import com.google.common.collect.ImmutableMap;

public class TaskDefinitionEntrySettingCommandAPITests extends BaseJsonTest {

    //protected final static LocalDateTime now = new LocalDateTime();
    //private final UniqueDataService unique = UniqueDataService.instance;

    private static SessionDataViewModel sdvm;
    private static Integer serviceTypeId;


    /* TaskDefinitionEntry */
    private static final EvidenceTask TASKDEF_NEEDSASSESSMENT = EvidenceTask.NEEDS_ASSESSMENT;
    private static final EvidenceTask TASKDEF_THREATASSESSMENT = EvidenceTask.THREAT_ASSESSMENT;
    private static final EvidenceTask TASKDEF_QUESTIONNAIREGENERAL = EvidenceTask.QUESTIONNAIRE_GENERAL;
    private static String[] taskDefEntries1 = {
        "source", "dataProtection", "referralAccepted", "decideFinal", "scheduleReviews", "start",
        TASKDEF_NEEDSASSESSMENT.getTaskName(), TASKDEF_THREATASSESSMENT.getTaskName(),
        TASKDEF_QUESTIONNAIREGENERAL.getTaskName(), "endFlow"};


    /* TaskDefinitionEntrySetting - some settings for TASKDEF_NEEDSASSESSMENT */
    private static String[] settingsOfText = {
            "titleCode",
            "sourcePageGroup",
            "outcomes",
            "questions",
            "outcomesById",
            "flagListName",
            "transientOutcomesByUuid",};
    private static ImmutableMap<String, String[]> settingOfOptions = ImmutableMap.of(
            "tookPlaceOn", new String[] {"dateTime"},
            "showActionGroups", new String[] {"link", "target", "status", "comment"}
            );

    @BeforeEach
    public void createServiceTypeData() {
        // emulating @BeforeClass but we want our injected services
        if (sdvm == null) {
            sdvm = sessionDataActor.getSessionData().getBody();
        }
        if (serviceTypeId == null) {
            ensureServiceType("servicetype1");
        }
    }

    @Test
    public void canAcceptSomeCommand() {
        String settingName_Text = settingsOfText[0];
        TaskDefinitionEntrySettingCommandViewModel tvm = createCommand(TASKDEF_NEEDSASSESSMENT.getTaskName(), settingName_Text);
        tvm.valueChange = ChangeViewModel.create(null, "any text here");
        Result result = commandActor.executeCommand(tvm).getBody();

        assertThat(result.isCommandSuccessful(), is(Boolean.TRUE));
        assertThat(result.getId(), notNullValue());
    }

    @Test
    public void canCreateSetting() {
        createSetting(TASKDEF_NEEDSASSESSMENT, settingsOfText[0], "some random value");
    }

    private void createSetting(EvidenceTask evidenceTask, String textSettingName, String value) {
        this.createSetting(evidenceTask, textSettingName, value, true);
    }
    private void createSetting(EvidenceTask evidenceTask, String textSettingName, String value, boolean verify) {
        TaskDefinitionEntrySettingCommandViewModel tvm = createCommand(evidenceTask.getTaskName(), textSettingName);
        tvm.valueChange = ChangeViewModel.create(null, value);
        commandActor.executeCommand(tvm);

        if (verify) {
            verifySetting(evidenceTask.getTaskName(), textSettingName, value);
        }
    }

    @Test
    public void canModifySetting() {
        String settingName_Text = settingsOfText[0];
        createSetting(TASKDEF_NEEDSASSESSMENT, settingName_Text, "any random value");

        String value = "some DIFFERENT text here";
        TaskDefinitionEntrySettingCommandViewModel tvm = createCommand(TASKDEF_NEEDSASSESSMENT.getTaskName(), settingName_Text);
        tvm.valueChange = ChangeViewModel.create("any random value", value);
        commandActor.executeCommand(tvm);

        verifySetting(TASKDEF_NEEDSASSESSMENT.getTaskName(), settingName_Text, value);
    }

    /**
     * There is no reason not to delete settings - in fact it might be that the presence
     * of the setting is enough to 'turn it on', but this isn't the approach taken - so just test it
     */
    @Test
    public void canRemoveSetting() {
        String settingName_Text = settingsOfText[1];
        String value = "99";
        createSetting(TASKDEF_NEEDSASSESSMENT, settingName_Text, value);

        TaskDefinitionEntrySettingCommandViewModel tvm = createCommand(TASKDEF_NEEDSASSESSMENT.getTaskName(), settingName_Text);
        tvm.valueChange = ChangeViewModel.create(value, null);
        commandActor.executeCommand(tvm);

        verifySetting(TASKDEF_NEEDSASSESSMENT.getTaskName(), settingName_Text, null);
    }

    @Test
    public void removeTaskDefinitionRemovesSettings() {
        // ensure some setting exists
        createSetting(TASKDEF_NEEDSASSESSMENT, settingsOfText[0], "some random value");

        TaskDefinitionEntryCommandViewModel tvm = new TaskDefinitionEntryCommandViewModel(
                TaskDefinitionEntryCommandViewModel.OPERATION_REMOVE, serviceTypeId, TASKDEF_NEEDSASSESSMENT.getTaskName());
        ResponseEntity<Result> response = commandActor.executeCommand(tvm);
        // any error will have already been thrown, but at least we are verifying what we expect of the test
        assertThat(response.getStatusCode(), is(HttpStatus.OK));

        // reset next test, which is to just add TASKDEF_NEEDSASSESSMENT back in
        tvm = new TaskDefinitionEntryCommandViewModel(
                TaskDefinitionEntryCommandViewModel.OPERATION_ADD, serviceTypeId, TASKDEF_NEEDSASSESSMENT.getTaskName());
        int originalOrder = (Arrays.asList(taskDefEntries1).indexOf(TASKDEF_NEEDSASSESSMENT.getTaskName())+1) * 5;
        tvm.orderbyChange = ChangeViewModel.create(null, originalOrder);
        commandActor.executeCommand(tvm);
    }

    /**
     * Verify that 'outcomes' invoke the special handler so that the outcomes also get
     * added to the service type (and not just in the 'outcomes' setting value)
     */
    @Test
    public void specialHandler_canAddSupportOutcomes() {
        OutcomeViewModel[] vms = outcomeActor.findAllSupportOutcomes().getBody();
        OutcomeViewModel outcome1 = vms[0];

        createSetting(TASKDEF_NEEDSASSESSMENT, settingsOfText[2], outcome1.name);

        ServiceTypeViewModel st = serviceTypeActor.getServiceTypeById(serviceTypeId).getBody();
        assertThat(st.supportOutcomes.size(), is(1));
        assertThat(st.supportOutcomes.get(0).name, is(outcome1.name));
    }

    /**
     * Verify that 'outcomes' invoke the special handler.
     * Also, verify the outcomes are removed - we no longer need them for historical data against that outcome
     */
    @Test
    public void specialHandler_canRemoveSupportOutcomes() {
        specialHandler_canAddSupportOutcomes();

        OutcomeViewModel[] vms = outcomeActor.findAllSupportOutcomes().getBody();
        OutcomeViewModel outcome1 = vms[0];
        String settingName_Text = settingsOfText[2];

        TaskDefinitionEntrySettingCommandViewModel tvm = createCommand(TASKDEF_NEEDSASSESSMENT.getTaskName(), settingName_Text);
        tvm.valueChange = ChangeViewModel.create(outcome1.name, null);
        commandActor.executeCommand(tvm);

        // the setting is null
        verifySetting(TASKDEF_NEEDSASSESSMENT.getTaskName(), settingName_Text, null);

        // verify that the outcome is no longer attached
        ServiceTypeViewModel st = serviceTypeActor.getServiceTypeById(serviceTypeId).getBody();
        assertThat(st.supportOutcomes.size(), is(0));
    }

    @Test
    public void specialHandler_canAddThreatOutcomes() {
        RiskAreaViewModel[] vms = outcomeActor.findAllThreatOutcomes().getBody();
        String settingName_Text = settingsOfText[2];
        RiskAreaViewModel outcome1 = vms[0];

        createSetting(TASKDEF_THREATASSESSMENT, settingName_Text, outcome1.name);

        ServiceTypeViewModel st = serviceTypeActor.getServiceTypeById(serviceTypeId).getBody();
        assertThat(st.riskAreas.size(), is(1));
        assertThat(st.riskAreas.get(0).name, is(outcome1.name));
    }

    @Test
    public void specialHandler_canRemoveThreatOutcomes() {
        specialHandler_canAddThreatOutcomes();

        RiskAreaViewModel[] vms = outcomeActor.findAllThreatOutcomes().getBody();
        RiskAreaViewModel outcome1 = vms[0];
        String settingName_Text = settingsOfText[2];

        TaskDefinitionEntrySettingCommandViewModel tvm = createCommand(TASKDEF_THREATASSESSMENT.getTaskName(), settingName_Text);
        tvm.valueChange = ChangeViewModel.create(outcome1.name, null);
        commandActor.executeCommand(tvm);

        // the setting is null, but the underlying config is still attached
        verifySetting(TASKDEF_THREATASSESSMENT.getTaskName(), settingName_Text, null);

        // verify that the outcome is no longer attached
        ServiceTypeViewModel st = serviceTypeActor.getServiceTypeById(serviceTypeId).getBody();
        assertThat(st.riskAreas.size(), is(0));
    }

    /**
     * Verify that 'outcomesById' invoke the special handler so that the outcomes also get
     * added to the service type (and not just in the 'outcomes' setting value)
     */
    @Test
    public void specialHandler_canAddSupportOutcomesById() {
        OutcomeViewModel[] vms = outcomeActor.findAllSupportOutcomes().getBody();
        OutcomeViewModel outcome1 = vms[0];

        createSetting(TASKDEF_NEEDSASSESSMENT, settingsOfText[4], outcome1.id.toString());

        ServiceTypeViewModel st = serviceTypeActor.getServiceTypeById(serviceTypeId).getBody();
        assertThat(st.supportOutcomes.size(), is(1));
        assertThat(st.supportOutcomes.get(0).name, is(outcome1.name));
    }

    @Test
    public void specialHandler_canRemoveSupportOutcomesById() {
        specialHandler_canAddSupportOutcomesById();

        OutcomeViewModel[] vms = outcomeActor.findAllSupportOutcomes().getBody();
        OutcomeViewModel outcome1 = vms[0];
        String settingName_Text = settingsOfText[4];

        TaskDefinitionEntrySettingCommandViewModel tvm = createCommand(TASKDEF_NEEDSASSESSMENT.getTaskName(), settingName_Text);
        // to NULL - this is the delete
        tvm.valueChange = ChangeViewModel.create(outcome1.id.toString(), null);
        commandActor.executeCommand(tvm);

        // the setting is null
        verifySetting(TASKDEF_NEEDSASSESSMENT.getTaskName(), settingName_Text, null);

        // verify that the outcome is no longer attached
        ServiceTypeViewModel st = serviceTypeActor.getServiceTypeById(serviceTypeId).getBody();
        assertThat(st.supportOutcomes.size(), is(0));
    }

    /**
     * Verify that 'outcomesById' invoke the special handler so that the outcomes also get
     * added to the service type (and not just in the 'outcomes' setting value)
     */
    @Test
    public void specialHandler_canAddThreatOutcomesById() {
        RiskAreaViewModel[] vms = outcomeActor.findAllThreatOutcomes().getBody();
        RiskAreaViewModel outcome1 = vms[0];

        createSetting(TASKDEF_THREATASSESSMENT, settingsOfText[4], outcome1.id.toString());

        ServiceTypeViewModel st = serviceTypeActor.getServiceTypeById(serviceTypeId).getBody();
        assertThat(st.riskAreas.size(), is(1));
        assertThat(st.riskAreas.get(0).name, is(outcome1.name));
    }

    @Test
    public void specialHandler_canRemoveThreatOutcomesById() {
        specialHandler_canAddThreatOutcomesById();

        RiskAreaViewModel[] vms = outcomeActor.findAllThreatOutcomes().getBody();
        RiskAreaViewModel outcome1 = vms[0];
        String settingName_Text = settingsOfText[4];

        TaskDefinitionEntrySettingCommandViewModel tvm = createCommand(TASKDEF_THREATASSESSMENT.getTaskName(), settingName_Text);
        // to NULL - this is the delete
        tvm.valueChange = ChangeViewModel.create(outcome1.id.toString(), null);
        commandActor.executeCommand(tvm);

        // the setting is null
        verifySetting(TASKDEF_THREATASSESSMENT.getTaskName(), settingName_Text, null);

        // verify that the outcome is no longer attached
        ServiceTypeViewModel st = serviceTypeActor.getServiceTypeById(serviceTypeId).getBody();
        assertThat(st.riskAreas.size(), is(0));
    }

    /**
     * Verify that 'transientOutcomesByUuid' invokes the special handler so that the outcomes also get
     * added to the service type
     */
    @Test
    public void transientHandler_canAddSupportTransientOutcomesByUuid() {
        OutcomeViewModel[] vms = outcomeActor.findAllSupportOutcomes().getBody();
        OutcomeViewModel outcome1 = vms[0];

        createSetting(TASKDEF_NEEDSASSESSMENT, settingsOfText[6], outcome1.uuid.toString(), false);

        ServiceTypeViewModel st = serviceTypeActor.getServiceTypeById(serviceTypeId).getBody();
        assertThat(st.supportOutcomes.size(), is(1));
        assertThat(st.supportOutcomes.get(0).name, is(outcome1.name));
    }

    /**
     * Verify that 'outcomes' invoke the special handler so that the outcomes also get
     * added to the service type (and not just in the 'outcomes' setting value)
     */
    @Test
    public void specialHandler_canAddQuestionGroups() {
        QuestionGroupViewModel[] vms = questionGroupActor.findAllQuestionGroups().getBody();
        QuestionGroupViewModel questionGroup1 = vms[0];
        String settingName_Text = settingsOfText[3];

        createSetting(TASKDEF_QUESTIONNAIREGENERAL, settingName_Text, questionGroup1.name);

        ServiceTypeViewModel st = serviceTypeActor.getServiceTypeById(serviceTypeId).getBody();
        assertThat(st.questionGroups.size(), is(1));
        assertThat(st.questionGroups.get(0).name, is(questionGroup1.name));
    }

    /**
     * Verify that 'outcomes' invoke the special handler so that the outcomes also get
     * added to the service type (and not just in the 'outcomes' setting value)
     * Also, verify that we don't remove them in case data has been saved against that outcome
     * and if the outcome is no longer associated, then currently, the history breaks
     */
    @Test
    public void specialHandler_canNotRemoveQuestionGroups() {
        specialHandler_canAddQuestionGroups();

        QuestionGroupViewModel[] vms = questionGroupActor.findAllQuestionGroups().getBody();
        QuestionGroupViewModel questionGroup1 = vms[0];
        String settingName_Text = settingsOfText[3];

        TaskDefinitionEntrySettingCommandViewModel tvm = createCommand(TASKDEF_QUESTIONNAIREGENERAL.getTaskName(), settingName_Text);
        tvm.valueChange = ChangeViewModel.create(questionGroup1.name, null);
        commandActor.executeCommand(tvm);

        // the setting is null, but the underlying config is still attached
        ServiceTypeViewModel st = serviceTypeActor.getServiceTypeById(serviceTypeId).getBody();
        assertThat(st.questionGroups.size(), is(1));
        assertThat(st.questionGroups.get(0).name, is(questionGroup1.name));
    }

    /**
     * Verify that 'flagThreatsById' invoke the special handler so that the flags also get
     * added to the service type (and not just in the setting value)
     */
    @Test
    public void specialHandler_canAddFlagListName() {
        // NB there is one flag in the portableDataBaseline.xml - see <insert tableName="flags">
        var listName = "flags-risks";

        // now retrieve them and verify
        createSetting(TASKDEF_THREATASSESSMENT, settingsOfText[5], listName);
    }

    /**
     * Verify that 'flagListName' is set and removed
     * We can safely remove the association because the flag data (ie its name) is not derived from the service type.
     * NB The flag data is currently gathered from the server side repository history - so as long as the flag exists
     * the data will show. Both the risk history and 'risk outstanding' area use this repository, ThreatWorkRepositoryImpl.
     * (see ThreatWorkToViewModel and RiskManagmentOutstandingControl.ts)
     */
    @Test
    public void specialHandler_canRemoveFlagThreatsById() {
        specialHandler_canAddFlagListName();

        var listName = "flags-risks";
        String settingName_Text = settingsOfText[5];

        TaskDefinitionEntrySettingCommandViewModel tvm = createCommand(TASKDEF_THREATASSESSMENT.getTaskName(), settingName_Text);
        // to NULL - this is the delete
        tvm.valueChange = ChangeViewModel.create(listName, null);
        commandActor.executeCommand(tvm);

        // the setting is null, but the underlying config is still attached
        verifySetting(TASKDEF_THREATASSESSMENT.getTaskName(), settingName_Text, null);
    }

    private void ensureServiceType(String name) {
        List<ServiceTypeViewModel> serviceTypes = serviceTypeActor.getServiceType(name);
        if (serviceTypes.size() > 0) {
            ServiceTypeViewModel vm = serviceTypes.get(0);
            serviceTypeId = vm.id;
            return;
        } else {
            serviceTypeId = serviceTypeActor.createServiceType(name, false).id;
        }

        for (String taskName : taskDefEntries1) {
            verifyTaskName(taskName);
        }
        createServiceTypeTasks(serviceTypeId, commandActor);

        ServiceTypeViewModel svm = serviceTypeActor.getServiceTypeById(serviceTypeId).getBody();
        assertThat(serviceTypeId, is(svm.id));
        assertThat(svm.taskDefinitionEntries.size(), is(taskDefEntries1.length));
        assertThat(svm.taskDefinitionEntries.get(6).name, is(TASKDEF_NEEDSASSESSMENT.getTaskName()));
    }

    public static void createServiceTypeTasks(long serviceTypeId, BaseCommandActor commandActor) {
        TaskDefinitionEntryCommandViewModel tvm;
        int orderby = 0;
        for (String taskName : taskDefEntries1) {
            tvm = new TaskDefinitionEntryCommandViewModel(TaskDefinitionEntryCommandViewModel.OPERATION_ADD, serviceTypeId, taskName);
            orderby += 5;
            tvm.orderbyChange = ChangeViewModel.create(null, orderby);
            commandActor.executeCommand(tvm);
        }
    }

    private void verifyTaskName(String taskName) {
        Stream<TaskDefinitionViewModel> filtered = sdvm.taskDefinitions.stream()
                .filter(input -> taskName.equals(input.name));
        assertThat((int) filtered.count(), is(1));
    }

    private TaskDefinitionEntrySettingCommandViewModel createCommand(String taskName, String settingName) {
        return new TaskDefinitionEntrySettingCommandViewModel(serviceTypeId, taskName, settingName);
    }

    private void verifySetting(String taskName, String settingName, String expectedValue) {
        ServiceTypeViewModel svm = serviceTypeActor.getServiceTypeById(serviceTypeId).getBody();
        TaskDefinitionEntryViewModel taskDef = svm.taskDefinitionEntries.stream()
                .filter(input -> taskName.equals(input.name))
                .findFirst().orElseThrow();
        assertThat(taskDef.settings.get(settingName), is(expectedValue));
    }

}