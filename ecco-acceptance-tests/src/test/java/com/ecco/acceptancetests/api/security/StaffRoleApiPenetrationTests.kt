package com.ecco.acceptancetests.api.security

import com.ecco.acceptancetests.api.BaseJsonTest
import com.ecco.acceptancetests.ui.pages.Role
import org.springframework.web.client.HttpClientErrorException.Forbidden
import kotlin.test.assertFailsWith

/**
 * Test that ROLE_STAFF cannot do things they that are beyond their level of authority
 */
class StaffRoleApiPenetrationTests : BaseJsonTest() {
    companion object {
        val sharedStaffCredentials: Pair<String, String>? = null
    }

    /** Create staff user once and keep the credentials */
    private val staffCredentials: Pair<String, String>
        @Synchronized
        get() {
            if (sharedStaffCredentials != null) return sharedStaffCredentials

            val userName = unique.userNameFor("STAFF")
            val pwd = unique.passwordFor("STAFF")
            userManagementSteps.createIndividualWithUser(
                userName,
                pwd,
                "$userName-first",
                "$userName-last",
                Role.staff,
            )
            return userName to pwd
        }

    @org.junit.jupiter.api.Test
    fun staffUserCannotAccessUserManagementApis() {
        // GIVEN we're logged in as staff
        val (username, password) = staffCredentials
        loginAs(username, password)

        // WHEN
        assertFailsWith<Forbidden>("Should not be able to modify sysadmin (or any other user's) password") {
            userActor.createPassword("sysadmin", "Now-I-can-do-anything!")
        }

        assertFailsWith<Forbidden>("Should not be able to list users (via UserListController)") {
            userActor.getUsers()
        }

        assertFailsWith<Forbidden>("Should not be able to get a single user (via UserController)") {
            userActor.getUser("sysadmin")
        }
    }
}