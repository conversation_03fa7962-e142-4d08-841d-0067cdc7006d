package com.ecco.acceptancetests.api.referral;

import com.ecco.test.support.UniqueDataService;
import com.ecco.acceptancetests.api.BaseJsonTest;
import com.ecco.data.client.ServiceOptions;
import com.ecco.webApi.contacts.ClientViewModel;
import com.ecco.dto.ChangeViewModel;
import com.ecco.webApi.evidence.ReferralViewModel;
import com.ecco.webApi.taskFlow.ReferralTaskClientDetailCommandViewModel;
import org.junit.jupiter.api.Test;
import org.springframework.web.client.HttpClientErrorException.BadRequest;


import static org.junit.Assert.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

/**
 * An isolated test, in that it does not form part of DefaultReferralStepsWebApiSupport because
 * we do not yet have a testing-flow with this in it.
 */
public class ReferralTaskClientWithContactUpdateCommandAPITests extends BaseJsonTest {

    ReferralViewModel rvm;
    TaskClientDetailAbstractCommandAPITests support = new TaskClientDetailAbstractCommandAPITests();

    @Test
    public void canEditClient() {

        // GIVEN a referral and a client
        ensureReferral();
        ClientViewModel cvm;

        // WHEN update the client
        {
            cvm = clientActor.getClientById(rvm.clientId).getBody();
            ReferralTaskClientDetailCommandViewModel cmd = new ReferralTaskClientDetailCommandViewModel(rvm.serviceRecipientId, null);
            support.populateSuccess(cmd, cvm);
            cmd.housingBenefitChange = ChangeViewModel.create(cvm.housingBenefit, "my new hb ref");
            commandActor.executeCommand(cmd);
        }

        // THEN we get the updated details back
        {
            ClientViewModel cvmUpdated = clientActor.getClientById(cvm.clientId).getBody();
            support.checkSuccess(cvmUpdated);
            assertEquals("housingBenefit not the same", "my new hb ref", cvmUpdated.housingBenefit);
        }
    }

    @Test
    public void failToEditClient() {

        // GIVEN a referral and a client
        ensureReferral();
        ClientViewModel cvm = clientActor.getClientById(rvm.clientId).getBody();

        // WHEN update the client with dodgy data
        {
            ReferralTaskClientDetailCommandViewModel cmd = new ReferralTaskClientDetailCommandViewModel(rvm.serviceRecipientId, null);
            this.support.populateFail(cmd, cvm);
            assertThrows(BadRequest.class, () -> commandActor.executeCommand(cmd));
        }
    }

    private void ensureReferral() {
        if (this.rvm == null) {
            String firstNameUnique = "EditingOnly";
            String lastNameUnique = UniqueDataService.instance.clientLastNameFor("ClientCmd");

            int serviceId = ServiceOptions.ACCOMMODATION.getServiceViewModel().id;
            com.ecco.dto.ServiceViewModel service = serviceActor.getAllServicesWithProjects().getBody().
                    services.stream().filter(svm -> svm.id == serviceId).findFirst().get();
            ServiceOptions options = new ServiceOptions(service);

            this.rvm = referralActor.createMinimalReferralAndClient(null, firstNameUnique, lastNameUnique, options);
        }
    }

}
