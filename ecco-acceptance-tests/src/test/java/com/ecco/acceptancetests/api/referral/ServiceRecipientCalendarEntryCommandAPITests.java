package com.ecco.acceptancetests.api.referral;

import com.ecco.acceptancetests.api.calendar.BaseCalendarEntryCommandAPITestSupport;
import com.ecco.calendar.core.webapi.EventResource;
import com.ecco.data.client.ServiceOptions;
import com.ecco.dom.CustomEventAbstract;
import com.ecco.webApi.calendar.CalendarEntryCommandViewModel;
import com.ecco.webApi.calendar.ServiceRecipientCalendarEntryCommandViewModel;
import com.ecco.webApi.evidence.ReferralViewModel;
import com.ecco.webApi.listsConfig.ListDefinitionEntryViewModel;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;

import org.jspecify.annotations.Nullable;
import java.util.Collection;
import java.util.Objects;
import java.util.UUID;

import static org.junit.Assert.assertNotNull;

/**
 * Tests around calendar entries. This currently matches the long-standing codebase
 * which involved bringing cosmo+ecco calendaring together into an EventResource,
 * but still had no tests - particularly around saving, which works if its not changed much!
 * The current situation on get/save is noted in the description on DEV-605.
 */
@TestInstance(Lifecycle.PER_CLASS)
public class ServiceRecipientCalendarEntryCommandAPITests extends BaseCalendarEntryCommandAPITestSupport<ServiceRecipientCalendarEntryCommandViewModel> {

    private ReferralViewModel rvm;
    private final ServiceOptions service = ServiceOptions.ACCOMMODATION;

    @BeforeEach
    public void createClientAndReferral() {
        // emulating @BeforeClass but we want our injected services
        if (rvm == null) {
            rvm = referralActor.createReferralAsStarted("client", unique.clientLastNameFor("calendar"), LocalDate.now(), service);

            ownerFullName = rvm.clientDisplayName;
            ownerContactId = rvm.contactId;
            var ind = contactActor.getContactById(rvm.contactId).getBody();
            ownerCalendarId = ind.calendarId;

            attendee = userActor.createIndividualWithUser("svcrec-".concat(unique.userNameFor("attendee")), "ecco", "client", unique.clientLastNameFor("attendee"), null);

            // ensure the entries exist (get 400 status if they do)
            // the requests assume the exact entry doesn't already exist, which it might, so we just wrap in try/catch
            String listName = CustomEventAbstract.EVENTCATEGORY_LISTNAME;
            ListDefinitionEntryViewModel vm = new ListDefinitionEntryViewModel();
            vm.listName = listName;
            vm.name = "test event category 1";
            ListDefinitionEntryViewModel vm2 = new ListDefinitionEntryViewModel();
            vm2.listName = listName;
            vm2.name = "test event category 2";

            Collection<ListDefinitionEntryViewModel> list = listDefActor.ensureAndReturnListDefinitionEntry(listName, vm, vm2);
            eventCategoryId1 = list.stream().findFirst().orElseThrow().id;
            eventCategoryId2 = list.stream().skip(1).findFirst().orElseThrow().id;
        }
    }

    @Override
    protected long getContactIdFromEvent(EventResource e) {
        var srId = e.getServiceRecipientId();
        return Objects.requireNonNull(serviceRecipientActor.findSummary(srId).getBody()).contactId;
    }

    @Override
    protected void verifyCommand(UUID commandUuid) {
        var cmd = serviceRecipientActor.findCalendarCommand(commandUuid.toString());
        assertNotNull(cmd);
    }

    @Override
    protected ServiceRecipientCalendarEntryCommandViewModel createCommand(String operation, @Nullable String eventUuid) {
        CalendarEntryCommandViewModel vm = new CalendarEntryCommandViewModel(operation, eventUuid);
        return new ServiceRecipientCalendarEntryCommandViewModel(rvm.serviceRecipientId, vm);
    }

    @Override
    protected ServiceRecipientCalendarEntryCommandViewModel createCommand(String operation, int id, @Nullable String eventUuid) {
        CalendarEntryCommandViewModel vm = new CalendarEntryCommandViewModel(operation, eventUuid);
        return new ServiceRecipientCalendarEntryCommandViewModel(id, vm);
    }

}
