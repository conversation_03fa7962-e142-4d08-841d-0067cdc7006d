package com.ecco.acceptancetests.api.referral;

import com.ecco.data.client.actors.*;
import com.ecco.data.client.steps.BaseReferralStepsWebApiSupport;
import com.ecco.data.client.steps.ReferralData;
import com.ecco.dom.EvidenceGroup;
import com.ecco.dom.Referral;
import com.ecco.evidence.EvidenceTask;
import com.ecco.infrastructure.time.Clock;
import com.ecco.servicerecipient.AcceptState;
import com.ecco.webApi.contacts.AgencyViewModel;
import com.ecco.webApi.contacts.ClientViewModel;
import com.ecco.webApi.contacts.IndividualViewModel;
import com.ecco.webApi.evidence.CommentCommandViewModel;
import com.ecco.webApi.evidence.EvidenceCommandHandler;
import com.ecco.webApi.featureConfig.SessionDataViewModel;
import com.ecco.webApi.listsConfig.FundingSourceViewModel;
import com.ecco.webApi.listsConfig.ListDefinitionEntryViewModel;
import com.ecco.webApi.taskFlow.*;
import com.google.common.collect.ImmutableMap;
import com.ecco.calendar.core.webapi.EventAttendee;
import com.ecco.calendar.core.webapi.EventResource;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.joda.time.LocalDate;
import org.junit.Assert;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import java.util.function.BiFunction;
import java.util.stream.Collectors;

import static com.ecco.webApi.contacts.PersonViewModel.formatDisplayName;
import static com.ecco.dto.ChangeViewModel.changeNullTo;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.containsInAnyOrder;
import static org.junit.Assert.*;

/**
 * Handles the mundane of referral aspects / tasks / steps, in terms of
 * making the item happen with predefined, generic data for testing.
 */
public class ReferralStepsWebApiSupport extends BaseReferralStepsWebApiSupport {
    private final Clock clock = Clock.DEFAULT;
    public final DateTime nowUtc = clock.now();
    private final DateTime nowLondonWithDST = nowUtc.withZone(DateTimeZone.forID("Europe/London"));
    private AgencyViewModel deliveredBy;

    public ReferralStepsWebApiSupport(AgreementActor agreementActor, SessionDataActor sessionDataActor,
                                      ReferralActor referralActor, ClientActor clientActor,
                                      ContactActor contactActor, CalendarActor calendarActor,
                                      UserActor userActor, ListDefActor listDefActor, ServiceActor serviceActor, CacheActor cacheActor) {
        super(referralActor, agreementActor, sessionDataActor, contactActor, clientActor, calendarActor, userActor, listDefActor, serviceActor, cacheActor);
    }

    @Override
    public void registerStepDefinitions(ImmutableMap.Builder<String, BiFunction<ReferralData, String, ReferralData>> builder) {
        builder
                // taskName; command view model factory (integer in constructor); method populating the view model; method validating
                .put(SOURCE_OF_REFERRAL, simpleStep(ReferralTaskEditSourceCommandViewModel::new, this::from, this::validateFrom))
                .put(DESTINATION, simpleStep(ReferralTaskEditDestinationCommandViewModel::new, this::destination, this::validateDestination))
                .put(DATA_PROTECTION, simpleStep(ReferralTaskEditDataProtectionCommandViewModel::new, this::dataProtection, this::validateDataProtection))
                .put(EMERGENCY_DETAILS, simpleStep(ReferralTaskEditEmergencyDetailsCommandViewModel::new, this::emergencyDetails, this::validateEmergencyDetails))
                .put(REFERRAL_DETAILS, simpleStep(ReferralTaskReferralDetailsCommandViewModel::new, this::referralDetails, this::validateReferralDetails))
                .put(PENDING_STATUS, simpleStep(ReferralTaskPendingStatusCommandViewModel::new, this::pendingStatus, this::validatePendingStatus))
                .put(DELIVERED_BY, simpleStep(ReferralTaskEditDeliveredByCommandViewModel::new, this::deliveredBy, this::validateDeliveredBy))
                .put(APPROPRIATE_REFERRAL, simpleStep(ReferralTaskAppropriateReferralCommandViewModel::new, this::appropriateReferral))
                .put(ACCEPT_ON_SERVICE, simpleStep(ReferralTaskAcceptOnServiceCommandViewModel::new, this::acceptOnService))
                .put(FUNDING, simpleStep(ReferralTaskFundingCommandViewModel::new, this::funding, this::validateFunding))
                .put(SETUP_INITIAL_ASSESSMENT, simpleStep(ReferralTaskAssessmentDateCommandViewModel::new, this::initialAssessment, this::validateInitialAssessment))
                .put(START, simpleStep(ReferralTaskEditStartOnServiceCommandViewModel::new, this::start, this::validateStart))
                .put(START_ACCOMMODATION, simpleStep(ReferralTaskEditStartOnServiceCommandViewModel::new, this::start, this::validateStart))
                .put(AGREEMENT_OF_APPOINTMENTS, serviceAgreementStep())
                .put(NEEDS_ASSESSMENT, needsAssessmentStep())
                .put(THREAT_ASSESSMENT_REDUCTION, riskManagementStep())
                .put(ROTA_VISIT, rotaVisitStep())
                .put(EXIT, simpleStep(ReferralTaskExitCommandViewModel::new, this::closeOff, this::validateCloseOff));
    }

    // Override these step definitions in subclasses to change the step behaviour

    // used with ReferralOptions.withServiceAgreement
    private BiFunction<ReferralData, String, ReferralData>  serviceAgreementStep() {
        return (referralData, taskHandle) -> {
            Integer contractId = referralData.referralOptions != null ? referralData.referralOptions.withAgreementContractId : null;
            agreementActor.createAgreement(referralData.referralViewModel.serviceRecipientId,
                    nowUtc.toLocalDate(), nowUtc.toLocalDate().plusDays(59), contractId);
            // TODO: create appointment schedule
            return referralData;
        };
    }

    private BiFunction<ReferralData, String, ReferralData> rotaVisitStep() {
        return simpleStep((serviceRecipientId, taskHandle) ->
                new CommentCommandViewModel(UUID.randomUUID(), serviceRecipientId, EvidenceGroup.NEEDS, EvidenceTask.ROTA_VISIT),
                this::rotaVisit);
    }

    private BiFunction<ReferralData, String, ReferralData> needsAssessmentStep() {
        return (referralData, taskHandle) -> referralData; // Just complete the workflow task without doing anything.
    }

    private BiFunction<ReferralData, String, ReferralData> riskManagementStep() {
        return (referralData, taskHandle) -> referralData; // Just complete the workflow task without doing anything.
    }

    // Override these command processors in subclasses to change the command properties

    private ReferralTaskAppropriateReferralCommandViewModel appropriateReferral(ReferralData referralViewModel, ReferralTaskAppropriateReferralCommandViewModel c) {
        c.acceptedState = changeNullTo(AcceptState.ACCEPTED);
        c.acceptedDate = changeNullTo(nowLondonWithDST.toLocalDate());
        return c;
    }

    private ReferralTaskEditDeliveredByCommandViewModel deliveredBy(ReferralData referralData, ReferralTaskEditDeliveredByCommandViewModel cmd) {
        deliveredBy = getFirstAgencyEnsuringAtLeastOneExists();
        cmd.deliveredBy = changeNullTo(deliveredBy.contactId);
        cmd.deliveredByStartDate = changeNullTo(nowUtc);
        return cmd;
    }

    private void validateDeliveredBy(ReferralData referralData, ReferralTaskEditDeliveredByCommandViewModel cmd) {
        assertThat(referralData.referralViewModel.deliveredById).isEqualTo(deliveredBy.contactId);
        assertThat(referralData.referralViewModel.delivererAgencyName).isEqualTo(deliveredBy.companyName);
        assertThat(referralData.referralViewModel.deliveredByStartDate).isEqualByComparingTo(nowUtc.toLocalDate());

    }


    private ReferralTaskAcceptOnServiceCommandViewModel acceptOnService(ReferralData referralViewModel, ReferralTaskAcceptOnServiceCommandViewModel c) {
        c.acceptedState = changeNullTo(AcceptState.ACCEPTED);
        c.acceptedDate = changeNullTo(nowLondonWithDST.toLocalDate());
        return c;
    }

    private final DateTime fundingDecisionDate = DateTime.now();
    private final LocalDate fundingReviewDate = LocalDate.now();
    private final String paymentRef = "TEST1234";
    private final BigDecimal amount = new BigDecimal("450.45");
    private final Boolean fundingAccepted = true;
    private final BigDecimal fundingHoursOfSupport = new BigDecimal("5.00");

    private ReferralTaskFundingCommandViewModel funding(ReferralData referralData, ReferralTaskFundingCommandViewModel cmd) {

        FundingSourceViewModel fundingSource = getFirstFundingSourceEnsuringAtLeastOneExists();

        cmd.fundingDecisionDate = changeNullTo(fundingDecisionDate); // TODO: change to just a date?? Check what legacy UI does
        cmd.fundingPaymentRef = changeNullTo(paymentRef);
        cmd.fundingSource = changeNullTo(fundingSource.getId());
        cmd.fundingReviewDate = changeNullTo(fundingReviewDate);
        cmd.fundingAmount = changeNullTo(amount);
        cmd.fundingAccepted = changeNullTo(fundingAccepted);
        cmd.hoursOfSupport = changeNullTo(fundingHoursOfSupport);
        return cmd;
    }

    private void validateFunding(ReferralData referralData, ReferralTaskFundingCommandViewModel cmd) {
        FundingSourceViewModel fundingSource = getFirstFundingSourceEnsuringAtLeastOneExists();

        // check funding details have been updated
        assertThat(referralData.referralViewModel.fundingPaymentRef).isEqualTo(paymentRef);
        assertThat(referralData.referralViewModel.fundingSource).isEqualTo(fundingSource.name);
        assertThat(referralData.referralViewModel.fundingDecisionDate).isEqualByComparingTo(fundingDecisionDate.toLocalDate());
        assertThat(referralData.referralViewModel.fundingReviewDate).isEqualByComparingTo(fundingReviewDate);
        assertThat(referralData.referralViewModel.fundingAmount).isEqualTo(amount);
        assertThat(referralData.referralViewModel.fundingHoursOfSupport).isEqualTo(fundingHoursOfSupport);
        assertThat(referralData.referralViewModel.fundingAccepted).isEqualTo(fundingAccepted);
    }

    private final DateTime firstOfferedInterviewDate = DateTime.now().plusHours(2).withMillisOfSecond(0);
    private final DateTime decisionDate = DateTime.now().plusDays(1).withMillisOfSecond(0);
    private final String interviewDnaComments = "Can't sleep, clown will eat me.";
    private final Integer interviewDna = 1;
    private final String interviewSetupComments = "Clown needs to be allowed to eat people while they sleep.";
    private final String location = "The Big Tent";

    private ReferralTaskAssessmentDateCommandViewModel initialAssessment(ReferralData referralData, ReferralTaskAssessmentDateCommandViewModel cmd) {
        final Long interviewer1Id = usersWithAccessTo(referralData.referralViewModel.serviceIdAcl, referralData.referralViewModel.projectIdAcl)[0].id;

        cmd.firstOfferedInterviewDate = changeNullTo(firstOfferedInterviewDate);
        cmd.decisionDate = changeNullTo(decisionDate);
        cmd.interviewDnaComments = changeNullTo(interviewDnaComments);
        cmd.interviewDna = changeNullTo(interviewDna);
        cmd.interviewSetupComments = changeNullTo(interviewSetupComments);
        cmd.interviewer1 = changeNullTo(interviewer1Id);
        cmd.location = changeNullTo(location);

        return cmd;
    }

    private void validateInitialAssessment(ReferralData referralData, ReferralTaskAssessmentDateCommandViewModel cmd) {

        final var interviewer1 = usersWithAccessTo(referralData.referralViewModel.serviceIdAcl, referralData.referralViewModel.projectIdAcl)[0];

        assertThat(referralData.referralViewModel.firstOfferedInterviewDate).isEqualByComparingTo(firstOfferedInterviewDate.toLocalDateTime());
        assertThat(referralData.referralViewModel.decisionDate).isEqualByComparingTo(decisionDate.toLocalDateTime());
        assertThat(referralData.referralViewModel.interviewDna).isEqualByComparingTo(interviewDna);
        assertThat(referralData.referralViewModel.interviewDnaComments).isEqualTo(interviewDnaComments);
        assertThat(referralData.referralViewModel.interviewSetupComments).isEqualTo(interviewSetupComments);
        assertThat(referralData.referralViewModel.interviewer1ContactId).isEqualByComparingTo(interviewer1.id);
        assertThat(referralData.referralViewModel.interviewer2ContactId).isNull();
        assertThat(referralData.referralViewModel.interviewLocation).isEqualTo(location);

        // Check that interviews have been added to appropriate calendars
        EventResource[] allEntries = calendarActor.getEntriesByTime(decisionDate.toLocalDate(),
                                                                    decisionDate.toLocalDate().plusDays(1),
                                                                    new Long[]{referralData.referralViewModel.contactId}, Collections.emptyList()).getBody();
        assert allEntries != null;
        List<EventResource> entries = Arrays.stream(allEntries).filter(r -> r.getTitle().startsWith("Interview")).collect(Collectors.toList());
        assertEquals(entries.size(), 1);
        var interview = entries.get(0);

        assertFalse(interview.isRecurrence());
        assertFalse(interview.isRecurringEntry());
        assertFalse(interview.isAllDay());
        assertTrue(interview.hasLink("edit")); // not edit-adhoc because we're controlled from the referral

        List<EventAttendee> eventAttendees = interview.getAttendees();
        assertEquals(2, eventAttendees.size());
        List<String> names = eventAttendees.stream().map(eventAttendee -> eventAttendee.name).collect(Collectors.toList());
        Assert.assertThat("Expected attendees", names, containsInAnyOrder(referralData.referralViewModel.displayName,
                interviewer1.getName()));

        // TODO check previous dates are removed
    }

    private final LocalDate receivingServiceDate = LocalDate.now().plusDays(2);

    private ReferralTaskEditStartOnServiceCommandViewModel start(ReferralData referralData, ReferralTaskEditStartOnServiceCommandViewModel c) {
        com.ecco.serviceConfig.viewModel.IdNameViewModel firstContact = usersWithAccessTo(referralData.referralViewModel.serviceIdAcl,
                referralData.referralViewModel.projectIdAcl)[0];

        c.receivingServiceDate = changeNullTo(receivingServiceDate);
        c.allocatedWorkerContactId = changeNullTo(firstContact.id);
        return c;
    }

    private void validateStart(ReferralData referralData, ReferralTaskEditStartOnServiceCommandViewModel c) {
        //noinspection ConstantConditions

        IndividualViewModel worker = contactActor.getContactById(c.allocatedWorkerContactId.to).getBody();
        assertThat(referralData.referralViewModel.receivingServiceDate).isEqualByComparingTo(receivingServiceDate);
        assertThat(referralData.referralViewModel.supportWorkerId).isEqualByComparingTo(c.allocatedWorkerContactId.to);
        assert worker != null;
        assertThat(referralData.referralViewModel.supportWorkerDisplayName).isEqualTo(formatDisplayName(worker.firstName, worker.lastName));
    }

    private final String descriptionDetails = "Tall, ginger, big nose";
    private final String communicationNeeds = "Partial deafness in left ear";
    private final String emergencyKeyword = "Salad";
    private final String emergencyDetails = "38 Johnson Street,\nTesting\nTesting Shire\nTE1 2ST\n\nKey is hidden under the flowerpot by the front door";
    private final String risksAndConcerns = "Risky business";
    private final String medicationDetails = "Epilepsy medication";
    private final String doctorDetails = "Dr Bob Alison,\nTesting Surgery,\nMythical Lane,\nTesting,\nTesting Shire\nTE2 2ST";
    private final String dentistDetails = "Dr Hannibal Lector,\nTesting Dentistry,\nFictional Street,\nTesting,\nTesting Shire\nTE3 2ST";

    private ReferralTaskEditEmergencyDetailsCommandViewModel emergencyDetails(ReferralData referralViewModel, ReferralTaskEditEmergencyDetailsCommandViewModel cmd) {
        cmd.descriptionDetails = changeNullTo(descriptionDetails);
        cmd.communicationNeeds = changeNullTo(communicationNeeds);
        cmd.risksAndConcerns = changeNullTo(risksAndConcerns);
        cmd.emergencyKeyword = changeNullTo(emergencyKeyword);
        cmd.emergencyDetails = changeNullTo(emergencyDetails);
        cmd.medicationDetails = changeNullTo(medicationDetails);
        cmd.doctorDetails = changeNullTo(doctorDetails);
        cmd.dentistDetails = changeNullTo(dentistDetails);
        return cmd;
    }

    private void validateEmergencyDetails(ReferralData referralData, ReferralTaskEditEmergencyDetailsCommandViewModel cmd) {

        ClientViewModel cvm = clientActor.getClientById(referralData.referralViewModel.clientId).getBody();

        assert cvm != null;
        assertThat(cvm.description).isEqualTo(descriptionDetails);
        assertThat(cvm.communicationNeeds).isEqualTo(communicationNeeds);
        assertThat(cvm.risksAndConcerns).isEqualTo(risksAndConcerns);
        assertThat(cvm.emergencyKeyWord).isEqualTo(emergencyKeyword);
        assertThat(cvm.emergencyDetails).isEqualTo(emergencyDetails);
        assertThat(cvm.medicationDetails).isEqualTo(medicationDetails);
        assertThat(cvm.doctorDetails).isEqualTo(doctorDetails);
        assertThat(cvm.dentistDetails).isEqualTo(dentistDetails);
    }


    private ReferralTaskEditSourceCommandViewModel from(ReferralData referralData, ReferralTaskEditSourceCommandViewModel c) {
        return c.withSelfReferral();
    }

    private void validateFrom(ReferralData referralData, ReferralTaskEditSourceCommandViewModel cmd) {
        assertThat(referralData.referralViewModel.selfReferral).isTrue();
    }

    private ReferralTaskEditDestinationCommandViewModel destination(ReferralData referralData, ReferralTaskEditDestinationCommandViewModel c) {
        // assume there is a project in the options provided, and choose the first - neither do we don't check what is already on the referral
        int projectId = referralData.serviceOptions.getServiceViewModel().projects.iterator().next().id;
        c.projectChange = changeNullTo(projectId);
        return c;
    }

    private void validateDestination(ReferralData referralData, ReferralTaskEditDestinationCommandViewModel cmd) {
        assert referralData.referralViewModel.projectIdAcl != null;
        assert cmd.projectChange != null;
        assertThat(referralData.referralViewModel.projectIdAcl.longValue()).isEqualTo(cmd.projectChange.to.intValue());
    }

    private ReferralTaskEditDataProtectionCommandViewModel dataProtection(ReferralData referralData, ReferralTaskEditDataProtectionCommandViewModel c) {
        c.agreementDateChange = changeNullTo(nowUtc.withMillisOfSecond(0)); // Avoids database rounding to nearest second on MySQL
        c.signedDateChange = changeNullTo(nowUtc.withMillisOfSecond(0)); // Avoids database rounding to nearest second on MySQL
        // also see reddot for svg test data
        c.signatureSvgXml = "<?xml version=\"1.0\" encoding=\"UTF-8\""
                + " standalone=\"no\"?><!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\""
                + " \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">"
                + "<svg xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\" width=\"174\" height=\"38\">"
                + "<path fill=\"none\" stroke=\"#000000\" stroke-width=\"2\" stroke-linecap=\"round\" "
                + "stroke-linejoin=\"round\" d=\"M 1 30 c 0.32 0.05 11.82 2.37 18 3 c 17.32 1.76 33.57 3.08 51 4 c 8.52"
                + " 0.45 16.81 0.47 25 0 c 3.34 -0.19 6.86 -1.1 10 -2 c 1.38 -0.39 2.98 -1.09 4 -2 c 2.09 -1.88 5.2"
                + " -4.51 6 -7 c 1.56 -4.89 2.21 -12.71 2 -18 c -0.09 -2.23 -3.44 -6.67 -3 -7 c 0.46 -0.35 4.56 2.97 7"
                + " 4 c 3.82 1.61 7.87 3.02 12 4 c 8.64 2.05 17.21 3.02 26 5 c 4.83 1.09 14 4 14 4\"/></svg>";
        return c;
    }

    private void validateDataProtection(ReferralData referralData, ReferralTaskEditDataProtectionCommandViewModel c) {
        assertThat(referralData.referralViewModel.dataProtectionAgreementDate.withMillisOfSecond(0)).isEqualByComparingTo(nowUtc.toLocalDateTime().withMillisOfSecond(0));
        assertThat(referralData.referralViewModel.dataProtectionSignedId).isNotNull();
    }

    private final LocalDate receivedDate = nowUtc.toLocalDate().minusDays(5);

    private ReferralTaskReferralDetailsCommandViewModel referralDetails(ReferralData referralData, ReferralTaskReferralDetailsCommandViewModel c) {
        return c.withReceivedDate(receivedDate);
    }

    private void validateReferralDetails(ReferralData referralData, ReferralTaskReferralDetailsCommandViewModel c) {
        assertThat(referralData.referralViewModel.receivedDate).isEqualTo(receivedDate);
    }

    private ReferralTaskPendingStatusCommandViewModel pendingStatus(ReferralData referralData, ReferralTaskPendingStatusCommandViewModel c) {
        c.pendingStatus = changeNullTo(getFirstPendingStatus().id);
        return c;
    }

    private void validatePendingStatus(ReferralData referralData, ReferralTaskPendingStatusCommandViewModel c) {
        var firstPendingStatus = getFirstPendingStatus();
        assertThat(referralData.referralViewModel.pendingStatusId).isEqualTo(firstPendingStatus.id);
    }

    private ReferralTaskExitCommandViewModel closeOff(ReferralData referralData, ReferralTaskExitCommandViewModel c) {
        c.exitedDateChange = changeNullTo(referralData.referralOptions.withExitedDate);
        return c;
    }

    private void validateCloseOff(ReferralData referralData, ReferralTaskExitCommandViewModel cmd) {
        assert cmd.exitedDateChange != null;
        assertThat(referralData.referralViewModel.exitedDate).isEqualTo(cmd.exitedDateChange.to);
    }

    private CommentCommandViewModel rotaVisit(ReferralData vm, CommentCommandViewModel c) {

        var sessionData = sessionDataActor.getSessionData().getBody();
        assert sessionData != null;
        var svcCat = serviceActor.getServiceCategorisation(sessionData, vm.referralViewModel.serviceAllocationId);

        var commenttype = ensureCommentType(svcCat.serviceTypeId);
        c.workDate = changeNullTo(nowUtc.toLocalDateTime());
        if (vm.referralOptions != null) {
            c.eventId = changeNullTo(vm.referralOptions.withRotaVisitEventRef);
            if (vm.referralOptions.withEventStatusId != null) {
                c.eventStatusId = changeNullTo(vm.referralOptions.withEventStatusId);
            }
            if (vm.referralOptions.withRotaVisitEventStatusRateId != null) {
                c.eventStatusRateId = changeNullTo(vm.referralOptions.withRotaVisitEventStatusRateId);
            }
            if (vm.referralOptions.withRotaVisitComment != null) {
                c.comment = changeNullTo(vm.referralOptions.withRotaVisitComment);
            }
        }
        c.minsSpent = changeNullTo(45);
        c.commentTypeId = changeNullTo(commenttype.id);
        c.mileageDuring = changeNullTo(BigDecimal.valueOf(25));
        c.mileageTo = changeNullTo(BigDecimal.valueOf(15));
        c.minsTravel = changeNullTo(22);
//        c.eventId = changeNullTo(a real event);
        return c;
    }

    private ListDefinitionEntryViewModel ensureCommentType(long serviceTypeId) {
        ListDefinitionEntryViewModel vm = new ListDefinitionEntryViewModel();
        vm.listName = EvidenceCommandHandler.COMMENTTYPE_PREFIX.concat(String.valueOf(serviceTypeId));
        vm.name = "some comment type";
        return listDefActor.ensureAndReturnListDefinitionEntry(vm.listName, vm).iterator().next();
    }

    private ListDefinitionEntryViewModel getFirstPendingStatus() {
        SessionDataViewModel svm = sessionDataActor.getSessionData().getBody();
        assert svm != null;
        if (svm.listDefinitions.get(Referral.PENDINGSTATUS_LISTNAME).isEmpty()) {
            ListDefinitionEntryViewModel vm = new ListDefinitionEntryViewModel();
            vm.listName = Referral.PENDINGSTATUS_LISTNAME;
            vm.name = "some pending status";
            return listDefActor.ensureAndReturnListDefinitionEntry(vm.listName, vm).iterator().next();
        } else {
            return svm.listDefinitions.get(Referral.PENDINGSTATUS_LISTNAME).iterator().next();
        }
    }


    private AgencyViewModel getFirstAgencyEnsuringAtLeastOneExists() {
        AgencyViewModel[] agencies = contactActor.getAllAgencies().getBody();

        assert agencies != null;
        if (agencies.length == 0) {
            AgencyViewModel agency = new AgencyViewModel();
            agency.companyName = "Hogwarts Police Service";
            contactActor.createAgency(agency);
            agencies = contactActor.getAllAgencies().getBody();
        }
        return agencies[0];
    }

    private FundingSourceViewModel getFirstFundingSourceEnsuringAtLeastOneExists() {
        SessionDataViewModel svm = sessionDataActor.getSessionData().getBody();
        assert svm != null;

        if (!svm.fundingSources.iterator().hasNext()) {
            // clear cache and try again
            cacheActor.clearCaches(); // otherwise Local Authority already exists
            SessionDataViewModel svm2 = sessionDataActor.getSessionData().getBody();
            if (!svm2.fundingSources.iterator().hasNext()) {
                FundingSourceViewModel fvm = new FundingSourceViewModel();
                fvm.name = "Local Authority";
                sessionDataActor.createFundingSource(fvm);

                cacheActor.clearCaches(); // otherwise Local Authority already exists
                svm = sessionDataActor.getSessionData().getBody(); // Must re-fetch to get the generated id
            }
        }

        return svm.fundingSources.iterator().next();
    }

    private com.ecco.serviceConfig.viewModel.IdNameViewModel[] usersWithAccessTo(Long serviceId, Long projectId) {
        return userActor.usersWithAccessTo(serviceId, projectId).getBody();
    }

}
