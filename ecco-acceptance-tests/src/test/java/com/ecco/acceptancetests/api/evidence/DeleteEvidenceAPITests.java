package com.ecco.acceptancetests.api.evidence;

import com.ecco.data.client.ServiceOptions;
import com.ecco.dom.EvidenceGroup;
import com.ecco.evidence.EvidenceTask;
import com.ecco.infrastructure.config.web.ConvertersConfig;
import com.ecco.serviceConfig.dom.TaskDefinition;
import com.ecco.webApi.evidence.BaseWorkViewModel;
import com.ecco.dto.ChangeViewModel;
import com.ecco.webApi.evidence.CommentCommandViewModel;
import com.ecco.webApi.viewModels.DeleteEvidenceCommandViewModel;
import com.ecco.webApi.viewModels.DeleteRequestEvidenceCommandViewModel;
import com.ecco.webApi.viewModels.Result;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.util.StdDateFormat;
import org.joda.time.Instant;
import org.joda.time.LocalDateTime;
import org.junit.jupiter.api.Test;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpClientErrorException.BadRequest;

import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Stream;

import static org.junit.Assert.*;

@SuppressWarnings({"ConstantConditions", "OptionalGetWithoutIsPresent", "unused", "SameParameterValue"})
public class DeleteEvidenceAPITests extends BaseReferralCommandAPITests {

    private enum Discriminator {
        SUPPORT, RISK
    }

    static ObjectMapper mapper = ConvertersConfig.getObjectMapper()
                    .setDateFormat(new StdDateFormat());

    private final EvidenceGroup evidenceGroupSupport = EvidenceGroup.NEEDS;
    private final EvidenceTask evidenceTaskSupport = EvidenceTask.NEEDS_ASSESSMENT;
    private final EvidenceGroup evidenceGroupRisk = EvidenceGroup.THREAT;
    private final EvidenceTask evidenceTaskRisk = EvidenceTask.THREAT_ASSESSMENT_REDUCTION;

    public DeleteEvidenceAPITests() {
        super(ServiceOptions.ACCOMMODATION);
    }

    @Test
    public void deleteEvidenceRequest() {
        BaseWorkViewModel workViewModel = createEvidence(rvm.serviceRecipientId, "evidence to request delete", evidenceTaskSupport, evidenceGroupSupport);
        deleteRequest(workViewModel.serviceRecipientId, workViewModel.id, false, workViewModel);

        // verify its marked as requestedDelete
        BaseWorkViewModel deleteRequested = findSupportWorkByUuidAndServiceRecipient(workViewModel.serviceRecipientId, workViewModel.id).get();
        assertTrue("Expect to have requestedDelete", deleteRequested.requestedDelete);
    }

    @Test
    public void deleteEvidenceRequestRevoke() {
        BaseWorkViewModel workViewModel = createEvidence(rvm.serviceRecipientId, "evidence to request delete", evidenceTaskSupport, evidenceGroupSupport);
        deleteRequest(workViewModel.serviceRecipientId, workViewModel.id, false, workViewModel);

        deleteRequest(workViewModel.serviceRecipientId, workViewModel.id, true, workViewModel);

        // verify its marked as requestedDelete
        BaseWorkViewModel deleteRequested = findSupportWorkByUuidAndServiceRecipient(workViewModel.serviceRecipientId, workViewModel.id).get();
        assertFalse("Expect to have no requestedDelete", deleteRequested.requestedDelete);
    }

    @Test
    public void deleteEvidence() {

        // GIVEN evidence requested delete
        BaseWorkViewModel workViewModel = createEvidence(rvm.serviceRecipientId, "evidence to delete", evidenceTaskSupport, evidenceGroupSupport);
        UUID requestDeletionUuid = deleteRequest(workViewModel.serviceRecipientId, workViewModel.id, false, workViewModel);

        // WHEN delete
        UUID deleteUuid = delete(workViewModel, requestDeletionUuid, "am fine with this");

        // THEN evidence doesn't exist, and delete command does
        {
            assertFalse("work found when it should have been deleted", this.findSupportWorkByUuidAndServiceRecipient(workViewModel.serviceRecipientId, workViewModel.id).isPresent());
            verifyDeleteRequestCommand(workViewModel.serviceRecipientId, workViewModel.id, requestDeletionUuid);
        }

    }

    @Test
    public void deleteEvidenceRiskRequest() {
        BaseWorkViewModel workViewModel = createEvidence(rvm.serviceRecipientId, "evidence to request risk delete", evidenceTaskRisk, evidenceGroupRisk);
        deleteRequest(workViewModel.serviceRecipientId, workViewModel.id, false,
                Discriminator.RISK, evidenceGroupRisk, evidenceTaskRisk, workViewModel);

        // verify its marked as requestedDelete
        BaseWorkViewModel deleteRequested = findRiskWorkByUuidAndServiceRecipient(workViewModel.serviceRecipientId, workViewModel.id).get();
        assertTrue("Expect to have requestedDelete", deleteRequested.requestedDelete);
    }

    @Test
    public void deleteEvidenceRisk() {

        // GIVEN evidence requested delete
        BaseWorkViewModel workViewModel = createEvidence(rvm.serviceRecipientId, "evidence to delete risk", evidenceTaskRisk, evidenceGroupRisk);
        UUID requestDeletionUuid = deleteRequest(workViewModel.serviceRecipientId, workViewModel.id, false,
                Discriminator.RISK, evidenceGroupRisk, evidenceTaskRisk, workViewModel);

        // WHEN delete
        UUID deleteUuid = delete(workViewModel, requestDeletionUuid, "am fine with this", evidenceGroupRisk, evidenceTaskRisk);

        // THEN evidence doesn't exist, and delete command does
        {
            assertFalse("work found when it should have been deleted", this.findRiskWorkByUuidAndServiceRecipient(workViewModel.serviceRecipientId, workViewModel.id).isPresent());
            verifyDeleteRequestCommand(workViewModel.serviceRecipientId, workViewModel.id, requestDeletionUuid);
        }

    }

    @Test
    public void deleteEvidenceFailsWhenInvalidRequestUuid() {

        // GIVEN evidence
        BaseWorkViewModel workViewModel = createEvidence(rvm.serviceRecipientId, "request delete doesn't exist", evidenceTaskSupport, evidenceGroupSupport);

        // WHEN delete request is made up
        UUID requestDeletionUuid = UUID.randomUUID();

        // WHEN delete for real
        assertThrows(HttpClientErrorException.class, () ->
                delete(workViewModel, requestDeletionUuid, "am fine with this"));
    }

    @Test
    public void deleteEvidenceFailsWhenRequestRevoked() {

        BaseWorkViewModel workViewModel = createEvidence(rvm.serviceRecipientId, "request delete revoked", evidenceTaskSupport, evidenceGroupSupport);

        // WHEN delete request is made up (its currently valid to have a revoke on its own)
        UUID requestDeletionUuid = deleteRequest(rvm.serviceRecipientId, workViewModel.id, true, workViewModel);

        // WHEN delete for real
        assertThrows(HttpClientErrorException.class, () ->
                delete(workViewModel, requestDeletionUuid, "am fine with this"));
    }

    @Test
    public void deleteEvidenceFailsWhenLatestRequestRevoked() {

        BaseWorkViewModel workViewModel = createEvidence(rvm.serviceRecipientId, "last request delete revoked", evidenceTaskSupport, evidenceGroupSupport);

        // WHEN delete request is made up (its currently valid to have a revoke on its own)
        UUID requestDeletionUuid = deleteRequest(rvm.serviceRecipientId, workViewModel.id, false, workViewModel);
        // WHEN revoked
        deleteRequest(rvm.serviceRecipientId, workViewModel.id, true, workViewModel);

        // WHEN delete for real
        assertThrows(BadRequest.class, () ->
                delete(workViewModel, requestDeletionUuid, "am fine with this"));
    }

    private BaseWorkViewModel createEvidence(int serviceRecipientId, String note, EvidenceTask evidenceTask, EvidenceGroup evidenceGroup) {
        // could use supportPlanSteps (SupportPlanContext) but that is only for the UI driver currently
        // could also use workflow idea from ReferralAPITests - but the needs/support is stubbed out (since we have api tests already)
        // so we use the api tests from BaseEvidenceCommandAPITests
        UUID workUuid = UUID.randomUUID(); // should be diff work uuid per test
        LocalDateTime workDate = LocalDateTime.now().minusDays(5);
        CommentCommandViewModel ccvm = new CommentCommandViewModel(workUuid, serviceRecipientId, evidenceGroup, evidenceTask);
        ccvm.timestamp = Instant.now();
        ccvm.comment = ChangeViewModel.changeNullTo(note);
        ccvm.workDate = ChangeViewModel.changeNullTo(workDate);
        ccvm.minsSpent = ChangeViewModel.changeNullTo(53);
        commandActor.executeCommand(ccvm);

        var type = taskDefinitions.stream().filter(t -> t.name.equalsIgnoreCase(evidenceTask.getTaskName())).findFirst().get().type;
        if (TaskDefinition.Type.EVIDENCE_SUPPORT.name().equalsIgnoreCase(type)) {
            List<? extends BaseWorkViewModel> supportWorks = supportEvidenceActor.findAllSupportWorkSummaryByServiceRecipientId(serviceRecipientId, evidenceGroup).getBody().getContent();
            return supportWorks.get(0);
        } if (TaskDefinition.Type.EVIDENCE_RISK.name().equalsIgnoreCase(type)) {
            List<? extends BaseWorkViewModel> riskWorks = riskEvidenceActor.findAllThreatWorkSummaryByServiceRecipientId(serviceRecipientId).getBody().getContent();
            return riskWorks.get(0);
        } else {
            throw new IllegalArgumentException("not implemented that evidence group");
        }
    }

    private Optional<? extends BaseWorkViewModel> findSupportWorkByUuidAndServiceRecipient(int serviceRecipientId, UUID uuid) {
        List<? extends BaseWorkViewModel> work =
                supportEvidenceActor.findAllSupportWorkSummaryByServiceRecipientId(serviceRecipientId, evidenceGroupSupport).getBody().getContent();
        Stream<? extends BaseWorkViewModel> filtered = work.stream().filter(input -> uuid.equals(input.id));
        return filtered.findFirst();
    }
    private Optional<? extends BaseWorkViewModel> findRiskWorkByUuidAndServiceRecipient(int serviceRecipientId, UUID uuid) {
        List<? extends BaseWorkViewModel> work =
                riskEvidenceActor.findAllThreatWorkSummaryByServiceRecipientId(serviceRecipientId).getBody().getContent();
        Stream<? extends BaseWorkViewModel> filtered = work.stream().filter(input -> uuid.equals(input.id));
        return filtered.findFirst();
    }

    private void verifyDeleteRequestCommand(int serviceRecipientId, UUID workUuid, UUID deleteRequestUuid) {
        // NB We could use a view model on the receiving side of the request.
        // However, currently there is no property on the view model to indicate its a delete,
        // so we just inspect the json.
        List<DeleteRequestEvidenceCommandViewModel> commands = serviceRecipientActor.findDeleteEvidenceRequestCommands(serviceRecipientId).getBody();
        DeleteRequestEvidenceCommandViewModel matched = commands.stream().filter(cmd -> cmd.uuid.equals(deleteRequestUuid)).findFirst().get();
        assertNotNull("Expect to see delete command", matched);
                // Matchers.iterableWithSize(1));
        assertEquals("Expect to see same serviceRecipientId", matched.serviceRecipientId.intValue(), serviceRecipientId);
        assertEquals("Expect to see same uuid", matched.workUuid.toString(), workUuid.toString());
    }

    public UUID deleteRequest(int serviceRecipientId, UUID workUuid, boolean revoke, BaseWorkViewModel evidenceViewModel) {
        return deleteRequest(serviceRecipientId, workUuid, revoke, Discriminator.SUPPORT, evidenceGroupSupport, evidenceTaskSupport, evidenceViewModel);
    }

    public UUID deleteRequest(int serviceRecipientId, UUID workUuid, boolean revoke, Discriminator discriminator,
                              EvidenceGroup evidenceGroup, EvidenceTask evidenceTask, BaseWorkViewModel evidenceViewModel) {
        String modelStr = null;
        try {
            modelStr = mapper.writeValueAsString(evidenceViewModel);
        } catch (JsonProcessingException e) {
            fail();
        }
        DeleteRequestEvidenceCommandViewModel vm =
                new DeleteRequestEvidenceCommandViewModel(discriminator.toString(), serviceRecipientId, workUuid, evidenceGroup, evidenceTask, modelStr);
        vm.reason = "incorrect entry";
        vm.revoke = revoke;
        Result result = commandActor.executeCommand(vm).getBody();
        assertEquals("updated", result.getMessage());

        // retrieve delete request UUID - TODO isn't this the same as what we sent - or perhaps its optional?
        List<DeleteRequestEvidenceCommandViewModel> cmdVms = serviceRecipientActor.findDeleteEvidenceRequestCommands(serviceRecipientId).getBody();
        DeleteRequestEvidenceCommandViewModel latestDeleteRequestCmd = cmdVms.stream()
                .filter(evm -> evm.workUuid.equals(workUuid))
                .reduce((prev, curr) -> prev.timestamp.isAfter(curr.timestamp) ? prev : curr).get();
        return latestDeleteRequestCmd.uuid;
    }

    private UUID delete(BaseWorkViewModel evidenceViewModel, UUID requestDeletionUuid, String reason) {
        return delete(evidenceViewModel, requestDeletionUuid, reason, evidenceGroupSupport, evidenceTaskSupport);
    }
    private UUID delete(BaseWorkViewModel evidenceViewModel, UUID requestDeletionUuid, String reason, EvidenceGroup evidenceGroup, EvidenceTask evidenceTask) {
        String jsonViewModel = null;
        try {
            jsonViewModel = mapper.writeValueAsString(evidenceViewModel);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }

        final DeleteEvidenceCommandViewModel vm =
                new DeleteEvidenceCommandViewModel(evidenceViewModel.serviceRecipientId, evidenceViewModel.id, requestDeletionUuid,
                        evidenceGroup, evidenceTask, jsonViewModel);
        vm.reason = reason;
        Result result = commandActor.executeCommand(vm).getBody();
        assertEquals("deleted", result.getMessage());
        assertNull(result.getId());
        return vm.uuid;
    }

}
