package com.ecco.acceptancetests.api.evidence;

import com.ecco.acceptancetests.api.serviceConfig.TaskDefinitionEntrySettingCommandAPITests;
import com.ecco.data.client.ServiceOptions;
import com.ecco.dom.EvidenceGroup;
import com.ecco.dto.ChangeViewModel;
import com.ecco.dto.ServiceViewModel;
import com.ecco.evidence.EvidenceTask;
import com.ecco.webApi.evidence.*;
import com.ecco.webApi.serviceConfig.TaskDefinitionEntrySettingCommandViewModel;

import java.util.*;

import static com.ecco.data.client.ServiceOptions.DEMO_ALL;
import static org.junit.Assert.*;

import org.junit.jupiter.api.Test;

public class RiskHistoryAPITests extends RiskBaseAPITests {

    public RiskHistoryAPITests() {
        super(EvidenceGroup.THREAT, EvidenceTask.THREAT_ASSESSMENT, ServiceOptions.DEMO_ALL);
    }

    @Test
    public void commentRisk_happyPath() {
        // GIVEN createUniqueClientAndReferral on 'service'

        // WHEN record a piece of work
        UUID threatWorkUuid = UUID.randomUUID();
        {
            // create risk entry with no handling of risk
            CommentCommandViewModel ccvm2 = createCommentCommandViewModel(threatWorkUuid, getServiceRecipientId(),
                    evidenceGroup, defaultEvidenceTask, "children are amazing", workDateUpTo50DaysInPast, 10 + new Random().nextInt(145));
            commandActor.executeCommand(ccvm2);
        }

        // THEN there is history on the srId, on the right service
        {
            // history exists on srId
            List<EvidenceThreatWorkViewModel> work = findWorkSummaryByServiceRecipientId();
            Optional<EvidenceThreatWorkViewModel> result = work.stream()
                    .filter(input -> threatWorkUuid.equals(input.id))
                    .findFirst();
            assertTrue(result.isPresent());
            assertEquals(getServiceRecipientId(), result.get().serviceRecipientId.intValue());

            // history exists on service
            long resultServiceId = referralActor.getReferralSummaryByServiceRecipientId(getServiceRecipientId()).getBody().serviceIdAcl;
            assertEquals(service.getServiceViewModel().id.longValue(), resultServiceId);
        }
    }

    @Test
    public void commentRiskSameClient_happyPath() {

        // *****
        // GIVEN a new service/type (to avoid config changes impacting other tests)
        String typeName = unique.nameFor("threat test");
        long serviceTypeId = serviceTypeActor.createServiceType(typeName, false).id;
        TaskDefinitionEntrySettingCommandAPITests.createServiceTypeTasks(serviceTypeId, commandActor);
        commandActor.executeCommand(serviceActor.createServiceCommand(typeName, serviceTypeId)).getBody().getId();
        ServiceViewModel svm = serviceActor.getAllServicesWithProjects().getBody().services.stream()
                .filter(s -> s.getName().equalsIgnoreCase(typeName)).findFirst().get();

        // *****
        // WHEN set scope to client
        var scopeCmd = new TaskDefinitionEntrySettingCommandViewModel(svm.serviceTypeId, EvidenceTask.THREAT_ASSESSMENT.getTaskName(), "scope");
        scopeCmd.valueChange = ChangeViewModel.changeNullTo("client");
        commandActor.executeCommand(scopeCmd);
        cacheActor.clearServiceTypeCaches(); // for safety

        // *****
        // GIVEN we create one client with two referrals
        // we ignore the default createUniqueClientAndReferral on 'service' because its tricky to re-use the same client
        String firstNameUnique = unique.clientFirstNameFor("Risk");
        String lastNameUnique = unique.clientLastNameFor("Test");
        ReferralViewModel vm1 = referralActor.createMinimalReferralAndClient("THREAT1", firstNameUnique, lastNameUnique, DEMO_ALL);
        ReferralViewModel vm2 = referralActor.createMinimalReferralAndClient("THREAT1", firstNameUnique, lastNameUnique, DEMO_ALL);
        assertEquals(vm1.clientId, vm2.clientId);

        // *****
        // WHEN record a piece of work on r1
        UUID threatWorkUuidR1 = UUID.randomUUID();
        // create risk entry with no handling of risk
        CommentCommandViewModel ccvm1 = createCommentCommandViewModel(threatWorkUuidR1, vm1.serviceRecipientId,
                evidenceGroup, defaultEvidenceTask, "first risk work", workDateUpTo50DaysInPast, 10 + new Random().nextInt(145));
        commandActor.executeCommand(ccvm1);

        // *****
        // WHEN record a piece of work on r2
        UUID threatWorkUuidR2 = UUID.randomUUID();
        // create risk entry with no handling of risk
        CommentCommandViewModel ccvm2 = createCommentCommandViewModel(threatWorkUuidR2, vm2.serviceRecipientId,
                evidenceGroup, defaultEvidenceTask, "second risk work", workDateUpTo50DaysInPast, 10 + new Random().nextInt(145));
        commandActor.executeCommand(ccvm2);

        // *****
        // THEN there is BOTH history items back from the same srId because its the same client
        List<EvidenceThreatWorkViewModel> work = riskEvidenceActor.findAllThreatWorkSummaryByServiceRecipientId(vm1.serviceRecipientId).getBody().getContent();
        Optional<EvidenceThreatWorkViewModel> workR1 = work.stream()
                .filter(input -> threatWorkUuidR1.equals(input.id))
                .findFirst();
        assertTrue(workR1.isPresent());
        Optional<EvidenceThreatWorkViewModel> workR2 = work.stream()
                .filter(input -> threatWorkUuidR2.equals(input.id))
                .findFirst();

        // TODO FAILS currently since there is an existing implementation using a different API by clientId - see c1ceb7bf
        // TODO We should change the test to use the other API
        // assertTrue(workR2.isPresent());

        assertEquals(vm1.serviceRecipientId.intValue(), workR1.get().serviceRecipientId.intValue());
        // TODO FAILS currently... as above
        //assertEquals(vm2.serviceRecipientId.intValue(), workR2.get().serviceRecipientId.intValue());
    }

}
