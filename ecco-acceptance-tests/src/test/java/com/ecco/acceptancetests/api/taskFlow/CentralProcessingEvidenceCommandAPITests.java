package com.ecco.acceptancetests.api.taskFlow;

import static com.ecco.acceptancetests.api.taskFlow.CentralProcessingAllocateCommandAPITests.matchServiceName;
import static com.ecco.dom.EvidenceGroup.NEEDS;
import static java.util.UUID.randomUUID;
import static org.hamcrest.Matchers.is;
import static org.junit.Assert.*;

import java.util.*;
import java.util.stream.Stream;

import com.ecco.data.client.ServiceOptions;
import com.ecco.dto.ChangeViewModel;
import com.ecco.evidence.EvidenceTask;
import com.ecco.serviceConfig.viewModel.ServiceCategorisationViewModel;
import com.ecco.webApi.featureConfig.SessionDataViewModel;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import com.ecco.acceptancetests.api.evidence.BaseEvidenceCommandAPITests;
import com.ecco.dto.ServiceViewModel;
import com.ecco.serviceConfig.viewModel.OutcomeViewModel;
import com.ecco.webApi.evidence.*;
import com.ecco.webApi.taskFlow.ReferralTaskAllocateServiceCommandViewModel;
import com.ecco.webApi.viewModels.Result;
import com.ecco.webApi.viewModels.ServicesViewModel;

public class CentralProcessingEvidenceCommandAPITests extends BaseEvidenceCommandAPITests<EvidenceSupportWorkViewModel> {

    private static String ACCOMMODATION_CHILD = "accommodation-child";
    private static String ACCOMMODATION_CHILD_CHILD = "accommodation-child-child";
    private static Integer actionDefId;

    public CentralProcessingEvidenceCommandAPITests() {
        super(NEEDS, EvidenceTask.NEEDS_ASSESSMENT, ServiceOptions.ACCOMMODATION);
    }

    private UUID workUuidChild;
    private UUID workUuidChildOfChild;
    private ReferralViewModel child;
    private ReferralViewModel childOfChild;
    private SessionDataViewModel sessionData;

    @Override
    @BeforeEach
    public void createUniqueClientAndReferral() {
        sessionData = sessionDataActor.getSessionData().getBody();

        super.createUniqueClientAndReferral();
        workUuidChild = randomUUID();
        workUuidChildOfChild = randomUUID();

        // create child referral
        if (child == null) {
            ServiceViewModel svm = getServiceViewModel(ACCOMMODATION_CHILD);

            // we create this because central processing needs a 'childService' flag on the service type to save its work
            // see ReferralEvidenceCommandHandler.handleInternal
            if (svm == null) {
                createChildServiceTypeAndService(ACCOMMODATION_CHILD, ACCOMMODATION_CHILD);
                svm = getServiceViewModel(ACCOMMODATION_CHILD);
            }

            assert svm != null;
            ServiceViewModel allocateTo = CentralProcessingAllocateCommandAPITests.createViewModel(svm.name, svm.id, null, null);
            child = allocateAndReturnChild(allocateTo, getServiceRecipientId());
        }

        // create child of the child referral
        if (childOfChild == null) {
            ServiceViewModel svm = getServiceViewModel(ACCOMMODATION_CHILD_CHILD);

            var svcCats = serviceActor.getAllServiceCategorisations().getBody();
            var svcCat = Arrays.stream(svcCats).filter(s -> child.serviceAllocationId.equals(s.id)).findFirst().get();
            if (svm == null) {
                createChildService(svcCat.getServiceTypeId(), ACCOMMODATION_CHILD_CHILD);
                svm = getServiceViewModel(ACCOMMODATION_CHILD_CHILD);
            }

            assert svm != null;
            ServiceViewModel allocateTo = CentralProcessingAllocateCommandAPITests.createViewModel(svm.name, svm.id, null, null);
            childOfChild = allocateAndReturnChild(allocateTo, child.serviceRecipientId);
        }
    }

    @Test
    @Disabled("central processing trigger (childService) has been removed - see c0ba8d5e")
    public void commentWorkSavesAgainstParent() {

        // new evidence on CHILD referral
        CommentCommandViewModel childVm = createCommentCommandViewModel(workUuidChild, child.serviceRecipientId,
                evidenceGroup, defaultEvidenceTask,
                "this is my CHILD piece of work", workDateUpTo50DaysInPast, 10 + new Random().nextInt(145));
        commandActor.executeCommand(childVm);

        EvidenceSupportWorkViewModel parentSummary = findWorkByUuidAndServiceRecipient(getServiceRecipientId(), workUuidChild);
        assert childVm.comment != null;
        assertEquals(childVm.comment.to, parentSummary.comment);
        assertEquals(workUuidChild, parentSummary.id);
    }

    /**
     * Central processing and 'allocate to' can be independent.
     * 'allocate to' can simply daisy chain a referral by setting the parentId
     * with no other intelligence currently associated with it.
     * Central processing uses 'allocate to' but also sets the servicetype as a
     * childService which triggers the loading and saving of data against the parent
     * The actual client side commands are not aware of the parent/child relationship,
     * its the client display and the server side command handlers.
     * HOWEVER, central processing on the client side allows recursion
     * (see service-config-domain.ts, addParentConfig) BUT the server side
     * saving only saves against the immediate parent.
     * This works well for one client (ECCO-1620) where each child created
     * saves work against new smart steps but display the history of the complete chain.
     * So we need to ensure there is a test before this changes for another client and
     * breaks the original work.
     */
    @Test
    public void commentWorkSavesAgainstImmediateParentOnly() {

        // new evidence on CHILD of CHILD referral
        CommentCommandViewModel childVm = createCommentCommandViewModel(workUuidChildOfChild, childOfChild.serviceRecipientId,
                evidenceGroup, defaultEvidenceTask,
                "this is my CHILD OF CHILD piece of work", workDateUpTo50DaysInPast, 10 + new Random().nextInt(145));
        commandActor.executeCommand(childVm);
        assert childVm.comment != null;

        // get the top most parent
        assertThrows(NoSuchElementException.class, () -> findWorkByUuidAndServiceRecipient(getServiceRecipientId(), workUuidChildOfChild));
    }

    @Test
    @Disabled("central processing trigger (childService) has been removed - see c0ba8d5e")
    public void goalWorkSavesAgainstParent() {

        LocalDate target = new LocalDate(2015,4,7);
        BaseGoalUpdateCommandViewModel updateCmd =
                new GoalUpdateCommandViewModel(workUuidChild, child.serviceRecipientId, evidenceGroup,
                        defaultEvidenceTask, actionDefId, randomUUID(), null);
        updateCmd.targetDateChange = new ChangeViewModel<>();
        updateCmd.targetDateChange.from = null;
        updateCmd.targetDateChange.to = target;
        commandActor.executeCommand(updateCmd);

        EvidenceSupportWorkViewModel parentSummary = findWorkByUuidAndServiceRecipient(getServiceRecipientId(), workUuidChild);
        assertEquals(actionDefId.intValue(), parentSummary.actions.get(0).actionId.intValue());
        assertEquals(updateCmd.targetDateChange.to, target);
    }

    // NB now that the handler processes the allocationIds in turn, it's really down to the client/user to choose whatever
    // so we don't need to throw illegal exceptions - that was when we created one referral when possibly selecting many services
    public static List<Integer> allocateToIds(ServiceCategorisationViewModel[] svcCats, ServiceViewModel... svms) {
        var matches = Arrays.stream(svcCats)
                .filter(sc -> {
                    var match = Arrays.stream(svms).filter(svm -> {
                        var serviceMatch = svm.id.longValue() == sc.serviceId;
                        var projectMatch = svm.getProjects() != null ? svm.getProjects().stream().anyMatch(p -> p != null ? Objects.equals(p.id.longValue(), sc.projectId) : sc.projectId == null) : sc.projectId == null;
                        //log.info("matching: "+sc.id+" on service/project: " + svm.id + " / " + projectMatch);
                        //log.info("matched: " + serviceMatch + " / " + projectMatch);
                        return serviceMatch && projectMatch;
                    });
                    var count = match.count();
                    if (count > 1) {
                        // we did ensure we only have one project selected per service
                        // there is no reason why not - except that multiple simultaneous referrals per service is not normal
                        throw new IllegalArgumentException("multiple projects per service are not currently supported");
                    }
                    return count == 1;
                })
                .map(sc -> sc.id)
                .toList();

        if (matches.isEmpty()) {
            throw new IllegalArgumentException("Service not found in sessionData: " + Arrays.stream(svms).map(s -> s.id + ", ").toList());
        }

        return matches;
    }

    private ReferralViewModel allocateAndReturnChild(ServiceViewModel allocateTo, int serviceRecipientId) {

        // create child referral
        ReferralTaskAllocateServiceCommandViewModel tvm = new ReferralTaskAllocateServiceCommandViewModel(serviceRecipientId,
                allocateToIds(serviceActor.getAllServiceCategorisations().getBody(), allocateTo));
        Result result = commandActor.executeCommand(tvm).getBody();
        assertTrue(result.isCommandSuccessful());
        assertNotNull(result.getId());

        // get child referral
        ReferralViewModel[] children = referralActor.getReferralsByParentServiceRecipientId(serviceRecipientId)
                .getBody();
        return children[0];
    }

    private ServiceViewModel getServiceViewModel(String serviceName) {
        ServicesViewModel allServices = serviceActor.getAllServicesWithProjects().getBody();
        Stream<ServiceViewModel> filtered = allServices.services.stream()
                .filter(matchServiceName(serviceName));
        Optional<ServiceViewModel> first = filtered.findFirst();
        return first.isPresent() ? first.get() : null;
    }

    @Override
    protected List<EvidenceSupportWorkViewModel> findWorkSummaryByServiceRecipientId() {
        return supportEvidenceActor.findAllSupportWorkSummaryByServiceRecipientId(getServiceRecipientId(), NEEDS).getBody().getContent();
    }

    private EvidenceSupportWorkViewModel findWorkByUuidAndServiceRecipient(int serviceRecipientId, UUID uuid) {
        List<EvidenceSupportWorkViewModel> work =
                supportEvidenceActor.findAllSupportWorkSummaryByServiceRecipientId(serviceRecipientId, NEEDS).getBody().getContent();
        Stream<EvidenceSupportWorkViewModel> filtered = work.stream().filter(input -> uuid.equals(input.id));
        return filtered.findFirst().get();
    }

    @Override
    protected void ensureDefinitionIds() {
        if (actionDefId == null) {
            List<OutcomeViewModel> outcomes = outcomeActor.findAllByServiceName(service.getServiceName()).getBody();
            Stream<OutcomeViewModel> filteredOutcomes = outcomes.stream()
                    .filter(input -> "use of time".equals(input.name));
            actionDefId = filteredOutcomes.findFirst().get().actionGroups.get(0).actions.get(0).id;
        }
    }

    private void createChildServiceTypeAndService(String serviceTypeName, String serviceName) {
        long serviceTypeId = serviceTypeActor.createServiceType(serviceTypeName, true).id;
        createChildService(serviceTypeId, serviceName);
    }

    private void createChildService(long serviceTypeId, String serviceName) {
        ResponseEntity<Result> srvResponse = commandActor.executeCommand(
                serviceActor.createServiceCommand(serviceName, serviceTypeId));
        assertThat(srvResponse.getStatusCode(), is(HttpStatus.OK));
        assertNotNull(srvResponse.getBody().getId());
    }

}
