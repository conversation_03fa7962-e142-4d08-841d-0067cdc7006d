package com.ecco.acceptancetests.api.managedvoids;

import com.ecco.acceptancetests.api.BaseJsonTest;
import com.ecco.test.support.UniqueDataService;
import org.junit.Test;

public class ManagedVoidAPITests extends BaseJsonTest {

    private final UniqueDataService unique = UniqueDataService.instance;

    @Test
    public void configSetupOnly() {
        /*
        MANAGED VOIDS
        -------------
        list - permission ‘managed voids’
        config -700: referralView / referralDetails / decideFinal / close / endFlow
        */
    }

}
