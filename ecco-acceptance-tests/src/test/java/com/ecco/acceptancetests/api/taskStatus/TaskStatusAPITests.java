package com.ecco.acceptancetests.api.taskStatus;

import com.ecco.acceptancetests.api.evidence.BaseReferralCommandAPITests;
import com.ecco.config.dom.ListDefinitionEntry;
import com.ecco.data.client.ServiceOptions;
import com.ecco.referral.ReferralCloseWorkflowSyncAgent;
import com.ecco.webApi.evidence.BaseCommandViewModel;
import com.ecco.dto.ChangeViewModel;
import com.ecco.webApi.taskFlow.ReferralTaskExitCommandViewModel;
import com.ecco.webApi.taskFlow.ReferralTaskStatusCommandViewModel;
import com.ecco.webApi.viewModels.TaskStatusViewModel;
import org.hamcrest.Matcher;
import org.hamcrest.Matchers;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.Test;

import org.jspecify.annotations.Nullable;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import static org.hamcrest.Matchers.*;
import static org.hamcrest.MatcherAssert.*;


/**
 * Test for TaskStatus
 */
public class TaskStatusAPITests extends BaseReferralCommandAPITests {

    public TaskStatusAPITests() {
        super(ServiceOptions.ACCOMMODATION);
    }

    @Test
    public void canCreateAndUpdateTaskStatus() {
        LocalDateTime relativeDate = LocalDateTime.now().withNano(0);
        UUID taskStatusUuid = UUID.randomUUID();

        TaskStatusViewModel[] taskStatuses = getTaskStatusByServiceRecipientId(getServiceRecipientId());
        int initialAnswersCount = taskStatuses.length;

        // GIVEN a task to create
        {
            LocalDateTime dueDateRecent = relativeDate.minusDays(30);
            sendTaskStatusCommand(BaseCommandViewModel.OPERATION_ADD, taskStatusUuid, "this is my piece of work 7", dueDateRecent, null);
        }

        LocalDateTime dueDateRecent = relativeDate.minusDays(10);
        // WHEN update the task
        {
            sendTaskStatusCommand(BaseCommandViewModel.OPERATION_UPDATE, taskStatusUuid, "this is my piece of work 10", dueDateRecent, null);
        }

        // THEN we still expect to see the last due date set
        {
            taskStatuses = getTaskStatusByServiceRecipientId(getServiceRecipientId());
            assertThat(taskStatuses.length, is(initialAnswersCount + 1));
            TaskStatusViewModel taskStatus = filterByUuid(taskStatuses, taskStatusUuid);
            assertThat(taskStatus.dueDate, is(dueDateRecent));
        }
    }

    /**
     * When updating a linear workflow task its possible some workflow tasks are available when its TaskStatus does not exist.
     * For instance, 'accept on service' is available, but it might not be created in the current set of tasks.
     * This means editing the task is actually about creating it, so we test that.
     */
    @Test
    public void canUpdateNonExistentTask() {
        LocalDateTime relativeDate = LocalDateTime.now().withNano(0);

        TaskStatusViewModel[] taskStatuses = getTaskStatusByServiceRecipientId(getServiceRecipientId());
        int initialAnswersCount = taskStatuses.length;

        LocalDateTime dueDateRecent = relativeDate.minusDays(10);
        String description = "update available workflow task that doesn't exist yet";
        // WHEN update a legacy task
        {
            // make up that we need to update taskDefId = 5, appropriate referral - see LinearWorkflowService.java#toHandle
            var taskHandle = ""+getServiceRecipientId()+"-5";
            ReferralTaskStatusCommandViewModel vm = new ReferralTaskStatusCommandViewModel(BaseCommandViewModel.OPERATION_UPDATE, taskHandle, getServiceRecipientId());
            vm.dueDate = ChangeViewModel.create(null, dueDateRecent);
            vm.description = ChangeViewModel.changeNullTo(description);
            commandActor.executeCommand(vm);
        }

        // THEN we still expect to see the last due date set
        {
            taskStatuses = getTaskStatusByServiceRecipientId(getServiceRecipientId());
            assertThat(taskStatuses.length, is(initialAnswersCount + 1));
            Matchers.arrayContainingInAnyOrder(List.of(taskStatuses), hasItem(matcherForSomeProperties(getServiceRecipientId(), description)));
        }
    }

    @Test
    public void canCreateAndDeleteTaskStatus() {
        LocalDateTime relativeDate = LocalDateTime.now();
        UUID taskStatusUuid = UUID.randomUUID();

        // GIVEN a task to create
        {
            LocalDateTime dueDateRecent = relativeDate.minusDays(30);
            sendTaskStatusCommand(BaseCommandViewModel.OPERATION_ADD, taskStatusUuid, "this is my piece of work 7", dueDateRecent, null);
        }

        // WHEN we delete it
        {
            sendTaskStatusCommand(BaseCommandViewModel.OPERATION_REMOVE, taskStatusUuid, null, null, null);
        }

        // THEN we don't have it
        {
            TaskStatusViewModel taskStatus = filterByUuid(getTaskStatusByServiceRecipientId(getServiceRecipientId()), taskStatusUuid);
            assertThat(taskStatus, is(nullValue()));
        }
    }

    @Test
    public void canUpdateDueDateAndClearFutureTaskStatus() {
        LocalDateTime relativeDate = LocalDateTime.now().withNano(0);
        var taskHandle = ""+getServiceRecipientId()+"-15";
        UUID taskStatusUuid = UUID.randomUUID();
        UUID taskStatusUuid2 = UUID.randomUUID();
        UUID taskStatusUuid3 = UUID.randomUUID();

        // GIVEN a few tasks - 1 not-complete / 2 completed (one past, one future)
        LocalDateTime dueDateRecent = relativeDate.minusDays(30);
        {
            sendTaskStatusCommand(BaseCommandViewModel.OPERATION_ADD, taskStatusUuid, "set due date", dueDateRecent, taskHandle);
            sendTaskStatusCommand(BaseCommandViewModel.OPERATION_ADD, taskStatusUuid2, "set due date and completing", dueDateRecent, taskHandle);
            sendTaskStatusCommand(BaseCommandViewModel.OPERATION_ADD, taskStatusUuid3, "set due date and completing - due future", relativeDate.plusDays(1), taskHandle);
            // complete recently / today (day early - due tomorrow)
            completeTaskStatusCommand(taskStatusUuid2, dueDateRecent.plusDays(20));
            completeTaskStatusCommand(taskStatusUuid3, relativeDate);
        }

        // WHEN we update the not-complete due date
        {
            sendTaskStatusCommand(BaseCommandViewModel.OPERATION_UPDATE, taskStatusUuid, "update due date to sooner", dueDateRecent.minusDays(10), taskHandle);
        }

        // THEN we don't have the future tasks anymore (completed or otherwise!)
        {
            var tasks = getTaskStatusByServiceRecipientId(getServiceRecipientId());
            // not-complete but updated, remains
            var taskStatus = filterByUuid(tasks, taskStatusUuid);
            assertThat(taskStatus.dueDate, is(dueDateRecent.minusDays(10)));
            // complete task in past, remains
            var taskStatus2 = filterByUuid(tasks, taskStatusUuid2);
            assertThat(taskStatus2.completed, is(notNullValue()));
            // complete task in future/today, gone
            var taskStatus3 = filterByUuid(tasks, taskStatusUuid3);
            assertThat(taskStatus3, is(nullValue()));
        }
    }

    @Test
    public void canCreateAndCloseAndUndoTaskStatus() {
        LocalDateTime relativeDate = LocalDateTime.now();
        UUID taskStatusUuid = UUID.randomUUID();
        UUID taskStatusUuidCompleted = UUID.randomUUID();
        // we also need a taskHandle to complete a workflow task
        // make up that we need to update taskDefId = 5, appropriate referral - see LinearWorkflowService.java#toHandle
        var taskHandleCompleted = ""+getServiceRecipientId()+"-5";

        // GIVEN a task, uncompleted
        {
            LocalDateTime dueDateRecent = relativeDate.minusDays(30);
            sendTaskStatusCommand(BaseCommandViewModel.OPERATION_ADD, taskStatusUuid, "this is my piece of work 7", dueDateRecent, null);
        }
        // GIVEN a task, completed
        {
            LocalDateTime dueDateRecent = relativeDate.minusDays(14);
            sendTaskStatusCommand(BaseCommandViewModel.OPERATION_ADD, taskStatusUuidCompleted, "this is my piece of work already completed", dueDateRecent, taskHandleCompleted);
            // TODO review - we need the taskHandle to complete a task
            completeTaskStatusCommand(taskStatusUuidCompleted, dueDateRecent.plusDays(7));
        }

        // WHEN we close the referral
        {
            var cmd = new ReferralTaskExitCommandViewModel(rvm.serviceRecipientId, null);
            cmd.exitedDateChange = ChangeViewModel.changeNullTo(LocalDate.now());
            commandActor.executeCommand(cmd);
        }

        // THEN we check the tasks are completed with correct status
        {
            // incompleted as TASKSTATUS_PARENTINACTIVE_ID
            var taskStatus = filterByUuid(getTaskStatusByServiceRecipientId(getServiceRecipientId()), taskStatusUuid);
            assertThat("taskStatus should exist", taskStatus, is(notNullValue()));
            assertThat("complete status not set correctly in " + ReferralCloseWorkflowSyncAgent.class.getSimpleName(), taskStatus.completedStatusId, is(ListDefinitionEntry.TASKSTATUS_PARENTINACTIVE_ID));

            // already complete as TASKSTATUS_COMPLETED_ID
            taskStatus = filterByUuid(getTaskStatusByServiceRecipientId(getServiceRecipientId()), taskStatusUuidCompleted);
            assertThat("taskStatus should exist", taskStatus, is(notNullValue()));
            assertThat("complete status not set correctly in " + ReferralCloseWorkflowSyncAgent.class.getSimpleName(), taskStatus.completedStatusId, is(ListDefinitionEntry.TASKSTATUS_COMPLETED_ID));
        }

        // WHEN we undoCloseOff the referral
        {
            var cmd = new ReferralTaskExitCommandViewModel(rvm.serviceRecipientId, null);
            cmd.exitedUndoCloseOff = true;
            commandActor.executeCommand(cmd);
        }

        // THEN we check the tasks are not completed, unless they already were (TASKSTATUS_COMPLETED_ID)
        {
            // check not complete
            var taskStatus = filterByUuid(getTaskStatusByServiceRecipientId(getServiceRecipientId()), taskStatusUuid);
            assertThat("taskStatus should exist", taskStatus, is(notNullValue()));
            assertThat("complete status not set correctly in " + ReferralCloseWorkflowSyncAgent.class.getSimpleName(), taskStatus.completedStatusId, is(nullValue()));

            // check complete, as it was before
            taskStatus = filterByUuid(getTaskStatusByServiceRecipientId(getServiceRecipientId()), taskStatusUuidCompleted);
            assertThat("taskStatus should exist", taskStatus, is(notNullValue()));
            assertThat("complete status not set correctly in " + ReferralCloseWorkflowSyncAgent.class.getSimpleName(), taskStatus.completedStatusId, is(ListDefinitionEntry.TASKSTATUS_COMPLETED_ID));
        }
    }

    private ReferralTaskStatusCommandViewModel sendTaskStatusCommand(String operation, UUID taskStatusUuid,
                                                                     @Nullable String description, @Nullable LocalDateTime dueDate,
                                                                     @Nullable String taskHandle) {
        ReferralTaskStatusCommandViewModel tsvm = createTaskStatusCommandViewModel(operation, taskStatusUuid, getServiceRecipientId(), description, dueDate);
        tsvm.taskHandle = taskHandle;
        commandActor.executeCommand(tsvm);
        return tsvm;
    }
    private ReferralTaskStatusCommandViewModel completeTaskStatusCommand(UUID taskStatusUuid, @Nullable LocalDateTime completeDate) {
        ReferralTaskStatusCommandViewModel tsvm = createTaskStatusCommandViewModel(BaseCommandViewModel.OPERATION_UPDATE, taskStatusUuid, getServiceRecipientId(), null, null);
        // see ReferralTaskStatusCommandHandler - the status is set as TASKSTATUS_COMPLETED_ID
        tsvm.completed = ChangeViewModel.changeNullTo(completeDate);
        tsvm.taskHandle = ""+getServiceRecipientId()+"-5";
        commandActor.executeCommand(tsvm);
        return tsvm;
    }

    private ReferralTaskStatusCommandViewModel createTaskStatusCommandViewModel(String operation, UUID taskStatusUuid, int serviceRecipientId, String description, LocalDateTime dueDate) {
        ReferralTaskStatusCommandViewModel vm = new ReferralTaskStatusCommandViewModel(taskStatusUuid, operation, serviceRecipientId);

        if (dueDate != null) {
            vm.dueDate = ChangeViewModel.create(null, dueDate);
        }
        if (vm.description != null) {
            vm.description = ChangeViewModel.changeNullTo(description);
        }
        return vm;
    }

    private TaskStatusViewModel[] getTaskStatusByServiceRecipientId(int serviceRecipientId) {
        return this.taskStatusActor.getTasksByServiceRecipient(serviceRecipientId).getBody();
    }

    private TaskStatusViewModel filterByUuid(TaskStatusViewModel[] taskStatuses, UUID taskStatusUuid) {
        return Arrays.stream(taskStatuses)
                .filter(ts -> ts.taskInstanceUuid.equals(taskStatusUuid))
                .findFirst()
                .orElse(null);
    }

    private Matcher<TaskStatusViewModel> matcherForSomeProperties(long serviceRecipientId, String description) {
        return allOf(
                hasProperty("serviceRecipientId", equalTo(serviceRecipientId)),
                hasProperty("description", equalTo(description)));
    }

}
