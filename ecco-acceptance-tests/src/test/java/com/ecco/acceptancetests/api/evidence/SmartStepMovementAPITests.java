package com.ecco.acceptancetests.api.evidence;

import com.ecco.acceptancetests.ui.DisableOnCI;
import com.ecco.data.client.ServiceOptions;
import com.ecco.dom.EvidenceGroup;
import com.ecco.dom.ReportCriteriaDto;
import com.ecco.evidence.EvidenceTask;
import com.ecco.serviceConfig.viewModel.ActionViewModel;
import com.ecco.serviceConfig.viewModel.OutcomeViewModel;
import com.ecco.dto.ChangeViewModel;
import com.ecco.webApi.evidence.GoalUpdateCommandViewModel;
import com.ecco.webApi.evidence.SupportSmartStepsSnapshotViewModel;
import com.ecco.webApi.evidence.EvidenceSupportWorkViewModel;
import org.hamcrest.Matcher;
import org.jspecify.annotations.Nullable;
import org.joda.time.Instant;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Disabled;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import java.util.function.Function;

import static java.util.stream.Collectors.toList;
import static org.hamcrest.CoreMatchers.*;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.hasProperty;

/**
 * NB A baseline taken from QuestionnaireMovementAnswersAPITests.
 * Test for getting the values at points/snapshots in time.
 * NB We use the same referral for each test, which means one test can influence another.
 * To keep tests independent (where needed) we use different work items and different questions.
 */
@SuppressWarnings("ConstantConditions")
@DisableOnCI("duplicate of QuestionnaireMovementAPITests and fails when run together")
@Disabled
public class SmartStepMovementAPITests extends BaseMovementAPITests<EvidenceSupportWorkViewModel> {

    private static final String[] testIndexGoalName = new String[]{"result 1", "result 2", "result 3", "result 4"};
    private static final UUID[] testIndexActionInstanceUuid = new UUID[23];

    public SmartStepMovementAPITests() {
        super(EvidenceGroup.NEEDS, EvidenceTask.NEEDS_ASSESSMENT, ServiceOptions.ACCOMMODATION);
    }

    @BeforeAll
    public static void init() {
        ensureDefs = Boolean.FALSE;
        defIds = new int[23];
    }

    @SuppressWarnings("OptionalGetWithoutIsPresent")
    @Override
    protected void ensureDefinitionIds() {
        differentEvidenceGroup = EvidenceGroup.NEEDS;
        differentEvidenceTask = EvidenceTask.STAFF_NOTES;

        if (ensureDefs == null || Boolean.FALSE.equals(ensureDefs)) {
            ensureDefs = Boolean.TRUE;

            List<OutcomeViewModel> outcomes = outcomeActor.findAllByServiceName(service.getServiceName()).getBody();
            //noinspection OptionalGetWithoutIsPresent - deliberately want exception in a test
            List<ActionViewModel> filteredActionsUoT = outcomes.stream()
                    .filter(input -> "use of time".equals(input.name))
                    .findFirst().get().actionGroups.get(0).actions;
            List<ActionViewModel> filteredActionsEW = outcomes.stream()
                    .filter(input -> "economic wellbeing".equals(input.name))
                    .findFirst().get().actionGroups.get(0).actions;
            List<ActionViewModel> filteredActionsBH = outcomes.stream()
                    .filter(input -> "be healthy".equals(input.name))
                    .findFirst().get().actionGroups.get(0).actions;
            List<ActionViewModel> filteredActionsAF = outcomes.stream()
                    .filter(input -> "abuse free".equals(input.name))
                    .findFirst().get().actionGroups.get(0).actions;

            for (int i = 0; i <= 22; i++) {
                testIndexActionInstanceUuid[i] = UUID.randomUUID();
            }

            /*
            -- 28 actions
            select a2.* from services s inner join servicetypes st on s.servicetypeId = st.id inner join servicetypes_outcomesupports so on st.id = so.servicetypeId
            inner join outcomes o on o.id=so.outcomeId inner join actiongroups a on o.id = a.outcomeId inner join actions a2 on a.id = a2.riskId
            where o.name='use of time';
             */
            defIds[0] = filteredActionsUoT.get(0).id;
            defIds[1] = filteredActionsUoT.get(1).id;
            defIds[2] = filteredActionsUoT.get(2).id;
            defIds[3] = filteredActionsUoT.get(3).id;
            defIds[4] = filteredActionsUoT.get(4).id;
            defIds[5] = filteredActionsUoT.get(5).id;
            defIds[6] = filteredActionsUoT.get(6).id;

            defIds[7] = filteredActionsEW.get(0).id;
            defIds[8] = filteredActionsEW.get(1).id;
            defIds[9] = filteredActionsEW.get(2).id;
            defIds[10] = filteredActionsEW.get(3).id;
            defIds[11] = filteredActionsEW.get(4).id;
            defIds[12] = filteredActionsEW.get(5).id;

            defIds[13] = filteredActionsBH.get(0).id;
            defIds[14] = filteredActionsBH.get(1).id;
            defIds[15] = filteredActionsBH.get(2).id;
            defIds[16] = filteredActionsBH.get(3).id;
            defIds[17] = filteredActionsBH.get(4).id;
            defIds[18] = filteredActionsBH.get(5).id;
            defIds[19] = filteredActionsBH.get(6).id;

            defIds[20] = filteredActionsAF.get(0).id;
            defIds[21] = filteredActionsAF.get(1).id;
            defIds[22] = filteredActionsAF.get(2).id;
        }
    }

    @Override
    protected List<EvidenceSupportWorkViewModel> findWorkSummaryByServiceRecipientId() {
        return supportEvidenceActor.findAllSupportWorkSummaryByServiceRecipientId(
                getServiceRecipientId(), this.evidenceGroup).getBody().getContent();
    }

    @Override
    protected Matcher<ResultSnapshotViewModel> matcherForSomeProperties(long defId, int testIndex) {
        return allOf(
                hasProperty("defId", equalTo(defId)),
                hasProperty("resultProperty", equalTo(testIndexGoalName[testIndex])));
    }

    @Override
    protected ResultsSnapshotViewModel[] runReportEarliestSnapshotInRange(ReportCriteriaDto reportCriteriaDto, int page) {
        ResponseEntity<SupportSmartStepsSnapshotViewModel[]> responseReport = reportActor.getReportSmartStepsEarliestInRangeSnapshot(reportCriteriaDto, page);
        assertThat(responseReport.getStatusCode(), is(HttpStatus.OK));
        assertThat(responseReport.getBody(), is(notNullValue()));
        return ResultsSnapshotsAdapter.apply(responseReport.getBody());
    }

    @Override
    protected ResultsSnapshotViewModel[] runReportLatestSnapshotBeforeRange(ReportCriteriaDto reportCriteriaDto, int page) {
        ResponseEntity<SupportSmartStepsSnapshotViewModel[]> responseReport = reportActor.getReportSmartStepsLatestSnapshotBeforeRange(reportCriteriaDto, page);
        assertThat(responseReport.getStatusCode(), is(HttpStatus.OK));
        assertThat(responseReport.getBody(), is(notNullValue()));
        return ResultsSnapshotsAdapter.apply(responseReport.getBody());
    }

    @Override
    protected ResultsSnapshotViewModel[] runReportLatestSnapshotInRange(ReportCriteriaDto reportCriteriaDto, int page) {
        ResponseEntity<SupportSmartStepsSnapshotViewModel[]> responseReport = reportActor.getReportSmartStepsLatestInRangeSnapshot(reportCriteriaDto, page);
        assertThat(responseReport.getStatusCode(), is(HttpStatus.OK));
        assertThat(responseReport.getBody(), is(notNullValue()));
        return ResultsSnapshotsAdapter.apply(responseReport.getBody());
    }

    @Override
    protected void createResult(UUID workUuid, @Nullable Instant timestampCreated, EvidenceGroup evidenceGroup, EvidenceTask defaultEvidenceTask, int defIdIndex, int testIndex) {
        UUID instanceUuid = testIndexActionInstanceUuid[defIdIndex];
        GoalUpdateCommandViewModel vm = new GoalUpdateCommandViewModel(workUuid, getServiceRecipientId(), evidenceGroup, defaultEvidenceTask, defIds[defIdIndex], instanceUuid, null);
        vm.goalNameChange = ChangeViewModel.changeNullTo(testIndexGoalName[testIndex]);
        if (timestampCreated != null) {
            vm.timestamp = timestampCreated;
        }
        commandActor.executeCommand(vm);
    }

    private static final Function<SupportSmartStepsSnapshotViewModel, ResultsSnapshotViewModel> ResultsSnapshotAdapter = (snapshot) ->
            ResultsSnapshotViewModel.builder()
                    .evidenceGroupKey(snapshot.evidenceGroupKey)
                    .serviceRecipientId(snapshot.serviceRecipientId)
                    .results(snapshot.latestActions.stream()
                            .map(a -> ResultSnapshotViewModel.builder()
                                    .id(a.id)
                                    .defId(a.actionId)
                                    .workDate(a.workDate)
                                    .resultProperty(a.goalName)
                                    .build()
                            ).collect(toList())
                    )
                    .build();

    private static final Function<SupportSmartStepsSnapshotViewModel[], ResultsSnapshotViewModel[]> ResultsSnapshotsAdapter = (snapshots) ->
            Arrays.stream(snapshots)
                    .map(ResultsSnapshotAdapter)
                    .toArray(ResultsSnapshotViewModel[]::new);
}
