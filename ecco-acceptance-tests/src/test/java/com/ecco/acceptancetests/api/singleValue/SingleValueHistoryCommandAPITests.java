package com.ecco.acceptancetests.api.singleValue;

import com.ecco.dto.ChangeViewModel;
import com.ecco.webApi.singleValue.SingleValueHistoryCommandViewModel;
import com.ecco.webApi.singleValue.SingleValueHistoryViewModel;

public class SingleValueHistoryCommandAPITests extends HistoryItemCommandAPITestSupport<SingleValueHistoryViewModel, SingleValueHistoryCommandViewModel> {

    private static final String keyToManage = "referral.choicesMap['supportTier']";

    {
        this.testParams = new HistoryItemTestParams<>() {
            private long value = 50;
            @Override
            public SingleValueHistoryViewModel createRandomItem() {
                SingleValueHistoryViewModel params = new SingleValueHistoryViewModel();
                params.value = value++;
                return params;
            }

            @Override
            public boolean testVars(SingleValueHistoryViewModel expected, SingleValueHistoryViewModel actual) {
                return expected.value.equals(actual.value);
            }
        };
    }

    @Override
    protected SingleValueHistoryCommandViewModel createCommand(String operation, int serviceRecipientId) {
        return new SingleValueHistoryCommandViewModel(operation, serviceRecipientId, "blah");
    }

    @Override
    protected SingleValueHistoryCommandViewModel createCommand(String operation, SingleValueHistoryViewModel params) {
        SingleValueHistoryCommandViewModel vm = new SingleValueHistoryCommandViewModel(operation, rvm.serviceRecipientId, keyToManage);
        if (params != null) {
            vm.value = ChangeViewModel.create(null, params.value);
        }
        return vm;
    }

    @Override
    protected SingleValueHistoryViewModel[] getHistory() {
        return singleValueHistoryActor.getSingleValueHistoryByServiceRecipientIdAndKeyOrderByValidFromDesc(rvm.serviceRecipientId, keyToManage).getBody();
    }

}