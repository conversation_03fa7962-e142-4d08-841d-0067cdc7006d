

package com.ecco.acceptancetests.api.referral;

import com.ecco.acceptancetests.api.BaseJsonTest;
import com.ecco.data.client.ServiceOptions;
import com.ecco.webApi.contacts.*;
import com.ecco.webApi.evidence.BaseCommandViewModel;
import com.ecco.dto.ChangeViewModel;
import com.ecco.webApi.evidence.ReferralViewModel;
import com.ecco.webApi.viewModels.Result;
import org.joda.time.LocalDate;
import org.joda.time.LocalDateTime;
import org.joda.time.format.ISODateTimeFormat;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.web.client.HttpServerErrorException;

import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Random;

import static org.junit.Assert.*;

public class ServiceRecipientAssociatedContactCommandAPITests extends BaseJsonTest {

    //protected final static LocalDateTime now = new LocalDateTime();
    //private final UniqueDataService unique = UniqueDataService.instance;

    static private Random random = new Random();
    static private ReferralViewModel rvm;
    static private ClientViewModel cvm;
    static private LocalDateTime now = new LocalDateTime().minusDays(50);
    static private Integer associatedTypeId_nextofkin = 150;
    static private Integer listDef_theyThem = 202;

    protected final ServiceOptions service = ServiceOptions.ACCOMMODATION;

    @BeforeEach
    public void createClientAndReferral() {
        // emulating @BeforeClass but we want our injected services
        if (rvm == null) {
            LocalDate received = LocalDate.now();
            rvm = referralActor.createReferralAsStarted("associated", "contact", received, service);
            cvm = clientActor.getClientById(rvm.clientId).getBody();
        }
    }

    @Test
    public void canAcceptSomeCommand() {
        int id = createIndividual("Bobby", "Davies");
        IndividualViewModel ivm = contactActor.getContactById(id).getBody();
        assertNotNull(ivm);
        ServiceRecipientAssociatedContactCommandViewModel vm = createCommand(BaseCommandViewModel.OPERATION_ADD, ivm.contactId);
        Result result = commandActor.executeCommand(vm).getBody();
        assertTrue(result.isCommandSuccessful());
        assertNotNull(result.getId());
    }

    @Test
    public void canCreate() {
        int id = createIndividual("Bobby", "Dino");
        IndividualViewModel ivm = contactActor.getContactById(id).getBody();
        assertNotNull(ivm);
        ServiceRecipientAssociatedContactViewModel[] contacts = referralActor.getReferralContactsBySrId(rvm.serviceRecipientId).getBody();
        int prior = contacts.length;

        ServiceRecipientAssociatedContactCommandViewModel vm = createCommand(BaseCommandViewModel.OPERATION_ADD, ivm.contactId);
        Result result = commandActor.executeCommand(vm).getBody();
        assertTrue(result.isCommandSuccessful());
        assertNotNull(result.getId());

        contacts = referralActor.getReferralContactsBySrId(rvm.serviceRecipientId).getBody();
        assertNotNull(contacts);
        assertEquals(rvm.serviceRecipientId, contacts[prior].serviceRecipientId);
        assertEquals(ivm.contactId, contacts[prior].contact.contactId);
    }

    // HTTP 400 - Bad Requst (Valid command but failed to execute) thrown by spring-data when deleting something that doesn't exist
    @Test
    public void cannotRemoveWhatIsNotThere() {
        ServiceRecipientAssociatedContactCommandViewModel vm = createCommand(BaseCommandViewModel.OPERATION_REMOVE, -999);
        assertThrows(HttpServerErrorException.class, () -> commandActor.executeCommand(vm));
    }

    // HTTP 500 - Internal server error (NPE when can't find srId)
    @Test
    public void cannotAddUnknownServiceRecipient() {
        int id = createIndividual("Davis", "Davies");
        ServiceRecipientAssociatedContactCommandViewModel vm = new ServiceRecipientAssociatedContactCommandViewModel(BaseCommandViewModel.OPERATION_REMOVE, -999, id);
        assertThrows(HttpServerErrorException.class, () -> commandActor.executeCommand(vm));
    }

    @Test
    public void canCreateAndRemove() {
        int id = createIndividual("Bobby", "Dino");
        IndividualViewModel ivm = contactActor.getContactById(id).getBody();
        assertNotNull(ivm);

        ServiceRecipientAssociatedContactViewModel[] contacts = referralActor.getReferralContactsBySrId(rvm.serviceRecipientId).getBody();
        int priorCount = contacts.length;

        ServiceRecipientAssociatedContactCommandViewModel vm = createCommand(BaseCommandViewModel.OPERATION_ADD, ivm.contactId);
        Result result = commandActor.executeCommand(vm).getBody();
        assertTrue(result.isCommandSuccessful());
        assertNotNull(result.getId());

        ServiceRecipientAssociatedContactCommandViewModel vmDel = createCommand(BaseCommandViewModel.OPERATION_REMOVE, ivm.contactId);
        Result resultDel = commandActor.executeCommand(vmDel).getBody();
        assertTrue(resultDel.isCommandSuccessful());
        assertNotNull(resultDel.getId());

        contacts = referralActor.getReferralContactsBySrId(rvm.serviceRecipientId).getBody();
        assertNotNull(contacts);
        assertEquals(priorCount, contacts.length);
    }

    @Test
    public void canUpdateAssociatedArchiveDate() {
        int id = createIndividual("Jimmy", "Wiper");
        IndividualViewModel ivm = contactActor.getContactById(id).getBody();
        assertNotNull(ivm);

        LocalDate archived = new LocalDate("2018-09-25");
        ServiceRecipientAssociatedContactCommandViewModel vm = createCommand(BaseCommandViewModel.OPERATION_ADD, ivm.contactId);
        vm.archivedChange = ChangeViewModel.changeNullTo(archived.toString(ISODateTimeFormat.date()));
        Result result = commandActor.executeCommand(vm).getBody();
        assertTrue(result.isCommandSuccessful());
        assertNotNull(result.getId());

        ServiceRecipientAssociatedContactViewModel[] contacts = referralActor.getReferralContactsBySrId(rvm.serviceRecipientId).getBody();
        assertNotNull(contacts);
        ServiceRecipientAssociatedContactViewModel c = Arrays.stream(contacts).filter(sc -> sc.contact.contactId.equals(ivm.contactId)).findFirst().get();
        assertEquals(rvm.serviceRecipientId, c.serviceRecipientId);
        assertEquals(ivm.contactId, c.contact.contactId);
        assertEquals(archived, c.archived);
    }

    @Test
    public void canUpdateAssociatedTypeId() {
        int id = createIndividual("Kelly", "Boulder");
        IndividualViewModel ivm = contactActor.getContactById(id).getBody();
        assertNotNull(ivm);

        ServiceRecipientAssociatedContactCommandViewModel vm = createCommand(BaseCommandViewModel.OPERATION_ADD, ivm.contactId);
        vm.addedAssociatedTypeIds = ChangeViewModel.changeNullTo(associatedTypeId_nextofkin.toString());
        Result result = commandActor.executeCommand(vm).getBody();
        assertTrue(result.isCommandSuccessful());
        assertNotNull(result.getId());

        ServiceRecipientAssociatedContactViewModel[] contacts = referralActor.getReferralContactsBySrId(rvm.serviceRecipientId).getBody();
        assertNotNull(contacts);
        ServiceRecipientAssociatedContactViewModel c = Arrays.stream(contacts).filter(sc -> sc.contact.contactId.equals(ivm.contactId)).findFirst().get();
        assertEquals(rvm.serviceRecipientId, c.serviceRecipientId);
        assertEquals(ivm.contactId, c.contact.contactId);
        assertEquals(associatedTypeId_nextofkin, c.associatedTypeIds[0]);
    }

    @Test
    public void canUpdateContactDate() {
        int id = createIndividual("Will", "Archive");
        IndividualViewModel ivm = contactActor.getContactById(id).getBody();
        assertNotNull(ivm);

        java.time.LocalDate archived = java.time.LocalDate.parse("2022-09-25");
        ContactCommandViewModel vm = new ContactCommandViewModel(ivm.contactId.intValue());
        vm.archived = ChangeViewModel.changeNullTo(DateTimeFormatter.ISO_DATE.format(archived));
        Result result = commandActor.executeCommand(vm).getBody();
        assert result != null;
        assertTrue(result.isCommandSuccessful());
        assertNotNull(result.getId());

        var contact = contactActor.getContactById(ivm.contactId).getBody();
        assertNotNull(contact);
        assertEquals(archived, contact.archived);
    }

    private int createIndividual(String firstName, String lastName) {
        IndividualViewModel ivm = new IndividualViewModel();
        ivm.setKnownAs(firstName.substring(0, 1));
        ivm.setPronounsId(listDef_theyThem);
        ivm.setFirstName(firstName);
        ivm.setLastName(lastName);
        Result r = contactActor.createIndividual(ivm).getBody();
        return Integer.parseInt(r.getId());
    }

    private ServiceRecipientAssociatedContactCommandViewModel createCommand(String operation, long contactId) {
        ServiceRecipientAssociatedContactCommandViewModel vm = new ServiceRecipientAssociatedContactCommandViewModel(operation, rvm.serviceRecipientId, (int) contactId);
        //vm.archiveDateChange = ChangeViewModel.create(null, new LocalDate().toString(ISODateTimeFormat.date()));
        return vm;
    }

}