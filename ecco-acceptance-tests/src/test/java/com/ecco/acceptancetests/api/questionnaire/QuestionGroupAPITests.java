package com.ecco.acceptancetests.api.questionnaire;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertThat;

import java.util.ArrayList;
import com.ecco.acceptancetests.api.BaseJsonTest;
import com.ecco.serviceConfig.viewModel.QuestionGroupViewModel;
import com.ecco.serviceConfig.viewModel.QuestionViewModel;
import com.ecco.webApi.viewModels.Result;

import org.junit.jupiter.api.Test;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

public class QuestionGroupAPITests extends BaseJsonTest {

    @Test
    public void saveQuestionGroup() {

        QuestionGroupViewModel vm = new QuestionGroupViewModel();
        vm.questions = new ArrayList<>();

        vm.name = "questiongroup1";
        QuestionViewModel qvm = new QuestionViewModel();
        qvm.name = "question1";
        qvm.answerType = "checkbox";
        vm.questions.add(qvm);

        ResponseEntity<Result> response = questionGroupActor.createQuestionGroup(vm);
        assertEquals(HttpStatus.CREATED, response.getStatusCode());
        Long id = Long.valueOf(response.getBody().getId());

        // load back the questiongroup
        QuestionGroupViewModel qg = questionGroupActor.findByQuestionGroupId(id).getBody();

        // asserts
        assertEquals("questiongroup1", qg.name);
        assertThat(qg.questions.size(), equalTo(1));

        QuestionViewModel q = qg.questions.get(0);
        assertThat(q.name, equalTo("question1"));
        assertThat(q.answerType, equalTo("checkbox"));
    }

}
