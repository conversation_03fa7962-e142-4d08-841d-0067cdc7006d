package com.ecco.acceptancetests.api.evidence;

import com.ecco.data.client.ServiceOptions;
import com.ecco.dom.EvidenceGroup;
import com.ecco.dto.AddedRemovedDto;
import com.ecco.dto.ChangeViewModel;
import com.ecco.evidence.EvidenceTask;
import com.ecco.webApi.evidence.*;
import com.ecco.webApi.listsConfig.ListDefinitionEntryViewModel;
import org.joda.time.Duration;
import org.joda.time.Instant;
import org.joda.time.LocalDate;
import org.junit.Assert;
import org.junit.jupiter.api.Test;

import java.util.*;

import static com.ecco.dto.ChangeViewModel.changeNullTo;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.is;
import static org.junit.Assert.*;

public class RiskCommandAPITests extends RiskBaseAPITests {

    public RiskCommandAPITests() {
        super(EvidenceGroup.THREAT, EvidenceTask.THREAT_ASSESSMENT, ServiceOptions.DEMO_ALL);
    }

    @Test
    public void commentRiskManagementHandled_happyPath() {
        // GIVEN
        List<UUID> supportWorkUuids = new ArrayList<>();
        {
            // see also @Before createUniqueClientAndReferral

            // create a comment with risk management required
            UUID supportWorkUuid = UUID.randomUUID();
            createWorkItemWithRiskRequired(supportWorkUuids, supportWorkUuid);
        }

        // WHEN
        {
            // create risk entry which deals with the support 'risk management required'
            CommentCommandViewModel ccvm = createCommentCommand("parents are amazing", workDateUpTo50DaysInPast);
            ccvm.riskManagementHandled = ChangeViewModel.create(null, supportWorkUuids);
            commandActor.executeCommand(ccvm);
        }

        // THEN
        {
            EvidenceThreatWorkViewModel lastEvidence = findWorkByUuid(workUuid);
            assertNotNull(lastEvidence);
            assertEquals(supportWorkUuids, lastEvidence.handledSupportWorkIds);
        }
    }

    @Test
    public void commentRiskManagementHandled_handleThenMoreWorkNotHandled() {
        // GIVEN
        List<UUID> supportWorkUuids = new ArrayList<>();
        {
            // see also @Before createUniqueClientAndReferral

            // create a comment with risk management required
            UUID supportWorkUuid = UUID.randomUUID();
            createWorkItemWithRiskRequired(supportWorkUuids, supportWorkUuid);
        }

        UUID secondWorkUuid = UUID.randomUUID();
        // WHEN
        {
            // create risk entry which deals with the support 'risk management required'
            CommentCommandViewModel ccvm = createCommentCommand("parents are amazing", workDateUpTo50DaysInPast);
            ccvm.riskManagementHandled = ChangeViewModel.create(null, supportWorkUuids);
            commandActor.executeCommand(ccvm);

            // create risk entry with no handling of risk
            CommentCommandViewModel ccvm2 = createCommentCommandViewModel(secondWorkUuid, getServiceRecipientId(),
                    evidenceGroup, defaultEvidenceTask, "children are amazing", workDateUpTo50DaysInPast, 10 + new Random().nextInt(145));
            commandActor.executeCommand(ccvm2);
        }

        // THEN
        {
            EvidenceThreatWorkViewModel handledEvidence = findWorkByUuid(workUuid);
            assertNotNull(handledEvidence);
            assertEquals(supportWorkUuids, handledEvidence.handledSupportWorkIds);

            EvidenceThreatWorkViewModel nonHandledEvidence = findWorkByUuid(secondWorkUuid);
            assertNotNull(nonHandledEvidence);
            Assert.assertThat(nonHandledEvidence.handledSupportWorkIds.isEmpty(), is(true));
        }
    }

    @Test
    public void saveWorkItemThenCommentShouldAppendToWorkItem() {
        BaseGoalUpdateCommandViewModel riskUpdateCmd = sendRiskUpdateCommand();

        CommentCommandViewModel ccvm = sendCommentCommand("look around... isn't nature amazing", workDateUpTo50DaysInPast);

        EvidenceThreatWorkViewModel lastEvidence = getAndVerifyRiskUpdateGotApplied(riskUpdateCmd);
        verifyThatEvidenceMatchesCommentCommand(ccvm, lastEvidence);
    }

    @Test
    public void riskActionUpdateCommandShouldCreateWorkWithNoComment() {
        GoalUpdateCommandViewModel riskUpdateCmd = sendRiskUpdateCommand();

        EvidenceThreatWorkViewModel lastEvidence = getAndVerifyRiskUpdateGotApplied(riskUpdateCmd);
        verifyLikelihoodAndSeverity(lastEvidence, riskUpdateCmd);
        verifyTriggerAndControl(lastEvidence, riskUpdateCmd);
        verifyNoCommentAndDefaultWorkDate(lastEvidence, riskUpdateCmd.timestamp);
    }

    @Test
    public void commentThenActionCommandShouldAppendToWorkAndPreserveComment() {
        CommentCommandViewModel commentCmd = sendCommentCommand("children are the future", workDateUpTo50DaysInPast);

        BaseGoalUpdateCommandViewModel riskUpdateCmd = sendRiskUpdateCommand();

        EvidenceThreatWorkViewModel lastEvidence = getAndVerifyRiskUpdateGotApplied(riskUpdateCmd);
        verifyThatEvidenceMatchesCommentCommand(commentCmd, lastEvidence);
    }

    @Test
    public void riskAreaUpdateCommandShouldCreateWorkWithNoComment() {
        AreaUpdateCommandViewModel areaUpdateCmd = sendRiskAreaUpdateCommand();
        getAndVerifyAreaUpdateGotApplied(areaUpdateCmd);
    }

    // NB taken from SupportCommandAPITests to test actionInstanceUuid, parent and hierarchy, position
    @Test
    public void parentActionInstanceUuid_happyPath() {
        // GIVEN
        GoalUpdateCommandViewModel parentGoalVm;
        {
            // see also @Before createUniqueClientAndReferral

            // create an actionInstanceUuid with work date and created 2 days ago
            parentGoalVm = new GoalUpdateCommandViewModel(workUuid, getServiceRecipientId(), evidenceGroup,
                    defaultEvidenceTask, riskActionDefId, UUID.randomUUID(), null);
            parentGoalVm.timestamp = Instant.now().minus(Duration.standardDays(2));
            parentGoalVm.targetDateChange = changeNullTo(new LocalDate(2018, 4, 7));
            commandActor.executeCommand(parentGoalVm);

            sendCommentCommand("parent + child entry", workDateUpTo50DaysInPast);
        }

        // WHEN add another smart step with the previous as the parent
        GoalUpdateCommandViewModel childGoalVm;
        {
            // see also @Before createUniqueClientAndReferral

            // create an actionInstanceUuid with work date and created 2 days ago
            childGoalVm = new GoalUpdateCommandViewModel(workUuid, getServiceRecipientId(), evidenceGroup,
                    defaultEvidenceTask, riskActionDefId, UUID.randomUUID(), parentGoalVm.actionInstanceUuid);
            childGoalVm.timestamp = parentGoalVm.timestamp;
            childGoalVm.statusChange = ChangeViewModel.changeNullTo(1);
            childGoalVm.hierarchyChange = ChangeViewModel.changeNullTo((short) 1);
            String parentGoalIndex = "0";
            childGoalVm.positionChange = ChangeViewModel.changeNullTo(parentGoalIndex + "-0");
            commandActor.executeCommand(childGoalVm);
        }

        // THEN evidence has both, and the parentActionInstanceUuid populated
        {
            EvidenceThreatWorkViewModel lastEvidence = findWorkByUuid(workUuid);
            assertNotNull(lastEvidence);

            EvidenceThreatActionViewModel parentVm = lastEvidence.riskActions.stream()
                    .filter(a -> a.actionInstanceUuid.equals(childGoalVm.parentActionInstanceUuid))
                    .findFirst()
                    .orElseThrow();
            assertNull(parentVm.parentActionInstanceUuid);

            EvidenceThreatActionViewModel childVm = lastEvidence.riskActions.stream()
                    .filter(a -> a.actionInstanceUuid.equals(childGoalVm.actionInstanceUuid))
                    .findFirst()
                    .orElseThrow();
            assertEquals(parentGoalVm.actionInstanceUuid, childVm.parentActionInstanceUuid);
            assertEquals(1, childVm.hierarchy.intValue());
            assertEquals("0-0", childVm.position);
        }

        // WHEN update an existing snapshot
        UUID updateWorkUuid = UUID.randomUUID();
        {
            GoalUpdateCommandViewModel updateVm = new GoalUpdateCommandViewModel(updateWorkUuid, getServiceRecipientId(), evidenceGroup,
                    defaultEvidenceTask, riskActionDefId, childGoalVm.actionInstanceUuid, parentGoalVm.actionInstanceUuid);
            // we need to have a different 'created' date for the handler to find the previous snapshot (see commit)
            updateVm.timestamp = parentGoalVm.timestamp.plus(1000);
            updateVm.targetDateChange = changeNullTo(new LocalDate(2019,4,7));
            // NB this can execute very quickly, meaning the existing snapshot to the actionInstanceUuid is not found
            commandActor.executeCommand(updateVm);
        }

        // THEN expect the same parentActionInstanceUuid, hierarchy, position
        {
            EvidenceThreatWorkViewModel lastEvidence = findWorkByUuid(updateWorkUuid);
            assertNotNull(lastEvidence);
            EvidenceThreatActionViewModel childVm = findWorkRiskActionInstanceUuid(lastEvidence, childGoalVm.actionInstanceUuid);
            assertEquals(parentGoalVm.actionInstanceUuid, childVm.parentActionInstanceUuid);
            assertEquals(1, childVm.hierarchy.intValue());
            assertEquals("0-0", childVm.position);
        }
    }

    @Test
    public void actionAssociated_happyPath() {
        // GIVEN
        {
            // see also @Before createUniqueClientAndReferral

            // a comment and piece of work exists
            sendCommentCommand("this is my associated piece of work", workDateUpTo50DaysInPast);
        }

        UUID instanceUuid = UUID.randomUUID();

        // WHEN
        GoalUpdateCommandViewModel vm;
        {
            // save some work with a 'blank' goal update
            vm = new GoalUpdateCommandViewModel(workUuid, getServiceRecipientId(), evidenceGroup,
                    defaultEvidenceTask, riskActionDefId, instanceUuid, null);
            vm.isRelevant = true;
            commandActor.executeCommand(vm);
            workUuid = vm.workUuid;
        }

        // THEN
        {
            EvidenceThreatWorkViewModel lastEvidence = findWorkByUuid(vm.workUuid);
            assertNotNull(lastEvidence);
            assertThat(lastEvidence.associatedActions).hasSize(1);
            assertThat(riskActionDefId.intValue()).isEqualTo(lastEvidence.associatedActions.get(0).intValue());
        }
    }

    @Test
    public void attachment_happyPath() {
        // GIVEN
        {
            // see also @Before createUniqueClientAndReferral

            // an attachment exists
            if (fileId == null) {
                fileId = standardAttachment("threat");
            }
        }

        List<EvidenceThreatWorkViewModel> workPrior = findWorkSummaryByServiceRecipientId();

        // WHEN
        {
            UUID firstWorkUuid = UUID.randomUUID();
            CommentCommandViewModel ccvm = createCommentCommandViewModel(firstWorkUuid, rvm.serviceRecipientId, EvidenceGroup.THREAT,
                    EvidenceTask.THREAT_ASSESSMENT, "work without attachment", null, workDateUpTo50DaysInPast, 10 + new Random().nextInt(145));
            commandActor.executeCommand(ccvm);

            CommentCommandViewModel ccvmAttachment = createCommentCommandViewModel(workUuid, rvm.serviceRecipientId, EvidenceGroup.THREAT,
                EvidenceTask.THREAT_ASSESSMENT, "work WITH attachment", null, workDateUpTo50DaysInPast, 10 + new Random().nextInt(145));
            ccvmAttachment.attachmentIdsToAdd = new Long[] {fileId};
            commandActor.executeCommand(ccvmAttachment);
        }

        // THEN
        {
            EvidenceThreatWorkViewModel lastEvidence = findWorkByUuid(workUuid);
            assertNotNull(lastEvidence);
            assertEquals(fileId.longValue(), lastEvidence.attachments.get(0).fileId);

            List<EvidenceThreatWorkViewModel> work = findWorkSummaryByServiceRecipientId();
            assertEquals(workPrior.size() + 2, work.size());

            List<EvidenceThreatWorkViewModel> workWithAttachments = findWorkSummaryWithAttachmentsByServiceRecipientId();
            assertEquals(1, workWithAttachments.size());
        }
    }

    @Test
    public void flag_happyPath() {
        // GIVEN
        {
            // see also @Before createUniqueClientAndReferral
        }

        // WHEN add a flag
        {
            // a comment and piece of work exists
            CommentCommandViewModel ccvm = createCommentCommand(workUuid, "this is my flag piece of work", null, workDateUpTo50DaysInPast);
            var f1 = ListDefinitionEntryViewModel.builder().listName("testlist_flag").name("flag 1").build();
            f1 = listDefActor.ensureAndReturnListDefinitionEntry(f1.getListName(), f1).iterator().next();
            ccvm.flagIds = AddedRemovedDto.added(Collections.singletonList(f1.getId()));
            commandActor.executeCommand(ccvm);
        }

        // THEN flag is returned
        {
            EvidenceThreatWorkViewModel lastEvidence = findWorkByUuid(workUuid);
            assertNotNull(lastEvidence);
            assertEquals(1, lastEvidence.flags.size());
        }
    }

}
