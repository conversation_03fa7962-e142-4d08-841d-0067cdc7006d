package com.ecco.acceptancetests.api.serviceConfig;

import com.ecco.test.support.UniqueDataService;
import com.ecco.acceptancetests.api.BaseJsonTest;
import com.ecco.serviceConfig.viewModel.ActionGroupViewModel;
import com.ecco.serviceConfig.viewModel.OutcomeViewModel;
import com.ecco.webApi.evidence.BaseCommandViewModel;
import com.ecco.dto.ChangeViewModel;
import com.ecco.webApi.serviceConfig.ActionGroupDefCommandViewModel;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.UUID;

import static org.junit.Assert.*;

public class ActionGroupDefCommandAPITests extends BaseJsonTest {

    private static String[] actionGroupDefNames = new String[] {
            UniqueDataService.instance.nameFor("actionGroupDef to add"),
            UniqueDataService.instance.nameFor("actionGroupDef to remove"),
            UniqueDataService.instance.nameFor("actionGroupDef to update"),
            UniqueDataService.instance.nameFor("actionGroupDef updated")};

    protected UUID outcomeDefId;

    @BeforeEach
    public void createDefinitionData() {
        if (outcomeDefId == null) {
            List<OutcomeViewModel> outcomes = outcomeActor.findAllByServiceName("demo-all-workflow").getBody();
            outcomeDefId = outcomes.stream()
                    .filter(input -> "economic wellbeing".equals(input.name))
                    .findFirst().get().uuid;
        }
    }

    @Test
    public void canAddGroups() { canAddGroup(actionGroupDefNames[0]); }

    @Test
    public void canAddThenUpdate() {
        ActionGroupViewModel vm = canAddGroup(actionGroupDefNames[2]);

        String newName = actionGroupDefNames[3];
        ActionGroupDefCommandViewModel cmd = new ActionGroupDefCommandViewModel(BaseCommandViewModel.OPERATION_UPDATE, vm.uuid);
        cmd.nameChange = ChangeViewModel.create(vm.name, newName);
        commandActor.executeCommand(cmd);

        ActionGroupViewModel retrieved = actionGroupActor.findByUUID(vm.uuid).getBody();
        assertNotNull(retrieved);
        assertEquals(newName, retrieved.name);
    }

    @Test
    public void canAddThenHide() {
        ActionGroupViewModel vm = canAddGroup(actionGroupDefNames[2]);

        ActionGroupDefCommandViewModel cmd = new ActionGroupDefCommandViewModel(BaseCommandViewModel.OPERATION_REMOVE, vm.uuid);
        commandActor.executeCommand(cmd);

        ActionGroupViewModel retrieved = actionGroupActor.findByUUID(vm.uuid).getBody();
        assertNotNull(retrieved);
        assertTrue(retrieved.disabled);
    }

    private ActionGroupViewModel canAddGroup(String name) {
        ActionGroupDefCommandViewModel vm = new ActionGroupDefCommandViewModel(BaseCommandViewModel.OPERATION_ADD, UUID.randomUUID());
        vm.outcomeDefUuid = this.outcomeDefId;
        return canAddActionGroupDef(vm, name);
    }

    private ActionGroupViewModel canAddActionGroupDef(ActionGroupDefCommandViewModel cvm, String name) {
        cvm.nameChange = ChangeViewModel.changeNullTo(name);
        commandActor.executeCommand(cvm);

        ActionGroupViewModel retrieved = actionGroupActor.findByUUID(cvm.actionGroupDefUuid).getBody();
        assertNotNull(retrieved);
        assertEquals(cvm.actionGroupDefUuid, retrieved.uuid);
        assertEquals(cvm.nameChange.to, retrieved.name);
        return retrieved;
    }

}
