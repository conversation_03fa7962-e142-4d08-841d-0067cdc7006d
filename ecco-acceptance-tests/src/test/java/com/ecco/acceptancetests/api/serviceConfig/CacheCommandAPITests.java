package com.ecco.acceptancetests.api.serviceConfig;


import org.junit.jupiter.api.Test;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import static org.hamcrest.Matchers.is;

import static org.junit.Assert.assertThat;

import com.ecco.acceptancetests.api.BaseJsonTest;
import com.ecco.webApi.viewModels.Result;

public class CacheCommandAPITests extends BaseJsonTest {

    @Test
    public void canClearCache() {
        ResponseEntity<Result> response = cacheActor.clearCaches();
        assertThat(response.getStatusCode(), is(HttpStatus.CREATED));
        assertThat(response.getBody().isCommandSuccessful(), is(Boolean.TRUE));
    }

}