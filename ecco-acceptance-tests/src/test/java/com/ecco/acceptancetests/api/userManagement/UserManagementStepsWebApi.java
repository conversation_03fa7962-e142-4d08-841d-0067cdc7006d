package com.ecco.acceptancetests.api.userManagement;

import com.ecco.test.support.UniqueDataService;
import com.ecco.acceptancetests.steps.UserManagementSteps;
import com.ecco.acceptancetests.ui.pages.Role;
import com.ecco.data.client.actors.BaseActor;
import com.ecco.data.client.actors.UserActor;
import com.ecco.infrastructure.time.Clock;
import com.ecco.webApi.contacts.IndividualUserSummaryViewModel;
import org.jspecify.annotations.NonNull;
import org.joda.time.DateTime;
import org.springframework.web.client.RestTemplate;

import java.util.Arrays;
import java.util.List;

import static java.util.stream.Collectors.toSet;

@SuppressWarnings("unused")
public class UserManagementStepsWebApi extends BaseActor implements UserManagementSteps {
    private final UserActor userActor;
    private Clock clock = Clock.DEFAULT;
    private DateTime now = clock.now();

    private final UniqueDataService unique = UniqueDataService.instance;

    public UserManagementStepsWebApi(RestTemplate restTemplate, UserActor userActor) {
        super(restTemplate);
        this.userActor = userActor;
    }

    @NonNull
    @Override
    public String createUser(@NonNull String username, @NonNull Role... groups) {
        throw new UnsupportedOperationException("Not completed on Web API");
    }

    @NonNull
    @Override
    public IndividualUserSummaryViewModel createIndividualWithUser(@NonNull String username, @NonNull String newPassword,
                                                                   @NonNull String firstName, @NonNull String lastName,
                                                                   @NonNull Role ...groups) {
        return userActor.createIndividualWithUser(username, newPassword, firstName, lastName,
                Arrays.stream(groups).map(Role::name).collect(toSet()));
    }

    @Override
    public void createSharedUser(@NonNull String username, @NonNull Role... group) {
        throw new UnsupportedOperationException("Not completed on Web API");
    }

    @NonNull
    @Override
    public List<String> listUsers() {
        throw new UnsupportedOperationException("Not completed on Web API");
    }

    @NonNull
    @Override
    public List<String> listUsers(@NonNull String initial, @NonNull String groupName, boolean enabledOnly) {
        throw new UnsupportedOperationException("Not completed on Web API");
    }

    @NonNull
    @Override
    public List<String> listUserGroups() {
        throw new UnsupportedOperationException("Not completed on Web API");
    }

    @Override
    public void navigateBack() {
        throw new UnsupportedOperationException("Not appropriate on Web API");
    }

    @Override
    public void navigateToHome() {
        throw new UnsupportedOperationException("Not appropriate on Web API");
    }

    @Override
    public void navigateToWelcome() {
        throw new UnsupportedOperationException("Not appropriate on Web API");
    }

    @Override
    public void checkCanSeeText(@NonNull String text) {
        throw new UnsupportedOperationException("Not appropriate on Web API");
    }

    @Override
    public void checkCannotSeeText(@NonNull String text) {
        throw new UnsupportedOperationException("Not appropriate on Web API");
    }
}
