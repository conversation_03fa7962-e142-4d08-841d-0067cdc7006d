package com.ecco.acceptancetests.api.contract;

import com.ecco.test.support.UniqueDataService;
import com.ecco.acceptancetests.steps.ContractSteps;
import com.ecco.data.client.actors.BaseActor;
import com.ecco.data.client.actors.ContractActor;
import com.ecco.webApi.rota.ContractViewModel;
import com.ecco.webApi.finance.RateCardViewModel;
import org.jspecify.annotations.NonNull;
import org.springframework.web.client.RestTemplate;

@SuppressWarnings("unused")
public class ContractStepsWebApi extends BaseActor implements ContractSteps {

    private final ContractActor contractActor;

    private final UniqueDataService unique = UniqueDataService.instance;

    public ContractStepsWebApi(RestTemplate restTemplate, ContractActor contractActor) {
        super(restTemplate);
        this.contractActor = contractActor;
    }

    @NonNull
    @Override
    public ContractViewModel givenContractWithRateCard(int eventStatusRateId) {
        String contractName = unique.nameFor("contract-one");
        ContractViewModel contract = contractActor.createContract(contractName, null, null);
        RateCardViewModel rateCard1 = contractActor.createRotaRateCard("rate card 1", contract.contractId);
        contractActor.createRotaRateCardEntry(rateCard1.getRateCardId(), 1, eventStatusRateId);
        return contract;
    }
}
