package com.ecco.acceptancetests.api.security;

import com.ecco.acceptancetests.api.BaseJsonTest;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

/**
 * Check sysadmin user can still do the things they should be able to do.
 * NOTE: This should really be split to working with e.g. ROLE_USERADMIN
 */
public class SysadminAPITests extends BaseJsonTest {

    public SysadminAPITests() {
        super();
    }

    @BeforeEach
    public void changeRestTemplateErrorHandler() {
//        restTemplate.setErrorHandler(new ExtractingResponseErrorHandler(restTemplate.getMessageConverters()));
    }

    @Test
    public void canListUsers() {
        // GIVEN what's already in database

        // WHEN

        // THEN
        {
            var users = userActor.getUsers();
            Assertions.assertThat(users.getBody().getData()).asList().hasSizeGreaterThanOrEqualTo(1);
        }

    }
}
