package com.ecco.acceptancetests.api.questionnaire;

import com.ecco.acceptancetests.api.evidence.BaseEvidenceCommandAPITests;
import com.ecco.data.client.ServiceOptions;
import com.ecco.dom.EvidenceGroup;
import com.ecco.dto.ChangeViewModel;
import com.ecco.evidence.EvidenceTask;
import com.ecco.serviceConfig.viewModel.QuestionGroupViewModel;
import com.ecco.webApi.evidence.*;
import org.joda.time.Instant;
import org.jspecify.annotations.Nullable;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.Random;
import java.util.UUID;

import static java.util.stream.Collectors.toList;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

/**
 * Test for saving an answer. We inherit tests for comments - saving the command
 * and saving the comment and checking the values
 *
 */
public class QuestionnaireEvidenceCommandAPITests extends BaseEvidenceCommandAPITests<QuestionnaireEvidenceViewModel> {

    static protected Long questionGroupDefId;
    static protected Integer questionDefId;
    protected static Long fileId;
    private static final EvidenceGroup questionnaire = EvidenceGroup.GENERALQUESTIONNAIRE;

    public QuestionnaireEvidenceCommandAPITests() {
        super(questionnaire, EvidenceTask.QUESTIONNAIRE_GENERAL, ServiceOptions.ACCOMMODATION);
    }

    @Override
    protected void ensureDefinitionIds() {
        if (questionGroupDefId == null) {

            // questionGroupDefId
            List<QuestionGroupViewModel> questionGroups = questionGroupActor.findAllQuestionGroupsByServiceName(service.getServiceName()).getBody();
            List<QuestionGroupViewModel> filteredQuestionGroups = questionGroups.stream()
                    .filter(QuestionGroupViewModel.questionGroupMatchName("SDQ"))
                    .collect(toList());
            questionGroupDefId = filteredQuestionGroups.get(0).id;

            // questionDefId
            // questionId 1 is "I try to be nice to other people. I care about their feelings"
            // the answer choice we use is "always true" with value 2
            /*
                select qg.*, q.*,qac.displayValue, qac.value from questiongroups qg inner join questiongroups_questions qgq on qg.id=qgq.questiongroupId inner join questions q on q.id=qgq.questionId
                inner join questions_questionanswrchoices qqac on q.id=qqac.questionId
                inner join questionanswerchoices qac on qac.id=qqac.questionanswerchoiceId
                    where qg.name="SDQ";
             */
            questionDefId = filteredQuestionGroups.get(0).questions.get(0).id;
        }
    }

    @Test
    @Disabled("only for notes")
    public void configSetupSupportHours() {
        // DEV-2697
        // create a questionnaire with a question of support hours (choices that have a 'value' as the hours)
        // enter the question group on the overview -> supportHoursGrp (this should be the same as questionnaireEvidenceGroup)
        // enter the question id on the overview -> supportHoursQn
        // add the report to the system from reports-definitions that refers to 'supportHoursQn'
        // then add the hours with a work date
        // do some work
        // run the report
    }

    @Test
    public void answer_happyPath() {
        // GIVEN
        {
            // see also @Before createUniqueClientAndReferral

            // a comment and piece of work exists
            sendCommentCommand("this is my piece of work", workDateUpTo50DaysInPast);
        }

        // WHEN
        QuestionAnswerCommandViewModel vm;
        {
            // save some work with a statusChangeReason
            vm = createQuestionAnswerCommandViewModel(
                    workUuid, null, getServiceRecipientId(),
                    evidenceGroup, defaultEvidenceTask, questionDefId, "2");
            commandActor.executeCommand(vm);
            workUuid = vm.workUuid;
        }

        // THEN
        {
            //EvidenceViewModel lastEvidence = readBackEvidence(vm.workUuid);
            QuestionnaireEvidenceViewModel workSummary = findWorkByUuid(workUuid);
            assertNotNull(workSummary);
            assertEquals(vm.answerChange.to, workSummary.answers.get(0).answer);
        }

    }

    @Disabled("we can assume the work is already saved - we couldn't assume this with goals" +
            "because of the new visual suppport work. See QuestionAnswerCommandHandler.")
    @Test
    public void commandOrder_answer() {
        // GIVEN
        QuestionAnswerCommandViewModel vm;
        {
            // see also @Before createUniqueClientAndReferral

            // save some work with an answer
            vm = createQuestionAnswerCommandViewModel(
                    workUuid, null, getServiceRecipientId(),
                    evidenceGroup, defaultEvidenceTask, questionDefId, "2");
            commandActor.executeCommand(vm);
            workUuid = vm.workUuid;
        }

        // WHEN
        {
            // a comment and piece of work exists
            sendCommentCommand("this is my piece of work", workDateUpTo50DaysInPast);
        }

        // THEN
        {
            //EvidenceViewModel lastEvidence = readBackEvidence(vm.workUuid);
            QuestionnaireEvidenceViewModel workSummary = findWorkByUuid(workUuid);
            assertNotNull(workSummary);
            assertEquals(vm.answerChange.to, workSummary.answers.get(0).answer);
        }
    }

    @Test
    public void attachment_happyPath() {
        // GIVEN
        {
            // see also @Before createUniqueClientAndReferral

            // an attachment exists
            if (fileId == null) {
                fileId = standardAttachment(questionnaire.nameAsLowercase());
            }
        }

        List<QuestionnaireEvidenceViewModel> workPrior = findWorkSummaryByServiceRecipientId();

        // WHEN
        {
            UUID firstWorkUuid = UUID.randomUUID();
            CommentCommandViewModel ccvm = createCommentCommandViewModel(firstWorkUuid, rvm.serviceRecipientId, questionnaire,
                    EvidenceTask.QUESTIONNAIRE_GENERAL, "work without attachment", null, workDateUpTo50DaysInPast, 10 + new Random().nextInt(145));
            commandActor.executeCommand(ccvm);

            CommentCommandViewModel ccvmAttachment = createCommentCommandViewModel(workUuid, rvm.serviceRecipientId, questionnaire,
                    EvidenceTask.QUESTIONNAIRE_GENERAL, "work WITH attachment", null, workDateUpTo50DaysInPast, 10 + new Random().nextInt(145));
            ccvmAttachment.attachmentIdsToAdd = new Long[] {fileId};
            commandActor.executeCommand(ccvmAttachment);
        }

        // THEN
        {
            QuestionnaireEvidenceViewModel lastEvidence = findWorkByUuid(workUuid);
            assertNotNull(lastEvidence);
            assertEquals(fileId.longValue(), lastEvidence.attachments.get(0).fileId);

            List<QuestionnaireEvidenceViewModel> work = findWorkSummaryByServiceRecipientId();
            assertEquals(workPrior.size() + 2, work.size());

            List<QuestionnaireEvidenceViewModel> workWithAttachments = findWorkSummaryWithAttachmentsByServiceRecipientId();
            assertEquals(1, workWithAttachments.size());
        }
    }

    @Override
    protected List<QuestionnaireEvidenceViewModel> findWorkSummaryByServiceRecipientId() {
        return questionnaireEvidenceActor.findAllQuestionnaireWorkSummaryByServiceRecipientId(
                getServiceRecipientId(), questionnaire.nameAsLowercase(), false).getBody();
    }

    protected List<QuestionnaireEvidenceViewModel> findWorkSummaryWithAttachmentsByServiceRecipientId() {
        return questionnaireEvidenceActor.findAttachmentsQuestionnaireWorkSummaryByServiceRecipientId(
                getServiceRecipientId(), questionnaire.nameAsLowercase()).getBody();
    }

    public static QuestionAnswerCommandViewModel createQuestionAnswerCommandViewModel(
            UUID workUuid, @Nullable Instant timestampCreated, int serviceRecipientId, EvidenceGroup evidenceGroup,
            EvidenceTask evidenceTask, long questionDefId,
            String answer
            ) {

        QuestionAnswerCommandViewModel qac = new QuestionAnswerCommandViewModel(
                workUuid, serviceRecipientId, evidenceGroup, evidenceTask, questionDefId);
        if (timestampCreated != null) {
            qac.timestamp = timestampCreated;
        }
        if (answer != null) {
            qac.answerChange = ChangeViewModel.create(null, answer);
        }
        return qac;
    }

}
