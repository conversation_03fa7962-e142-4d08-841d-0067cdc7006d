package com.ecco.acceptancetests.api.evidence

import com.ecco.data.client.ServiceOptions
import com.ecco.dom.EvidenceGroup
import com.ecco.dto.ChangeViewModel
import com.ecco.evidence.EvidenceTask
import com.ecco.webApi.evidence.BaseCommandViewModel
import com.ecco.webApi.evidence.CommentCommandViewModel
import com.ecco.webApi.evidence.EvidenceFormSnapshotCommandHandler
import com.ecco.webApi.evidence.EvidenceFormSnapshotCommandViewModel
import com.ecco.webApi.evidence.EvidenceFormWorkViewModel
import com.ecco.webApi.evidence.SignWorkCommandViewModel
import com.fasterxml.jackson.databind.JsonNode
import org.joda.time.DateTimeZone
import org.joda.time.Instant
import org.joda.time.LocalDateTime
import org.junit.Assert.assertEquals
import org.junit.Assert.assertNotNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.io.IOException
import java.util.*
import kotlin.collections.reversed

/**
 * JSON snapshot tests, based on evidence tests but not utilising fixed EvidenceTask
 * or EvidenceGroup.
 * @see BaseEvidenceCommandAPITests
 */
class EvidenceFormSnapshotCommandAPITests : BaseReferralCommandAPITests(ServiceOptions.ACCOMMODATION) {
    private val formDefinitionUuid =
        UUID.fromString("00200000-0000-babe-babe-dadafee1600d") // from 'acceptanceTests', see 0e8491de
    private lateinit var workDateUpTo50DaysInPast: LocalDateTime
    private val evidenceTaskGroupKey = "customForm1"

    @BeforeEach
    fun createDefinitionData() {
        workDateUpTo50DaysInPast = LocalDateTime.now().minusDays(random.nextInt(50)).withMillisOfSecond(0)
    }

    @Test
    fun jsonPatching() {
        val jsonPatch = asJsonNode("""[{ "op": "add", "path": "/hello", "value": ["world"]}]""")
        val jsonOriginal = ""
        val jsonNew = EvidenceFormSnapshotCommandHandler.applyJsonPatch(jsonOriginal, jsonPatch, objectMapper)
        assertEquals("""{"hello":["world"]}""", jsonNew)
    }

    @Test
    fun `user can fill form and then edit form in subsequent work`() {
        val workUuid1 = UUID.randomUUID()
        val jsonPatch = asJsonNode("""[{ "op": "add", "path": "/hello", "value": ["world"]}]""")
        val created = workDateUpTo50DaysInPast.toDateTime(DateTimeZone.UTC)
        createCommandViewModel(
            BaseCommandViewModel.OPERATION_ADD,
            serviceRecipientId,
            EvidenceTask.CUSTOMFORM_1.taskName,
            workUuid1,
            evidenceTaskGroupKey,
            jsonPatch,
            formDefinitionUuid,
            created.toInstant(),
            workDateUpTo50DaysInPast,
        ).let(commandActor::executeCommand)

        findSnapshotByUuid(workUuid1).apply {
            assertEquals("""{"hello":["world"]}""", form)
            assertEquals(formDefinitionUuid, formDefinitionUuid)
            assertEquals(workDateUpTo50DaysInPast, workDate)
            assertEquals(created.toLocalDateTime(), createdDate)
        }

        val workUuid2 = UUID.randomUUID()
        val jsonUpdate =
            asJsonNode(
                """ [{
                "op": "replace",
                "path": "/hello",
                "value": ["earth", "mars", "europa"]
            }]""",
            )

        createCommandViewModel(
            BaseCommandViewModel.OPERATION_ADD,
            serviceRecipientId,
            EvidenceTask.CUSTOMFORM_1.taskName,
            workUuid2,
            evidenceTaskGroupKey,
            jsonUpdate,
            formDefinitionUuid,
            created.plusHours(1).toInstant(),
            workDateUpTo50DaysInPast.plusHours(1),
        ).let(commandActor::executeCommand)
        findSnapshotByUuid(workUuid2).apply {
            assertEquals("""{"hello":["earth","mars","europa"]}""", form)
            assertEquals(formDefinitionUuid, formDefinitionUuid)
            assertEquals(workDateUpTo50DaysInPast.plusHours(1), workDate)
            assertEquals(created.plusHours(1).toLocalDateTime(), createdDate)
        }

        findLatestSnapshotsPerEvidenceGroup().apply {
            assertEquals(1, size)
            assertEquals(evidenceTaskGroupKey, get(0).evidenceGroupKey)
        }
    }

    private fun findLatestSnapshotsPerEvidenceGroup(): List<EvidenceFormWorkViewModel> =
        evidenceFormSnapshotActor.findLatestSnapshotsPerEvidenceGroupByServiceRecipientId(serviceRecipientId).body!!

    @org.junit.jupiter.api.Test
    fun `user can edit historical item`() {
        val workUuid = UUID.randomUUID()
        val jsonPatch = asJsonNode("""[{ "op": "add", "path": "/hello", "value": ["world"]}]""")
        val created = workDateUpTo50DaysInPast.toDateTime(DateTimeZone.UTC)
        createCommandViewModel(
            BaseCommandViewModel.OPERATION_ADD,
            serviceRecipientId,
            EvidenceTask.CUSTOMFORM_1.taskName,
            workUuid,
            evidenceTaskGroupKey,
            jsonPatch,
            formDefinitionUuid,
            created.toInstant(),
            workDateUpTo50DaysInPast,
        ).let(commandActor::executeCommand)

        findSnapshotByUuid(workUuid).apply {
            assertEquals("""{"hello":["world"]}""", form)
            assertEquals(formDefinitionUuid, formDefinitionUuid)
            assertEquals(workDateUpTo50DaysInPast, workDate)
            assertEquals(created.toLocalDateTime(), createdDate)
        }

        val jsonUpdate =
            asJsonNode(
                """ [{
                "op": "replace",
                "path": "/hello",
                "value": ["earth", "mars", "europa"]
            }]""",
            )

        createCommandViewModel(
            BaseCommandViewModel.OPERATION_UPDATE,
            serviceRecipientId,
            EvidenceTask.CUSTOMFORM_1.taskName,
            workUuid,
            evidenceTaskGroupKey,
            jsonUpdate,
            formDefinitionUuid,
            created.plusHours(1).toInstant(),
            workDateUpTo50DaysInPast.plusHours(1),
        ).let(commandActor::executeCommand)
        findSnapshotByUuid(workUuid).apply {
            assertEquals("""{"hello":["earth","mars","europa"]}""", form)
            assertEquals(formDefinitionUuid, formDefinitionUuid)
            assertEquals(workDateUpTo50DaysInPast.plusHours(1), workDate)
            assertEquals(
                created.toLocalDateTime(),
                createdDate,
            ) // NOTE: createdDate on this API is that of the work, not the snapshot
        }
    }

    @org.junit.jupiter.api.Test
    fun commentSave_happyPath() {
        val workUuid = UUID.randomUUID()
        var ccvm: CommentCommandViewModel

        // WHEN
        run {
            // see also @Before createUniqueClientAndReferral

            // a comment and piece of work exists
            ccvm =
                createCommentCommandViewModel(
                    workUuid,
                    rvm.serviceRecipientId,
                    EvidenceGroup.CUSTOMFORM1,
                    EvidenceTask.CUSTOMFORM_1,
                    "save my note",
                    null,
                    workDateUpTo50DaysInPast,
                    10 + Random().nextInt(145),
                )
            commandActor.executeCommand(ccvm)
        }

        // THEN
        run {
            // EvidenceViewModel lastEvidence = readBackEvidence(vm.workUuid);
            val workSummary = findSnapshotByUuid(workUuid)
            assertNotNull(workSummary)
            assertEquals(ccvm.comment!!.to, workSummary.comment)
        }
    }

    // NB test to indicate that we can have a no-op with an unknown work uuid
    @Test
    fun signatureSave_noWork() {
        val unknownWorkUuid = UUID.randomUUID()
        val redDot =
            "iVBORw0KGgoAAAANSUhEUgAAAAUAAAAFCAYAAACNbyblAAAAHElEQVQI12P4//8/w38GIAXDIBKE0DHxgljNBAAO9TXL0Y4OHwAAAABJRU5ErkJggg=="
        val sigCmd =
            SignWorkCommandViewModel(
                rvm.serviceRecipientId,
                Collections.singletonList(unknownWorkUuid),
                redDot,
                LocalDateTime.now(),
            )

        // WHEN
        run {
            commandActor.executeCommand(sigCmd)
        }
    }

    private fun asJsonNode(jsonText: String): JsonNode = try {
        objectMapper.readTree(jsonText)
    } catch (e: IOException) {
        throw RuntimeException(e)
    }

    private fun createCommandViewModel(
        operation: String,
        serviceRecipientId: Int,
        taskName: String,
        workUuid: UUID,
        evidenceTaskGroupKey: String,
        jsonPatch: JsonNode,
        formDefinitionUuid: UUID,
        createdCommand: Instant,
        workDate: LocalDateTime,
    ): EvidenceFormSnapshotCommandViewModel {
        val cvm =
            EvidenceFormSnapshotCommandViewModel(
                operation,
                serviceRecipientId,
                taskName,
                evidenceTaskGroupKey,
                formDefinitionUuid,
            )
        cvm.workUuid = workUuid
        cvm.timestamp = createdCommand
        cvm.workDate = ChangeViewModel.changeNullTo(workDate)
        cvm.jsonPatch = jsonPatch
        return cvm
    }

    protected fun createCommentCommandViewModel(
        workUuid: UUID?,
        serviceRecipientId: Int,
        group: EvidenceGroup?,
        sourceTask: EvidenceTask?,
        comment: String?,
        createdCommand: Instant?,
        workDate: LocalDateTime?,
        minsSpent: Int?,
    ): CommentCommandViewModel {
        val cc = CommentCommandViewModel(workUuid, serviceRecipientId, group, sourceTask)
        if (createdCommand != null) {
            cc.timestamp = createdCommand
        }
        if (workDate != null) {
            cc.workDate = ChangeViewModel.create(null, workDate)
        }
        if (comment != null) {
            cc.comment = ChangeViewModel.create(null, comment)
        }
        if (minsSpent != null) {
            cc.minsSpent = ChangeViewModel.create(null, minsSpent)
        }
        return cc
    }

    private fun findSnapshotByUuid(uuid: UUID?): EvidenceFormWorkViewModel {
        val work = findSnapshotsByServiceRecipientIdAndEvidenceTaskGroupKey()
        return work
            .reversed()
            .stream()
            .filter { input: EvidenceFormWorkViewModel -> uuid == input.id }
            .findFirst()
            .get()
    }

    private fun findSnapshotsByServiceRecipientIdAndEvidenceTaskGroupKey(): List<EvidenceFormWorkViewModel> = evidenceFormSnapshotActor
        .findAllSnapshotsByServiceRecipientIdAndEvidenceTaskGroupKey(
            serviceRecipientId,
            evidenceTaskGroupKey,
        ).body!!
        .content
}