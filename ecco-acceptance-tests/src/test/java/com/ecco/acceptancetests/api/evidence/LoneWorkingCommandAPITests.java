package com.ecco.acceptancetests.api.evidence;

import com.ecco.data.client.ServiceOptions;
import com.ecco.dom.agreements.DaysOfWeek;
import com.ecco.dto.ChangeViewModel;
import com.ecco.evidence.EvidenceTask;
import com.ecco.webApi.calendar.CalendarEntryCommandViewModel;
import com.ecco.webApi.calendar.ContactCalendarEntryCommandViewModel;
import com.ecco.webApi.evidence.*;
import com.ecco.webApi.viewModels.UserViewModel;
import org.joda.time.Duration;
import org.joda.time.Instant;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static com.ecco.dom.EvidenceGroup.NEEDS;
import static com.ecco.webApi.evidence.BaseCommandViewModel.OPERATION_ADD;
import static com.ecco.dto.ChangeViewModel.changeNullTo;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.Assert.*;

/**
 * Test scenarios around staff recording support evidence using 'start' 'stop' (lone working)
 */
@SuppressWarnings("ConstantConditions")
public class LoneWorkingCommandAPITests extends BaseEvidenceCommandAPITests<EvidenceSupportWorkViewModel> {

    public LoneWorkingCommandAPITests() {
        super(NEEDS, EvidenceTask.NEEDS_ASSESSMENT, ServiceOptions.ACCOMMODATION);
    }

    /**
     * Test lone working when recording data against a calendar series
     */
    @Test
    public void loneWorkingScenarioOnNonConcrete() {
        loneWorkingScenario(false);
    }

    /**
     * Test lone working when recording data against a calendar concrete entry (not a series)
     */
    @Test
    public void loneWorkingScenarioOnConcrete() {
        loneWorkingScenario(true);;
    }

    private void loneWorkingScenario(boolean useConcreteEvent) {
        // GIVEN an event exists
        var user = userActor.getLoggedInUser().getBody();
        var eventUid = useConcreteEvent ? createEvent(user) : createEventNonConcrete(user);

        {
            // see also @Before createUniqueClientAndReferral

            // AND no evidence currently exists for the workUuid
            Optional<EvidenceSupportWorkViewModel> lastEvidence = findOptionalWorkByUuid(this.workUuid);
            assertFalse(lastEvidence.isPresent());

        }

        // WHEN create an associated contact command, and some work
        EvidenceAssociatedContactCommandViewModel vm;
        {
            // start the visit
            Instant whenStarted = Instant.now().minus(Duration.standardMinutes(60));
            // use the logged in user - we have a '@Before' on BaseJsonTest.loginAsSysadmin
            vm = new EvidenceAssociatedContactCommandViewModel(
                    this.workUuid,
                    eventUid,
                    getServiceRecipientId(), evidenceGroup,
                    defaultEvidenceTask, user.individual.contactId);
            vm.eventId = eventUid;
            vm.timestamp = whenStarted;
            vm.attendanceStatus = EvidenceAssociatedContactCommandViewModel.AttendanceStatus.START;
            LocationViewModel lvm = new LocationViewModel();
            lvm.lat = 2.00f;
            lvm.lon = 1.34f;
            vm.location = lvm;
            commandActor.executeCommand(vm);

            List<EvidenceAssociatedContactCommandViewModel> associatedCommands = associatedContactActor
                    .findAssociatedContactCommands(vm.serviceRecipientId, NEEDS).getBody();
            assertEquals(1L, associatedCommands.stream()
                    .filter(ac -> ac.workUuid.equals(this.workUuid))
                    .count());

            // now save a comment
            Instant whenEnded = whenStarted.plus(Duration.standardMinutes(55));
            sendCommentCommand(this.workUuid, "we did this work", whenEnded, workDateUpTo50DaysInPast);

            // now stop the visit
            vm = new EvidenceAssociatedContactCommandViewModel(
                    this.workUuid,
                    eventUid,
                    getServiceRecipientId(), evidenceGroup,
                    defaultEvidenceTask, user.individual.contactId);
            vm.attendanceStatus = EvidenceAssociatedContactCommandViewModel.AttendanceStatus.END;
            commandActor.executeCommand(vm);
        }

        // THEN check the associated contact command has saved the workUuid and start/end
        // AND check the snapshot table has updated
        {
            EvidenceSupportWorkViewModel lastEvidence = findWorkByUuid(this.workUuid);
            assertNotNull(lastEvidence);
            List<EvidenceAssociatedContactCommandViewModel> associatedCommands = associatedContactActor
                    .findAssociatedContactCommands(vm.serviceRecipientId, NEEDS).getBody();
            assertEquals(2L, associatedCommands.stream()
                    .filter(ac -> ac.workUuid.equals(this.workUuid))
                    .count());

            // could assert the start time, but the minsSpent is client-side currently
            //assertEquals(Integer.valueOf(55), lastEvidence.minsSpent);

            CalendarEventSnapshotDtoResource[] snapshots = loneWorkerActor.findAll().getBody();
            assertNotNull(snapshots);
            assertThat(snapshots).anyMatch(it -> it.eventUid.equals(eventUid)
                    && it.resourceContact.contactId.equals(user.individual.contactId)
                    && it.endInstant != null);
        }
    }

    @Test
    public void associatedContactCreatesWorkWithEventStatus() {

        // GIVEN an event exists
        var user = userActor.getLoggedInUser().getBody();
        var eventUid = createEvent(user);

        {
            // see also @Before createUniqueClientAndReferral

            // no evidence currently exists for the workUuid
            Optional<EvidenceSupportWorkViewModel> lastEvidence = findOptionalWorkByUuid(this.workUuid);
            assertFalse(lastEvidence.isPresent());
        }

        // WHEN create an associated contact command
        {
            // use the logged in user - we have a '@Before' on BaseJsonTest.loginAsSysadmin
            var vm = new EvidenceAssociatedContactCommandViewModel(
                    workUuid,
                    eventUid,
                    getServiceRecipientId(), evidenceGroup,
                    defaultEvidenceTask, user.individual.contactId);
            vm.attendanceStatus = EvidenceAssociatedContactCommandViewModel.AttendanceStatus.START;
            commandActor.executeCommand(vm);
            this.workUuid = vm.workUuid;
        }

        // THEN check the associated contact command has saved the workUuid
        {
            var lastEvidence = supportEvidenceActor.findOneSupportWorkByWorkUuid(getServiceRecipientId(), evidenceGroup, this.workUuid).getBody();
            assertNotNull(lastEvidence);
            assertNotNull(lastEvidence.eventSnapshot);
        }
    }

    @Test
    public void associatedContactJoinsToWork() {
        String commentToSave = "this is my piece of work for lone working";

        // GIVEN an event exists
        var user = userActor.getLoggedInUser().getBody();
        var eventUid = createEvent(user);

        {
            // see also @Before createUniqueClientAndReferral

            // a comment and piece of work exists
            sendCommentCommand(commentToSave, workDateUpTo50DaysInPast);
        }

        // WHEN create an associated contact command
        EvidenceAssociatedContactCommandViewModel vm;
        {
            // save some work with a statusChangeReason
            // use the logged in user - we have a '@Before' on BaseJsonTest.loginAsSysadmin
            vm = new EvidenceAssociatedContactCommandViewModel(
                    this.workUuid,
                    eventUid,
                    getServiceRecipientId(), evidenceGroup,
                    defaultEvidenceTask, user.individual.contactId);
            vm.attendanceStatus = EvidenceAssociatedContactCommandViewModel.AttendanceStatus.START;
            commandActor.executeCommand(vm);
            this.workUuid = vm.workUuid;
        }

        // THEN check the associated contact command has also saved
        {
            EvidenceSupportWorkViewModel lastEvidence = findWorkByUuid(this.workUuid);
            assertNotNull(lastEvidence);
            assertThat(lastEvidence.comment).isEqualToIgnoringCase(commentToSave);
            List<EvidenceAssociatedContactCommandViewModel> associatedCommands = associatedContactActor
                    .findAssociatedContactCommands(vm.serviceRecipientId, NEEDS).getBody();
            assertEquals(1L, associatedCommands.stream()
                .filter(ac -> ac.workUuid.equals(this.workUuid))
                .count());
        }
    }

    /**
     * Create a recurring event (a series, not a concrete entry)
     */
    // see ServiceRecipientCalendarEntryCommandAPITests#canCreateAppointment_repeatHappyPath
    private String createEventNonConcrete(UserViewModel user) {
        var vm = new CalendarEntryCommandViewModel(BaseCommandViewModel.OPERATION_ADD, null);
        // Friday
        LocalDate start = new LocalDate().withDayOfWeek(5);
        vm.setStartDate(ChangeViewModel.changeNullTo(start));
        vm.setAllDay(ChangeViewModel.changeNullTo(true));

        // repeat every Sunday, after the Friday start date
        DaysOfWeek dow = new DaysOfWeek();
        dow.setSunday(true);
        vm.setRepeatEveryDays(ChangeViewModel.changeNullTo(dow.daysAttending()));
        vm.setRepeatEveryWeeks(ChangeViewModel.changeNullTo(1)); // trigger recurring

        var cvm = new ContactCalendarEntryCommandViewModel(user.individual.contactId.intValue(), vm);
        var result = commandActor.executeCommand(cvm.getCalendarEntryViewModelParent()).getBody();
        assertTrue(result.isCommandSuccessful());
        assertNotNull(result.getId());
        var recurringEventId = result.getLink("self").getHref().split("eventIds=")[1];

        // check some calendar events have the recurringEventId, and return it
        var events = calendarActor.getEntriesByTime(start, start.plusWeeks(2), new Long[]{user.individual.contactId}, Collections.emptyList());
        return Arrays.stream(events.getBody()).filter(e -> e.isRecurrence() && e.getEntryId().startsWith(recurringEventId)).findFirst().get().getEntryId();
    }

    /**
     * Create a concrete event (not a series)
     */
    private String createEvent(UserViewModel user) {
        var vm = new CalendarEntryCommandViewModel(OPERATION_ADD, null);
        vm.setStartDate(changeNullTo(LocalDate.now()));
        vm.setAllDay(changeNullTo(true));
        var cvm = new ContactCalendarEntryCommandViewModel(user.individual.contactId.intValue(), vm);
        var result = commandActor.executeCommand(cvm).getBody();
        return result.getLink("self").getHref().split("eventIds=")[1];
    }

    @Override
    protected void ensureDefinitionIds() {
//        if (contactId == null) {
//        }
    }

    @Override
    protected List<EvidenceSupportWorkViewModel> findWorkSummaryByServiceRecipientId() {
        return supportEvidenceActor.findAllSupportWorkSummaryByServiceRecipientId(getServiceRecipientId(), NEEDS).getBody().getContent();
    }

}
