package com.ecco.acceptancetests.api.evidence;

import com.ecco.dom.EvidenceAction;
import com.ecco.webApi.evidence.GoalUpdateCommandViewModel;
import com.ecco.webApi.evidence.Schedule;
import org.joda.time.DateTimeZone;
import org.junit.jupiter.api.Test;

import java.util.UUID;

import static com.ecco.dto.ChangeViewModel.changeNullTo;
import static com.ecco.infrastructure.time.JodaToJDKAdapters.localDateToJDk;
import static org.junit.Assert.*;


/**
 * Tests that setting target schedule results in correct evidence snapshot
 */
@SuppressWarnings("FieldCanBeLocal")
public class TargetScheduleAPITests extends SupportCommonCommandAPITests {

    public TargetScheduleAPITests() {
        super();
    }

    @Test
    public void settingTargetScheduleProducesCorrectTargetDateOnSnapshot() {
        // GIVEN a schedule
        GoalUpdateCommandViewModel scheduledGoalVm;
        {
            // see also @Before createUniqueClientAndReferral

            scheduledGoalVm = new GoalUpdateCommandViewModel(workUuid, getServiceRecipientId(), evidenceGroup,
                    defaultEvidenceTask, actionDefId, UUID.randomUUID(), null);
            scheduledGoalVm.timestamp = now.toDateTime(DateTimeZone.UTC).toInstant();
            // targetDate can be used to specify a start date, but lets assume from work date
            var fromTomorrow = localDateToJDk(now.toLocalDate().plusDays(1));
            scheduledGoalVm.targetScheduleChange = changeNullTo(Schedule.from(fromTomorrow).getScheduleString());
            commandActor.executeCommand(scheduledGoalVm);

            sendCommentCommand("scheduled", workDateUpTo50DaysInPast);
        }

        // THEN schedule exists with targetDate set to first schedule
        {
            var actionVm = findWorkActionInstanceUuid(workUuid, scheduledGoalVm.actionInstanceUuid);
            var dueTomorrow = now.toLocalDate().plusDays(1);
            //assertEquals(scheduledGoalVm.targetScheduleChange.to, actionVm.targetSchedule);
            assert actionVm.targetDateTime != null;
            assertEquals(dueTomorrow, actionVm.targetDateTime.toLocalDate());
            assertEquals(EvidenceAction.isRelevant, actionVm.status);

            assertNull(actionVm.hierarchy);
            assertNull(actionVm.position);
        }
    }

    @Test
    public void targetScheduleEnding5DaysAgoShouldHaveNullTargetDateOnSnapshot() {
        // GIVEN a schedule
        GoalUpdateCommandViewModel scheduledGoalVm;
        {
            // see also @Before createUniqueClientAndReferral

            scheduledGoalVm = new GoalUpdateCommandViewModel(workUuid, getServiceRecipientId(), evidenceGroup,
                    defaultEvidenceTask, actionDefId, UUID.randomUUID(), null);
            scheduledGoalVm.timestamp = now.toDateTime(DateTimeZone.UTC).toInstant();

            // create an already expired schedule, such that no target date should be returned
            var scheduleFrom10DaysAgo = localDateToJDk(now.toLocalDate().minusDays(10));
            var scheduleEnd5DaysAgo = localDateToJDk(now.toLocalDate().minusDays(5));
            scheduledGoalVm.targetScheduleChange = changeNullTo(Schedule.from(scheduleFrom10DaysAgo, "*", scheduleEnd5DaysAgo).getScheduleString());
            commandActor.executeCommand(scheduledGoalVm);

            sendCommentCommand("scheduled in the past", workDateUpTo50DaysInPast);
        }

        // THEN schedule exists with no targetDate set
        {
            var actionVm = findWorkActionInstanceUuid(workUuid, scheduledGoalVm.actionInstanceUuid);
            //assertEquals(scheduledGoalVm.targetScheduleChange.to, actionVm.targetSchedule);
            assertNull(actionVm.targetDateTime);
            assertEquals(EvidenceAction.isRelevant, actionVm.status);

            assertNull(actionVm.hierarchy);
            assertNull(actionVm.position);
        }
    }
}
