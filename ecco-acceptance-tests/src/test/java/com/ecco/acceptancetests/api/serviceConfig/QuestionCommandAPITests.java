package com.ecco.acceptancetests.api.serviceConfig;

import com.ecco.test.support.UniqueDataService;
import com.ecco.acceptancetests.api.BaseJsonTest;
import com.ecco.serviceConfig.viewModel.QuestionGroupViewModel;
import com.ecco.serviceConfig.viewModel.QuestionViewModel;
import com.ecco.dto.ChangeViewModel;
import com.ecco.webApi.serviceConfig.QuestionCommandViewModel;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;
import java.util.function.Predicate;
import java.util.stream.Stream;

import static java.util.stream.Collectors.toList;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

public class QuestionCommandAPITests extends BaseJsonTest {

    //protected final static LocalDateTime now = new LocalDateTime();
    //private final UniqueDataService unique = UniqueDataService.instance;
    private static final String ADD = QuestionCommandViewModel.OPERATION_ADD;
    private static final String UPDATE = QuestionCommandViewModel.OPERATION_UPDATE;
    private static final String REMOVE = QuestionCommandViewModel.OPERATION_REMOVE;
    private Integer questionGroupId = null;

    private static Predicate<QuestionViewModel> matchQuestionName(final String name) {
        return input -> name.equals(input.name);
    }
    private static Predicate<QuestionGroupViewModel> matchQuestionGroupName(final String name) {
        return input -> name.equals(input.name);
    }

    private static final String[] questionNames = new String[] {
            UniqueDataService.instance.nameFor("quest to add"),
            UniqueDataService.instance.nameFor("quest to remove"),
            UniqueDataService.instance.nameFor("quest to update"),
            UniqueDataService.instance.nameFor("quest updated"),
            UniqueDataService.instance.nameFor("quest to disable")};

    @BeforeEach
    public void cleanBefore() {
        clean();
        this.questionGroupId = Arrays.stream(getAllQuestionGroups()).findFirst().get().id.intValue();
    }

    @AfterEach
    public void cleanAfter() {
        clean();
    }

    @Test
    public void canAdd() {
        canAdd(questionNames[0]);
    }

    @Test
    public void canAddCantRemove() {
        QuestionViewModel qvm = canAdd(questionNames[1]);

        QuestionCommandViewModel cmd = new QuestionCommandViewModel(REMOVE, null, qvm.id);
        commandActor.executeCommand(cmd);

        List<QuestionViewModel> filtered = getQuestion(qvm.name);
        assertEquals(1, filtered.size());
        assertFalse(filtered.get(0).disabled);
    }

    @Test
    public void canAddThenDisable() {
        QuestionViewModel qvm = canAdd(questionNames[4]);

        QuestionCommandViewModel cmd = new QuestionCommandViewModel(UPDATE, null, qvm.id);
        cmd.disableChange = ChangeViewModel.changeNullTo(true);
        commandActor.executeCommand(cmd);

        List<QuestionViewModel> filtered = getQuestion(qvm.name);
        assertEquals(1, filtered.size());
        assertTrue(filtered.get(0).disabled);
    }

    @Test
    public void canAddThenUpdate() {
        QuestionViewModel qvm = canAdd(questionNames[2]);
        List<QuestionViewModel> filtered = getQuestion(qvm.name);
        assertEquals(1, filtered.size());
        assertEquals(qvm.id, filtered.get(0).id);

        String newName = questionNames[3];
        QuestionCommandViewModel cmd = new QuestionCommandViewModel(UPDATE, null, qvm.id);
        cmd.nameChange = ChangeViewModel.create(qvm.name, newName);
        commandActor.executeCommand(cmd);

        filtered = getQuestion(newName);
        assertEquals(1, filtered.size());
        assertEquals(qvm.id, filtered.get(0).id);
        assertEquals(newName, filtered.get(0).name);
    }

    private QuestionViewModel canAdd(String name) {
        QuestionCommandViewModel qvm = new QuestionCommandViewModel(ADD, this.questionGroupId, null);
        qvm.nameChange = ChangeViewModel.create(null, name);
        commandActor.executeCommand(qvm);

        List<QuestionViewModel> filtered = getQuestion(name);
        assertEquals(1, filtered.size());
        return filtered.get(0);
    }

    private QuestionViewModel[] getAllQuestions() {
        return questionGroupActor.getAllQuestions().getBody();
    }

    private QuestionGroupViewModel[] getAllQuestionGroups() {
        return questionGroupActor.findAllQuestionGroups().getBody();
    }

    private List<QuestionViewModel> getQuestion(String name) {
        return Stream.of(getAllQuestions())
                .filter(matchQuestionName(name))
                .collect(toList());
    }

    private List<QuestionGroupViewModel> getQuestionGroup(String name) {
        return Stream.of(getAllQuestionGroups())
                .filter(matchQuestionGroupName(name))
                .collect(toList());
    }

    private void clean() {
        for (String questionName : questionNames) {
            Stream<QuestionViewModel> filtered = Stream.of(getAllQuestions()).filter(matchQuestionName(questionName));
            filtered.forEach(questionViewModel -> {
                QuestionCommandViewModel cmd = new QuestionCommandViewModel(REMOVE, null, questionViewModel.id);
                commandActor.executeCommand(cmd);
            });
        }
    }

}
