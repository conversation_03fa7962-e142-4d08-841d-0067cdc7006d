package com.ecco.acceptancetests.api;

import static org.assertj.core.api.Assertions.assertThat;

import com.ecco.data.client.actors.BaseActor;
import org.junit.jupiter.api.Test;

public class BaseActorTest {

    @Test
    public void testUrlAtBaseUrlEndpoint() {
        String nonHostUrl = "http:/ecco-war/api/rota/workers:all/view?serviceRecipientFilter=buildings:1046";
        String result = BaseActor.urlAtBaseUrlHost(nonHostUrl);
        assertThat(result).isEqualTo("http://" + BaseActor.baseUri.getHost() + ":" + BaseActor.baseUri.getPort()
                + "/ecco-war/api/rota/workers:all/view?serviceRecipientFilter=buildings:1046");
    }

}
