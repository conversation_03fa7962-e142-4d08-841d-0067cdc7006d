package com.ecco.acceptancetests.api.resources;

import static org.assertj.core.api.Assertions.assertThat;

import com.ecco.acceptancetests.api.BaseJsonTest;
import org.junit.jupiter.api.Test;
import org.springframework.http.HttpStatus;

public class HostedUiResourcesTest extends BaseJsonTest {

    @Test
    public void shouldBeServingDebugJsFiles() {
        var response = resourcesActor.getUiJs("debug/ecco-components.js");
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
    }

    @Test
    public void shouldBeServingDistJsFiles() {
        var response = resourcesActor.getUiJs("dist/ecco-components.js");
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
    }

    @Test
    public void shouldBeServingLoginPageJs() {
        var response = resourcesActor.getNocacheJs("offline/login-page.js");
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
    }
}
