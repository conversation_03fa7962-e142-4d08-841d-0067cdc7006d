package com.ecco.acceptancetests.api.serviceConfig;

import com.ecco.test.support.UniqueDataService;
import com.ecco.acceptancetests.api.BaseJsonTest;
import com.ecco.serviceConfig.viewModel.ServiceTypeViewModel;
import com.ecco.dto.ChangeViewModel;
import com.ecco.webApi.featureConfig.SessionDataViewModel;
import com.ecco.webApi.serviceConfig.TaskDefinitionEntryCommandViewModel;
import com.ecco.webApi.viewModels.Result;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.web.client.HttpServerErrorException;

import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.Assert.*;

public class TaskDefinitionEntryCommandAPITests extends BaseJsonTest {

    private static String ADD = TaskDefinitionEntryCommandViewModel.OPERATION_ADD;
    private static String UPDATE = TaskDefinitionEntryCommandViewModel.OPERATION_UPDATE;
    private static String REMOVE = TaskDefinitionEntryCommandViewModel.OPERATION_REMOVE;

    private static SessionDataViewModel sdvm;
    private static volatile Integer serviceTypeId;
    private AtomicInteger counter = new AtomicInteger(1);
    private static String serviceTypeNamePrefix = "another servicetype # ";

    @BeforeEach
    public void createServiceTypeData() {
        // emulating @BeforeClass but we want our injected services
        if (sdvm == null) {
            sdvm = sessionDataActor.getSessionData().getBody();
        }

        // create a new service type per test
        String serviceTypeName = UniqueDataService.instance.nameFor(serviceTypeNamePrefix)
                .concat(Integer.toString(counter.getAndIncrement()));
        serviceTypeId = serviceTypeActor.createServiceType(serviceTypeName, false).id;
    }

    @AfterEach
    public void cleanAfter() {
        // close the service type created for the test
        String serviceTypeName = UniqueDataService.instance.nameFor(serviceTypeNamePrefix).concat(Integer.toString(counter.get() - 1));
        serviceTypeActor.removeServiceTypes(serviceTypeName);
    }

    @Test
    public void canAcceptSomeCommand() {
        String taskName = sdvm.taskDefinitions.get(0).name;
        TaskDefinitionEntryCommandViewModel tvm = createCommand(ADD, taskName);

        Result result = commandActor.executeCommand(tvm).getBody();
        assertTrue(result.isCommandSuccessful());
        assertNotNull(result.getId());
    }

    @Test
    public void canCreate() {
        String taskName = sdvm.taskDefinitions.get(0).name;
        createEntry(taskName);

        ServiceTypeViewModel svm = serviceTypeActor.getServiceTypeById(serviceTypeId).getBody();
        assertEquals(serviceTypeId, svm.id);
        assertEquals(1, svm.taskDefinitionEntries.size());
        assertEquals(taskName, svm.taskDefinitionEntries.get(0).name);
    }

    // HTTP 400 - Bad Request (Valid command but failed to execute) thrown by spring-data when deleting something that doesn't exist
    @Test
    public void cannotRemoveWhatIsNotThere() {
        // with a new service type for this test, we won't have any tasks assigned yet
        String taskName = sdvm.taskDefinitions.get(0).name;
        TaskDefinitionEntryCommandViewModel tvm = createCommand(REMOVE, taskName);

        assertThrows(HttpServerErrorException.class, () -> commandActor.executeCommand(tvm));
    }

    // HTTP 500 - Internal server error (NPE when can't find referral aspect)
    @Test
    public void cannotAddUnknownTaskDefinition() {
        String taskName = "random unknown task definition";
        TaskDefinitionEntryCommandViewModel tvm = createCommand(ADD, taskName);

        assertThrows(HttpServerErrorException.class, () -> commandActor.executeCommand(tvm));
    }

    @Test
    public void canCreateAndRemove() {
        String taskName = sdvm.taskDefinitions.get(0).name;
        createEntry(taskName);

        TaskDefinitionEntryCommandViewModel tvm = createCommand(REMOVE, taskName);
        commandActor.executeCommand(tvm);

        ServiceTypeViewModel svm = serviceTypeActor.getServiceTypeById(serviceTypeId).getBody();
        assertEquals(serviceTypeId, svm.id);
        assertEquals(0, svm.taskDefinitionEntries.size());
    }

    @Test
    public void canUpdateAllowNext() {
        String taskName = sdvm.taskDefinitions.get(0).name;
        createEntry(taskName);

        TaskDefinitionEntryCommandViewModel tvm = createCommand(UPDATE, taskName);
        tvm.allowNextChange = ChangeViewModel.create(false, true);
        commandActor.executeCommand(tvm);

        ServiceTypeViewModel svm = serviceTypeActor.getServiceTypeById(serviceTypeId).getBody();
        assertEquals(serviceTypeId, svm.id);
        assertEquals(1, svm.taskDefinitionEntries.size());
        assertTrue(svm.taskDefinitionEntries.get(0).allowNext);
    }

    @Test
    public void canUpdateOrderby() {
        String taskName = sdvm.taskDefinitions.get(0).name;
        createEntry(taskName);

        TaskDefinitionEntryCommandViewModel tvm = createCommand(UPDATE, taskName);
        // NB produce a WARN that the from is not the same (is null)
        tvm.orderbyChange = ChangeViewModel.create(10, 5);
        commandActor.executeCommand(tvm);

        ServiceTypeViewModel svm = serviceTypeActor.getServiceTypeById(serviceTypeId).getBody();
        assertEquals(serviceTypeId, svm.id);
        assertEquals(1, svm.taskDefinitionEntries.size());
        assertEquals(5, svm.taskDefinitionEntries.get(0).orderby);
    }

    private TaskDefinitionEntryCommandViewModel createCommand(String operation, String taskName) {
        return new TaskDefinitionEntryCommandViewModel(operation, serviceTypeId, taskName);
    }

    private void createEntry(String taskName) {
        TaskDefinitionEntryCommandViewModel tvm = createCommand(ADD, taskName);
        commandActor.executeCommand(tvm);
    }
}