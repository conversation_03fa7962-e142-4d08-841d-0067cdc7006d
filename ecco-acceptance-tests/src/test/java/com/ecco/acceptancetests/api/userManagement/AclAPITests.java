package com.ecco.acceptancetests.api.userManagement;

import com.ecco.acceptancetests.api.BaseJsonTest;

import org.junit.jupiter.api.Test;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import static org.hamcrest.CoreMatchers.is;
import static org.junit.Assert.assertThat;

public class AclAPITests extends BaseJsonTest {

    @Test
    public void aclEnabledCallSucceeds() {
        boolean isEnabled = aclEnabled();
        System.out.println("enableAcls: " + isEnabled);
    }

    private boolean aclEnabled() {
        ResponseEntity<Boolean> response = adminActor.isAclEnabled();
        assertThat(response.getStatusCode(), is(HttpStatus.OK));
        return response.getBody();
    }

}
