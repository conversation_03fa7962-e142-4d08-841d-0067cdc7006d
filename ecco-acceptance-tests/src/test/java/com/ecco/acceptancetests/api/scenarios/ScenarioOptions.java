package com.ecco.acceptancetests.api.scenarios;

import org.joda.time.LocalDateTime;

public class ScenarioOptions {

    private LocalDateTime startDate;
    private int referralCount;
    private int meanTaskPercent;
    private int varianceTaskPercent;
    private int stepSourcePercentSplit = 80;
    private int stepDataProtectionPercentSplit = 80;
    private int stepAcceptOnServicePercentSplit = 80;

    /** in past because we add dates to this */
    public ScenarioOptions fromStartDateInPast(LocalDateTime startDate) {
        this.startDate = startDate;
        return this;
    }

    public ScenarioOptions withReferralCount(int count) {
        this.referralCount = count;
        return this;
    }

    /**
     * A percentage indication of the progression of tasks/steps that most referrals want to be at
     */
    public ScenarioOptions withMeanTaskPercent(int factor) {
        if (factor > 100) {
            factor = 100;
        }
        this.meanTaskPercent = factor;
        return this;
    }

    /**
     * A percentage indication of the number of tasks to be ex/included in the first deviation (about 70%)
     * from the mean task/step. For a 10-step referral process, values of 20 here would result in 2 tasks
     * being added or removed from the number of tasks to complete.
     */
    public ScenarioOptions withVarianceTaskPercent(int factor) {
        if (factor > 100) {
            factor = 100;
        }
        this.varianceTaskPercent = factor;
        return this;
    }

    public LocalDateTime getStartDate() {
        return startDate;
    }

    public int getMeanTaskPercent() {
        return meanTaskPercent;
    }

    public int getVarianceTaskPercent() {
        return varianceTaskPercent;
    }

    public int getReferralCount() {
        return referralCount;
    }

    public int getStepSourcePercentSplit() {
        return stepSourcePercentSplit;
    }

    public int getStepDataProtectionPercentSplit() {
        return stepDataProtectionPercentSplit;
    }

    public int getStepAcceptOnServicePercentSplit() {
        return stepAcceptOnServicePercentSplit;
    }
}
