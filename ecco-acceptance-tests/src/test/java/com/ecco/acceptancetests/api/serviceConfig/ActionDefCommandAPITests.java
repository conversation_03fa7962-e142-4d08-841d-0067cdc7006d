package com.ecco.acceptancetests.api.serviceConfig;

import com.ecco.test.support.UniqueDataService;
import com.ecco.acceptancetests.api.BaseJsonTest;
import com.ecco.serviceConfig.viewModel.ActionViewModel;
import com.ecco.serviceConfig.viewModel.OutcomeViewModel;
import com.ecco.webApi.evidence.BaseCommandViewModel;
import com.ecco.dto.ChangeViewModel;
import com.ecco.webApi.serviceConfig.ActionDefCommandViewModel;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.UUID;

import static org.junit.Assert.*;

public class ActionDefCommandAPITests extends BaseJsonTest {

    private static String[] actionDefNames = new String[] {
            UniqueDataService.instance.nameFor("actionDef to add"),
            UniqueDataService.instance.nameFor("actionDef to remove"),
            UniqueDataService.instance.nameFor("actionDef to update"),
            UniqueDataService.instance.nameFor("actionDef updated")};

    protected UUID actionGroupDefId;

    @BeforeEach
    public void createDefinitionData() {
        if (actionGroupDefId == null) {
            List<OutcomeViewModel> outcomes = outcomeActor.findAllByServiceName("demo-all-workflow").getBody();
            actionGroupDefId = outcomes.stream()
                    .filter(input -> "economic wellbeing".equals(input.name))
                    .findFirst().get().actionGroups.get(0).uuid;
        }
    }

    @Test
    public void canAddNeeds() { canAddNeeds(actionDefNames[0]); }

    @Test
    public void canAddThenUpdate() {
        ActionViewModel vm = canAddNeeds(actionDefNames[2]);

        String newName = actionDefNames[3];
        ActionDefCommandViewModel cmd = new ActionDefCommandViewModel(BaseCommandViewModel.OPERATION_UPDATE, vm.uuid);
        cmd.nameChange = ChangeViewModel.create(vm.name, newName);
        commandActor.executeCommand(cmd);

        ActionViewModel retrieved = actionActor.findByUUID(vm.uuid).getBody();
        assertNotNull(retrieved);
        assertEquals(newName, retrieved.name);
    }

    @Test
    public void canAddThenHide() {
        ActionViewModel vm = canAddNeeds(actionDefNames[2]);

        ActionDefCommandViewModel cmd = new ActionDefCommandViewModel(BaseCommandViewModel.OPERATION_REMOVE, vm.uuid);
        commandActor.executeCommand(cmd);

        ActionViewModel retrieved = actionActor.findByUUID(vm.uuid).getBody();
        assertNotNull(retrieved);
        assertTrue(retrieved.disabled);
    }

    private ActionViewModel canAddNeeds(String name) {
        ActionDefCommandViewModel vm = new ActionDefCommandViewModel(BaseCommandViewModel.OPERATION_ADD, UUID.randomUUID());
        vm.actionGroupDefUuid = this.actionGroupDefId;
        return canAddActionDef(vm, name);
    }

    private ActionViewModel canAddActionDef(ActionDefCommandViewModel cvm, String name) {
        cvm.nameChange = ChangeViewModel.changeNullTo(name);
        commandActor.executeCommand(cvm);

        ActionViewModel retrieved = actionActor.findByUUID(cvm.actionDefUuid).getBody();
        assertNotNull(retrieved);
        assertEquals(cvm.actionDefUuid, retrieved.uuid);
        assertEquals(cvm.nameChange.to, retrieved.name);
        return retrieved;
    }

}
