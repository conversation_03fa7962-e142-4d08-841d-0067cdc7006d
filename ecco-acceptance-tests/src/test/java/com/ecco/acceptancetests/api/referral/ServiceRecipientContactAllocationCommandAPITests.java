package com.ecco.acceptancetests.api.referral;

import com.ecco.acceptancetests.api.BaseJsonTest;
import com.ecco.config.service.SettingsService;
import com.ecco.data.client.ServiceOptions;
import com.ecco.service.AgencyScope;
import com.ecco.webApi.contacts.*;
import com.ecco.webApi.evidence.ReferralViewModel;
import com.ecco.webApi.viewModels.Result;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Arrays;

import static org.junit.Assert.*;

public class ServiceRecipientContactAllocationCommandAPITests extends BaseJsonTest {

    static private ReferralViewModel rvm, rvm2;
    static private String companyName, outsideScopeCompanyName;

    protected final ServiceOptions service = ServiceOptions.ACCOMMODATION;

    @BeforeEach
    public void createClientAndReferral() {
        // emulating @BeforeClass but we want our injected services
        if (rvm == null) {
            LocalDate received = LocalDate.now();
            rvm = referralActor.createReferralAsStarted("contact", "allocation", received, service);
        }
        if (rvm2 == null) {
            LocalDate received = LocalDate.now();
            rvm2 = referralActor.createReferralAsStarted("contact", "other-allocation", received, ServiceOptions.FLOATING_SUPPORT);
        }
    }

    @Test
    public void canCreateAndFindWithAndWithoutContext() {
        String companyName = unique.nameFor("Azkaban Police Service");
        String outsideScopeCompanyName = unique.nameFor("Outside Police Service");
        // NB companyName is created with context of the ACCOMMODATION service
        // NB outsideScopeCompanyName is created with context of the FLOATING_SUPPORT service
        create(companyName, outsideScopeCompanyName);

        // put setting back
        settingActor.changeSetting(SettingsService.Namespace.AgencyScope, AgencyScope.class.getSimpleName(), AgencyScope.GLOBAL.name());

        // find all agencies across scopes, since default is GLOBAL
        var contacts = contactActor.getAgenciesScoped(rvm.serviceRecipientId).getBody();
        assertNotNull(contacts);
        var matches = Arrays.stream(contacts).filter(a -> a.companyName != null)
                .filter(a -> a.companyName.equals(companyName) || a.companyName.equals(outsideScopeCompanyName));
        // both agencies are found in GLOBAL scope
        assertEquals(2, matches.count());

        // find only agencies with the same scope
        settingActor.changeSetting(SettingsService.Namespace.AgencyScope, AgencyScope.class.getSimpleName(), AgencyScope.SERVICE.name());
        // find contacts on the ACCOMMODATION service - which should be 'companyName'
        contacts = contactActor.getAgenciesScoped(rvm.serviceRecipientId).getBody();
        // we need to trigger the professionals load verifies the hibernate loading works
        //var professionalsLoad = contactActor.getProfessionalsScoped(rvm.serviceRecipientId).getBody();
        assertNotNull(contacts);
        matches = Arrays.stream(contacts).filter(a -> a.companyName != null).filter(a -> a.companyName.equals(companyName));
        assertEquals(1, matches.count());
        // doesn't find agency on floating support
        var noMatches = Arrays.stream(contacts).filter(a -> a.companyName != null).filter(a -> a.companyName.equals(outsideScopeCompanyName));
        assertEquals(0, noMatches.count());

        // put setting back
        settingActor.changeSetting(SettingsService.Namespace.AgencyScope, AgencyScope.class.getSimpleName(), AgencyScope.GLOBAL.name());
    }

    private void create(String companyName, String outsideCompanyName) {

        // scoped agency
        AgencyViewModel agency = new AgencyViewModel();
        agency.contextId = rvm.serviceRecipientId;
        agency.companyName = companyName;
        var agencyId = Integer.parseInt(contactActor.createAgency(agency).getBody().getId());
        int id = createIndividual(agencyId, unique.firstNameFor("Sirius"), unique.lastNameFor("Snape"));
        IndividualViewModel ivm = contactActor.getContactById(id).getBody();
        assertNotNull(ivm);

        // unscoped agency - contextId is floating support service
        AgencyViewModel outsideScope = new AgencyViewModel();
        outsideScope.contextId = rvm2.serviceRecipientId;
        outsideScope.companyName = outsideCompanyName;
        contactActor.createAgency(outsideScope);
    }

    private int createIndividual(int agencyId, String firstName, String lastName) {
        IndividualViewModel ivm = new IndividualViewModel();
        ivm.setOrganisationId(Integer.valueOf(agencyId).longValue());
        ivm.setFirstName(firstName);
        ivm.setLastName(lastName);
        Result r = contactActor.createIndividual(ivm).getBody();
        return Integer.parseInt(r.getId());
    }

}