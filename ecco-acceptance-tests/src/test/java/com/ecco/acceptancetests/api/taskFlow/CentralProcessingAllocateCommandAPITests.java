package com.ecco.acceptancetests.api.taskFlow;

import com.ecco.acceptancetests.api.BaseJsonTest;
import com.ecco.data.client.ServiceOptions;
import com.ecco.dom.ReferralStatusName;
import com.ecco.dom.ReportCriteriaDto;
import com.ecco.serviceConfig.viewModel.ProjectViewModel;
import com.ecco.dto.ServiceViewModel;
import com.ecco.serviceConfig.viewModel.ServiceTypeViewModel;
import com.ecco.dto.ChangeViewModel;
import com.ecco.webApi.evidence.ReferralViewModel;
import com.ecco.webApi.featureConfig.SessionDataViewModel;
import com.ecco.webApi.taskFlow.ReferralTaskAllocateServiceCommandViewModel;
import com.ecco.webApi.taskFlow.ReferralTaskExitCommandViewModel;
import com.ecco.webApi.viewModels.Result;
import com.ecco.webApi.viewModels.ServicesViewModel;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.joda.time.LocalDate;
import org.joda.time.format.ISODateTimeFormat;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Stream;

import static java.util.stream.Collectors.toList;
import static org.hamcrest.CoreMatchers.is;
import static org.junit.Assert.*;

public class CentralProcessingAllocateCommandAPITests extends BaseJsonTest {

    static Predicate<ServiceViewModel> matchServiceName(final String serviceName) {
        return input -> serviceName.equals(input.name);
    }

    private static Predicate<ReferralViewModel> matchServiceAllocation(Integer serviceCategorisationId) {
        return input -> serviceCategorisationId.equals(input.serviceAllocationId);
    }

    private static Predicate<ReferralViewModel> matchParentReferralId(final long parentReferralId) {
        return input -> input.parentReferralId != null && input.parentReferralId.equals(parentReferralId);
    }

    private Integer serviceRecipientId;
    private static Long referralId;
    private static ServiceViewModel accomViewModel;
    private static ServiceViewModel floatingViewModel;
    private HashMap<Long, ServiceTypeViewModel> serviceTypesById = new HashMap<>();
    private SessionDataViewModel sessionData;

    @BeforeEach
    public void createReferralData() {
        // emulating @BeforeClass but we want our injected services
        ReferralViewModel referral = createReferral();
        referralId = referral.referralId;
        serviceRecipientId = referral.serviceRecipientId;
        accomViewModel = getAndCheckServiceExists(ServiceOptions.ACCOMMODATION.getServiceName());
        serviceTypesById.put(accomViewModel.serviceTypeId, serviceTypeActor.getServiceTypeById(accomViewModel.serviceTypeId).getBody());
        floatingViewModel = getAndCheckServiceExists(ServiceOptions.FLOATING_SUPPORT.getServiceName());
        serviceTypesById.put(floatingViewModel.serviceTypeId, serviceTypeActor.getServiceTypeById(floatingViewModel.serviceTypeId).getBody());
        sessionData = sessionDataActor.getSessionData().getBody();
    }

    @Test
    public void allocateReferralToOneService() {
        ReferralViewModel[] list = referralsListNow();
        int priorCount = list.length;

        ServiceViewModel svm = createViewModel(accomViewModel.name, accomViewModel.id, null, null);
        allocate(svm);

        List<ReferralViewModel> filtered = extractFromResultMatchingReferralId(priorCount, 1);
        verifyFirstOnService(sessionData, filtered, accomViewModel.name, null, accomViewModel.serviceTypeId);
    }

    @Test
    public void allocateReferralToOneServiceWithOneProject() {
        ReferralViewModel[] list = referralsListNow();
        int priorCount = list.length;

        ServiceViewModel svm = createViewModel(accomViewModel.name, accomViewModel.id,
                accomViewModel.projects.get(0).name, accomViewModel.projects.get(0).id);
        allocate(svm);

        List<ReferralViewModel> filtered = extractFromResultMatchingReferralId(priorCount, 1);
        verifyFirstOnService(sessionData, filtered, accomViewModel.name, accomViewModel.projects.get(0).name, accomViewModel.serviceTypeId);
    }

    @Test
    public void allocateReferralToManyServicesWithAProject() {
        ReferralViewModel[] list = referralsListNow();
        int priorCount = list.length;

        ServiceViewModel svm1 = createViewModel(floatingViewModel.name, floatingViewModel.id, null, null);
        ServiceViewModel svm2 = createViewModel(accomViewModel.name, accomViewModel.id,
                accomViewModel.projects.get(0).name, accomViewModel.projects.get(0).id);
        allocate(svm1, svm2);

        List<ReferralViewModel> filtered = extractFromResultMatchingReferralId(priorCount, 2);

        var sessionData = sessionDataActor.getSessionData().getBody();
        verifyFirstOnService(sessionData, filtered, floatingViewModel.name, null, floatingViewModel.serviceTypeId);
        verifyFirstOnService(sessionData, filtered, accomViewModel.name, accomViewModel.projects.get(0).name, accomViewModel.serviceTypeId);
    }

    @Test
    public void allocateReferralToOneServiceWithManyProjects() {

        ServiceViewModel serviceTo = createViewModel(accomViewModel.name, accomViewModel.id,
                accomViewModel.projects.get(0).name, accomViewModel.projects.get(0).id);
        // add the extra project
        ProjectViewModel project6 = new ProjectViewModel();
        project6.name = accomViewModel.projects.get(1).name;
        project6.id = accomViewModel.projects.get(1).id;
        serviceTo.addProject(project6);

        //assertThrows(HttpClientErrorException.class, () -> allocate(serviceTo));
        ReferralViewModel[] list = referralsListNow();
        int priorCount = list.length;

        allocate(serviceTo);
        //assertThat("exception should have been called to prevent multiple projects", true, is(false));
        extractFromResultMatchingReferralId(priorCount, 2);
    }

    @Test
    public void exitReferral() {
        ReferralViewModel[] list = referralsListNow();
        int priorCount = list.length;

        allocateReferralToOneService();

        ReferralTaskExitCommandViewModel vm = new ReferralTaskExitCommandViewModel(serviceRecipientId, null); // FIXME: Is there a taskHandle available
        // The close date gets applied in ReferralTaskCloseCommandHandler as toDateTimeAtStartOfDay()
        // which handles as per ReportController (see below comments) and therefore we need to be
        // supplying the LocalDate in London time, as per referralsListNow()
        vm.exitedDateChange = ChangeViewModel.create(null, new LocalDate(DateTimeZone.forID("Europe/London")));
        Result result = commandActor.executeCommand(vm).getBody();
        assertTrue(result.isCommandSuccessful());
        assertNotNull(result.getId());

        // NB this should be 0, but the use of referralsListNow() brings back the closed referral because
        // it uses /reports/referral (ReportController) which uses a LocalDate (with no reference to time)
        // adding 1 to the 'to' (in ReportController.applyReportCriteria) and converting to
        // toDateTimeAtStartOfDay(DateTimeZone.forID("Europe/London")).
        // We could use ReferralListFilter which uses time but this is older tech, and even the newer
        // ReferralListController uses the reporting day range - meaning newly closed cases are shown in
        // 'referrals list'.
        int dodgyCount = 1;

        extractFromResultMatchingReferralId(priorCount, dodgyCount);
    }

    static ServiceViewModel createViewModel(String serviceName, int serviceId, String projectName, Integer projectId) {
        ServiceViewModel allocateTo = ServiceViewModel.builder().id(serviceId).name(serviceName).build();

        if (projectId != null) {
            ProjectViewModel allocateProj = new ProjectViewModel();
            allocateProj.name = projectName;
            allocateProj.id = projectId;
            allocateTo.addProject(allocateProj);
        }
        return allocateTo;
    }

    private void allocate(ServiceViewModel... svm) {
        var allocationIds = CentralProcessingEvidenceCommandAPITests.allocateToIds(serviceActor.getAllServiceCategorisations().getBody(), svm);
        ReferralTaskAllocateServiceCommandViewModel tvm = new ReferralTaskAllocateServiceCommandViewModel(serviceRecipientId, allocationIds);
        Result result = commandActor.executeCommand(tvm).getBody();
        assertTrue(result.isCommandSuccessful());
        assertNotNull(result.getId());
    }

    private List<ReferralViewModel> extractFromResultMatchingReferralId(int priorCount, int additionalPredicted) {
        ReferralViewModel[] listAfter = referralsListNow();
        int afterCount = listAfter.length;
        assertThat(afterCount - priorCount, is(additionalPredicted));
        List<ReferralViewModel> filtered = Stream.of(listAfter)
                .filter(matchParentReferralId(referralId)).collect(toList());
        assertThat(filtered.size(), is(additionalPredicted));
        return filtered;
    }

    private void verifyFirstOnService(SessionDataViewModel sessionData, List<ReferralViewModel> filtered, String serviceName,
                                      String projectName, long serviceTypeId) {

        var svcCatId = serviceActor.getServiceCategorisationOfNames(sessionData, serviceName, projectName).id;
        List<ReferralViewModel> filteredServiceName = filtered.stream().filter(matchServiceAllocation(svcCatId)).toList();
        ReferralViewModel match = filteredServiceName.get(0);

        // verify service/project
        /*assertThat(match.referredServiceName, is(serviceName));
        if (projectName == null) {
            assertThat(match.currentProjectName, is((String) null));
        } else {
            assertThat(match.currentProjectName, is(projectName));
        }*/

        // verify tasks are marked complete
        int countOfTasksToReferralView = this.serviceTypesById.get(serviceTypeId).getTaskCountToReferralView();
        assertEquals(Integer.parseInt(match.currentTaskDefinitionIndex), countOfTasksToReferralView);
    }

    private ReferralViewModel[] referralsListNow() {
        String nowStr = getNowAsISOString(); // this is how the current referral list operates: 'now' as the from date
        ReportCriteriaDto dto = new ReportCriteriaDto();
        dto.setFrom(nowStr);
        dto.setTo(null);
        dto.setReferralStatus(ReferralStatusName.Ongoing.getName()); // the default for the referral list
        ResponseEntity<ReferralViewModel[]> responseReport = reportActor.getAllReportReferrals(dto);
        assertThat(responseReport.getStatusCode(), is(HttpStatus.OK));
        assertNotNull(responseReport.getBody());
        return responseReport.getBody();
    }

    private String getNowAsISOString() {
        // ReferralStatusCommonPredicates constructor says to use the users timezone
        // so we supply a users date which will work over midnight boundaries in BST
        DateTime now = DateTime.now(DateTimeZone.forID("Europe/London")).withTimeAtStartOfDay();
        String nowStr = ISODateTimeFormat.date().print(now);
        return nowStr;
    }

    private ReferralViewModel createReferral() {
        String firstName= "Dup";
        String lastName = "Licate";
        LocalDate referralDate = new LocalDate(2014, 02, 13);

        ReferralViewModel rvm = referralActor.createReferralAsStarted(firstName, lastName, referralDate, ServiceOptions.ACCOMMODATION);
        return rvm;
    }

    private ServiceViewModel getAndCheckServiceExists(String serviceName) {
        ServicesViewModel allServices = serviceActor.getAllServicesWithProjects().getBody();
        List<ServiceViewModel> filtered = allServices.services.stream()  // TODO: Can we do a matcher that matches on predicates
                .filter(matchServiceName(serviceName))
                .collect(toList());
        assertThat(filtered.size(), is(1));
        return filtered.get(0);
    }

}
