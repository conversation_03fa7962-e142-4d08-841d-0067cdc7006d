package com.ecco.acceptancetests.api.serviceConfig;

import com.ecco.acceptancetests.api.BaseJsonTest;
import com.ecco.test.support.UniqueDataService;
import com.ecco.serviceConfig.viewModel.ActionGroupViewModel;
import com.ecco.serviceConfig.viewModel.ActionViewModel;
import com.ecco.serviceConfig.viewModel.OutcomeViewModel;
import com.ecco.webApi.serviceConfig.OutcomeCloneCommandViewModel;
import org.junit.jupiter.api.Test;

import java.util.UUID;

import static java.util.stream.Collectors.toList;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

public class OutcomeCloneCommandAPITests extends BaseJsonTest {

    private static String[] outcomeNames = new String[] {
            UniqueDataService.instance.nameFor("outcome to add")};

    @Test
    public void canAdd() {
        canAdd(outcomeNames[0]);
    }

    private OutcomeViewModel canAdd(String name) {
        OutcomeViewModel outcomeViewModel = new OutcomeBuilder().withName(name).withActions("one", "two").build();
        OutcomeCloneCommandViewModel vm = new OutcomeCloneCommandViewModel(outcomeViewModel);
        commandActor.executeCommand(vm);

        OutcomeViewModel reloaded = getOutcome(vm.getOutcomeViewModel().uuid);
        assertNotNull(reloaded);
        assertEquals(2, reloaded.actionGroups.get(0).actions.size());
        return reloaded;
    }

    private OutcomeViewModel getOutcome(UUID uuid) {
        return outcomeActor.findNeedByUUID(uuid).getBody();
    }

    private class OutcomeBuilder {

        private final OutcomeViewModel outcomeViewModel;

        private OutcomeBuilder() {
            this.outcomeViewModel = new OutcomeViewModel();
            this.outcomeViewModel.uuid = UUID.randomUUID();
        }

        private OutcomeViewModel build() {
            return this.outcomeViewModel;
        }

        private OutcomeBuilder withName(String name) {
            this.outcomeViewModel.name = name;
            return this;
        }

        private OutcomeBuilder withActions(String... names) {
            ActionGroupViewModel actionGroupViewModel = new ActionGroupViewModel();
            actionGroupViewModel.uuid = UUID.randomUUID();
            this.outcomeViewModel.actionGroups = java.util.Collections.singletonList(actionGroupViewModel);
            actionGroupViewModel.name = "group1";
            actionGroupViewModel.actions = java.util.Arrays.stream(names).map(actionName ->
            {
                ActionViewModel actionViewModel = new ActionViewModel();
                actionViewModel.uuid = UUID.randomUUID();
                actionViewModel.name = actionName;
                return actionViewModel;
            })
                    .collect(toList());

            return this;
        }
    }

}
