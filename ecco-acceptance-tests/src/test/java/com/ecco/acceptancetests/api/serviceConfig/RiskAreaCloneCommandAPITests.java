package com.ecco.acceptancetests.api.serviceConfig;

import com.ecco.acceptancetests.api.BaseJsonTest;
import com.ecco.test.support.UniqueDataService;
import com.ecco.serviceConfig.viewModel.*;
import com.ecco.webApi.serviceConfig.RiskAreaCloneCommandViewModel;
import org.junit.jupiter.api.Test;

import java.util.UUID;

import static java.util.stream.Collectors.toList;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

public class RiskAreaCloneCommandAPITests extends BaseJsonTest {

    private static String[] riskAreaNames = new String[] {
            UniqueDataService.instance.nameFor("riskArea to add")};

    @Test
    public void canAdd() {
        canAdd(riskAreaNames[0]);
    }

    private RiskAreaViewModel canAdd(String name) {
        RiskAreaViewModel riskAreaViewModel = new RiskAreaBuilder().withName(name).withActions("one", "two").build();
        RiskAreaCloneCommandViewModel vm = new RiskAreaCloneCommandViewModel(riskAreaViewModel);
        commandActor.executeCommand(vm);

        RiskAreaViewModel reloaded = getRiskArea(vm.getRiskAreaViewModel().uuid);
        assertNotNull(reloaded);
        assertEquals(2, reloaded.actionGroups.get(0).actions.size());
        return reloaded;
    }

    private RiskAreaViewModel getRiskArea(UUID uuid) {
        return outcomeActor.findThreatByUUID(uuid).getBody();
    }

    private class RiskAreaBuilder {

        private final RiskAreaViewModel riskAreaViewModel;

        private RiskAreaBuilder() {
            this.riskAreaViewModel = new RiskAreaViewModel();
            this.riskAreaViewModel.uuid = UUID.randomUUID();
        }

        private RiskAreaViewModel build() {
            return this.riskAreaViewModel;
        }

        private RiskAreaBuilder withName(String name) {
            this.riskAreaViewModel.name = name;
            return this;
        }

        private RiskAreaBuilder withActions(String... names) {
            RiskActionGroupViewModel actionGroupViewModel = new RiskActionGroupViewModel();
            this.riskAreaViewModel.actionGroups = java.util.Collections.singletonList(actionGroupViewModel);
            riskAreaViewModel.uuid = UUID.randomUUID();
            actionGroupViewModel.name = "group1";
            actionGroupViewModel.uuid = UUID.randomUUID();
            actionGroupViewModel.actions = java.util.Arrays.stream(names).map(actionName ->
                {
                    RiskActionViewModel actionViewModel = new RiskActionViewModel();
                    actionViewModel.uuid = UUID.randomUUID();
                    actionViewModel.name = actionName;
                    return actionViewModel;
                })
                .collect(toList());

            return this;
        }
    }

}
