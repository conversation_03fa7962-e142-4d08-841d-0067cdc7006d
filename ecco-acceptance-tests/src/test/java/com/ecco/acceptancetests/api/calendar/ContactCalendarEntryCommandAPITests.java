package com.ecco.acceptancetests.api.calendar;

import com.ecco.acceptancetests.api.referral.ServiceRecipientCalendarEntryCommandAPITests;
import com.ecco.calendar.core.webapi.EventResource;
import com.ecco.dom.CustomEventAbstract;
import com.ecco.webApi.calendar.CalendarEntryCommandViewModel;
import com.ecco.webApi.calendar.ContactCalendarEntryCommandViewModel;
import com.ecco.webApi.contacts.IndividualUserSummaryViewModel;
import com.ecco.webApi.contacts.IndividualViewModel;
import com.ecco.webApi.listsConfig.ListDefinitionEntryViewModel;
import org.jspecify.annotations.Nullable;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;

import java.util.Collection;
import java.util.UUID;

import static org.junit.Assert.assertNotNull;

/**
 * @see ServiceRecipientCalendarEntryCommandAPITests
 */
@TestInstance(Lifecycle.PER_CLASS)
public class ContactCalendarEntryCommandAPITests extends BaseCalendarEntryCommandAPITestSupport<ContactCalendarEntryCommandViewModel> {

    @BeforeEach
    public void create() {
        // emulating @BeforeClass but we want our injected services
        if (ownerContactId == null) {
            IndividualUserSummaryViewModel user = userActor.createIndividualWithUser("contact-".concat(unique.userNameFor("calendar")), "ecco", "contact", unique.clientLastNameFor("contact"), null);
            IndividualViewModel ivm = contactActor.getContactById(user.individualId).getBody();

            ownerContactId = ivm.contactId;
            ownerFullName = ivm.firstName + " " + ivm.lastName;
            ownerCalendarId = ivm.calendarId;

            attendee = userActor.createIndividualWithUser("cont-".concat(unique.userNameFor("attendee")), "ecco", "contact", unique.clientLastNameFor("contact"), null);

            // ensure the entries exist (get 400 status if they do)
            // the requests assume the exact entry doesn't already exist, which it might, so we just wrap in try/catch
            String listName = CustomEventAbstract.EVENTCATEGORY_LISTNAME;
            ListDefinitionEntryViewModel vm = new ListDefinitionEntryViewModel();
            vm.listName = listName;
            vm.name = "test event category 1";
            ListDefinitionEntryViewModel vm2 = new ListDefinitionEntryViewModel();
            vm2.listName = listName;
            vm2.name = "test event category 2";

            //noinspection unused
            Collection<ListDefinitionEntryViewModel> list = listDefActor.ensureAndReturnListDefinitionEntry(listName, vm, vm2);

            // now retrieve them
            eventCategoryId1 = list.stream().findFirst().orElseThrow().id;
            eventCategoryId2 = list.stream().skip(1).findFirst().orElseThrow().id;
        }
    }

    @Override
    protected long getContactIdFromEvent(EventResource e) {
        return e.getContactId();
    }

    @Override
    protected void verifyCommand(UUID commandUuid) {
        var cmd = contactActor.findContactCalendarCommand(commandUuid.toString());
        assertNotNull(cmd);
    }

    @Override
    protected ContactCalendarEntryCommandViewModel createCommand(String operation, @Nullable String eventUuid) {
        CalendarEntryCommandViewModel vm = new CalendarEntryCommandViewModel(operation, eventUuid);
        ContactCalendarEntryCommandViewModel cvm = new ContactCalendarEntryCommandViewModel(ownerContactId.intValue(), vm);
        return cvm;
    }

    @Override
    protected ContactCalendarEntryCommandViewModel createCommand(String operation, int id, @Nullable String eventUuid) {
        CalendarEntryCommandViewModel vm = new CalendarEntryCommandViewModel(operation, eventUuid);
        ContactCalendarEntryCommandViewModel cvm = new ContactCalendarEntryCommandViewModel(id, vm);
        return cvm;
    }
}
