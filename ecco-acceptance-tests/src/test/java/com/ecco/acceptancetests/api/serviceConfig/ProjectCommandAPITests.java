package com.ecco.acceptancetests.api.serviceConfig;

import com.ecco.test.support.UniqueDataService;
import com.ecco.acceptancetests.api.BaseJsonTest;
import com.ecco.serviceConfig.viewModel.ProjectViewModel;
import com.ecco.webApi.evidence.BaseCommandViewModel;
import com.ecco.dto.ChangeViewModel;
import com.ecco.webApi.serviceConfig.ProjectCommandViewModel;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.web.client.HttpServerErrorException.InternalServerError;

import java.util.List;
import java.util.function.Predicate;
import java.util.stream.Stream;

import static java.util.stream.Collectors.toList;
import static org.junit.Assert.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

public class ProjectCommandAPITests extends BaseJsonTest {

    //protected final static LocalDateTime now = new LocalDateTime();
    //private final UniqueDataService unique = UniqueDataService.instance;
    private static String ADD = BaseCommandViewModel.OPERATION_ADD;
    private static String UPDATE = BaseCommandViewModel.OPERATION_UPDATE;
    private static String REMOVE = BaseCommandViewModel.OPERATION_REMOVE;

    private static Predicate<ProjectViewModel> matchProjectName(final String name) {
        return input -> name.equals(input.name);
    }

    private static String[] projectNames = new String[] {
            UniqueDataService.instance.nameFor("proj to add"),
            UniqueDataService.instance.nameFor("proj to remove"),
            UniqueDataService.instance.nameFor("proj to update"),
            UniqueDataService.instance.nameFor("proj updated")};

    @BeforeEach
    public void cleanBefore() {
        clean();
    }

    @AfterEach
    public void cleanAfter() {
        clean();
    }

    @Test
    public void canAdd() {
        canAdd(projectNames[0]);
    }

    // HTTP 400 - Bad Request (Valid command but failed to execute) thrown by spring-data when deleting something that doesn't exist
    @Test
    public void cannotRemoveWhatIsNotThere() {
        int unknownId = -5;
        ProjectCommandViewModel pvm = new ProjectCommandViewModel(REMOVE, unknownId);
        assertThrows(InternalServerError.class, () -> commandActor.executeCommand(pvm));
    }

    @Test
    public void canAddThenRemove() {
        ProjectViewModel pvm = canAdd(projectNames[1]);

        ProjectCommandViewModel cmd = new ProjectCommandViewModel(REMOVE, pvm.id);
        commandActor.executeCommand(cmd);

        List<ProjectViewModel> filtered = getProject(pvm.name);
        assertEquals(0, filtered.size());
    }

    @Test
    public void canAddThenUpdate() {
        ProjectViewModel pvm = canAdd(projectNames[2]);
        List<ProjectViewModel> filtered = getProject(pvm.name);
        assertEquals(1, filtered.size());
        assertEquals(pvm.id, filtered.get(0).id);

        String newName = projectNames[3];
        ProjectCommandViewModel cmd = new ProjectCommandViewModel(UPDATE, pvm.id);
        cmd.changeName = ChangeViewModel.create(pvm.name, newName);
        commandActor.executeCommand(cmd);

        filtered = getProject(newName);
        assertEquals(1, filtered.size());
        assertEquals(pvm.id, filtered.get(0).id);
        assertEquals(newName, filtered.get(0).name);
    }

    private ProjectViewModel canAdd(String name) {
        ProjectCommandViewModel pvm = new ProjectCommandViewModel(ADD, null);
        pvm.changeName = ChangeViewModel.create(null, name);
        commandActor.executeCommand(pvm);

        List<ProjectViewModel> filtered = getProject(name);
        assertEquals(1, filtered.size());
        return filtered.get(0);
    }

    private ProjectViewModel[] getAllProjects() {
        return projectActor.getAllProjects().getBody();
    }

    private List<ProjectViewModel> getProject(String name) {
        return Stream.of(getAllProjects())
                .filter(matchProjectName(name))
                .collect(toList());
    }

    private void clean() {
        for (String projectName : projectNames) {
            Stream.of(getAllProjects())
                    .filter(matchProjectName(projectName))
                    .forEach(projectViewModel -> {
                ProjectCommandViewModel cmd = new ProjectCommandViewModel(REMOVE, projectViewModel.id);
                commandActor.executeCommand(cmd);
            });
        }
    }

}
