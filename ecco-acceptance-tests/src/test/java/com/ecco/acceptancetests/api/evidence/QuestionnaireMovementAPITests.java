package com.ecco.acceptancetests.api.evidence;

import com.ecco.acceptancetests.api.questionnaire.QuestionnaireEvidenceCommandAPITests;
import com.ecco.data.client.ServiceOptions;
import com.ecco.dom.EvidenceGroup;
import com.ecco.dom.ReportCriteriaDto;
import com.ecco.evidence.EvidenceTask;
import com.ecco.serviceConfig.viewModel.QuestionGroupViewModel;
import com.ecco.webApi.evidence.QuestionAnswerCommandViewModel;
import com.ecco.webApi.evidence.QuestionnaireAnswersSnapshotViewModel;
import com.ecco.webApi.evidence.QuestionnaireEvidenceViewModel;
import org.hamcrest.Matcher;
import org.jspecify.annotations.Nullable;
import org.joda.time.Instant;
import org.junit.jupiter.api.BeforeAll;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import java.util.function.Function;

import static java.util.stream.Collectors.toList;
import static org.hamcrest.CoreMatchers.*;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.hasProperty;

/**
 * NB A baseline taken from QuestionnaireMovementAnswersAPITests.
 * Test for getting the values at points/snapshots in time.
 * NB We use the same referral for each test, which means one test can influence another.
 * To keep tests independent (where needed) we use different work items and different questions.
 */
public class QuestionnaireMovementAPITests extends BaseMovementAPITests<QuestionnaireEvidenceViewModel> {

    private static final String ANSWER_NOTTRUE = "0";
    private static final String ANSWER_SOMEWHATTRUE = "1";
    private static final String ANSWER_ALWAYSTRUE = "2";
    private static final String ANSWER_MISSING = "999";

    static private final String[] testIndexAnswers = new String[]{ANSWER_NOTTRUE, ANSWER_MISSING, ANSWER_ALWAYSTRUE, ANSWER_SOMEWHATTRUE};

    private static final EvidenceTask questionnaireTaskIapt = EvidenceTask.QUESTIONNAIRE_IAPTFEEDBACK;
    private static final EvidenceGroup questionnaireGroupIapt = EvidenceGroup.IAPTFEEDBACK;

    public QuestionnaireMovementAPITests() {
        super(questionnaireGroupIapt, questionnaireTaskIapt, ServiceOptions.ACCOMMODATION);
    }

    @BeforeAll
    public static void init() {
        ensureDefs = Boolean.FALSE;
        defIds = new int[23];
    }

    @Override
    protected void ensureDefinitionIds() {
        differentEvidenceGroup = EvidenceGroup.GENERALQUESTIONNAIRE;
        differentEvidenceTask = EvidenceTask.QUESTIONNAIRE_IAPTFEEDBACK;

        if (ensureDefs == null || Boolean.FALSE.equals(ensureDefs)) {
            ensureDefs = Boolean.TRUE;

            // questionGroupDefId
            List<QuestionGroupViewModel> questionGroups = questionGroupActor.findAllQuestionGroupsByServiceName(service.getServiceName()).getBody();
            List<QuestionGroupViewModel> filteredQuestionGroups = questionGroups.stream()
                    .filter(QuestionGroupViewModel.questionGroupMatchName("SDQ"))
                    .collect(toList());

            // questionDefId
            // questionId 1 is "I try to be nice to other people. I care about their feelings"
            // the answer choice we use is "always true" with value 2
            /*
                select qg.*, q.*,qac.displayValue, qac.value from questiongroups qg inner join questiongroups_questions qgq on qg.id=qgq.questiongroupId inner join questions q on q.id=qgq.questionId
                inner join questions_questionanswrchoices qqac on q.id=qqac.questionId
                inner join questionanswerchoices qac on qac.id=qqac.questionanswerchoiceId
                    where qg.name="SDQ";
             */
            defIds[0] = filteredQuestionGroups.get(0).questions.get(0).id;
            defIds[1] = filteredQuestionGroups.get(0).questions.get(1).id;
            defIds[2] = filteredQuestionGroups.get(0).questions.get(2).id;
            defIds[3] = filteredQuestionGroups.get(0).questions.get(3).id;
            defIds[4] = filteredQuestionGroups.get(0).questions.get(4).id;
            defIds[5] = filteredQuestionGroups.get(0).questions.get(5).id;
            defIds[6] = filteredQuestionGroups.get(0).questions.get(6).id;
            defIds[7] = filteredQuestionGroups.get(0).questions.get(7).id;
            defIds[8] = filteredQuestionGroups.get(0).questions.get(8).id;
            defIds[9] = filteredQuestionGroups.get(0).questions.get(9).id;
            defIds[10] = filteredQuestionGroups.get(0).questions.get(10).id;
            defIds[11] = filteredQuestionGroups.get(0).questions.get(11).id;
            defIds[12] = filteredQuestionGroups.get(0).questions.get(12).id;
            defIds[13] = filteredQuestionGroups.get(0).questions.get(13).id;
            defIds[14] = filteredQuestionGroups.get(0).questions.get(14).id;
            defIds[15] = filteredQuestionGroups.get(0).questions.get(15).id;
            defIds[16] = filteredQuestionGroups.get(0).questions.get(16).id;
            defIds[17] = filteredQuestionGroups.get(0).questions.get(17).id;
            defIds[18] = filteredQuestionGroups.get(0).questions.get(18).id;
            defIds[19] = filteredQuestionGroups.get(0).questions.get(19).id;
            defIds[20] = filteredQuestionGroups.get(0).questions.get(20).id;
            defIds[21] = filteredQuestionGroups.get(0).questions.get(21).id;
            defIds[22] = filteredQuestionGroups.get(0).questions.get(22).id;
        }
    }

    @Override
    protected List<QuestionnaireEvidenceViewModel> findWorkSummaryByServiceRecipientId() {
        return questionnaireEvidenceActor.findAllQuestionnaireWorkSummaryByServiceRecipientId(
                getServiceRecipientId(), this.evidenceGroup.nameAsLowercase(), false).getBody();
    }

    protected Matcher<ResultSnapshotViewModel> matcherForSomeProperties(long defId, int testIndex) {
        return allOf(
                hasProperty("defId", equalTo(defId)),
                hasProperty("resultProperty", equalTo(testIndexAnswers[testIndex])));
    }

    protected ResultsSnapshotViewModel[] runReportEarliestSnapshotInRange(ReportCriteriaDto reportCriteriaDto, int page) {
        ResponseEntity<QuestionnaireAnswersSnapshotViewModel[]> responseReport = reportActor.getReportQuestionnaireEarliestInRangeSnapshot(reportCriteriaDto, page);
        assertThat(responseReport.getStatusCode(), is(HttpStatus.OK));
        assertThat(responseReport.getBody(), is(notNullValue()));
        return ResultsSnapshotsAdapter.apply(responseReport.getBody());
    }

    protected ResultsSnapshotViewModel[] runReportLatestSnapshotBeforeRange(ReportCriteriaDto reportCriteriaDto, int page) {
        ResponseEntity<QuestionnaireAnswersSnapshotViewModel[]> responseReport = reportActor.getReportQuestionnaireLatestSnapshotBeforeRange(reportCriteriaDto, page);
        assertThat(responseReport.getStatusCode(), is(HttpStatus.OK));
        assertThat(responseReport.getBody(), is(notNullValue()));
        return ResultsSnapshotsAdapter.apply(responseReport.getBody());
    }

    protected ResultsSnapshotViewModel[] runReportLatestSnapshotInRange(ReportCriteriaDto reportCriteriaDto, int page) {
        ResponseEntity<QuestionnaireAnswersSnapshotViewModel[]> responseReport = reportActor.getReportQuestionnaireLatestInRangeSnapshot(reportCriteriaDto, page);
        assertThat(responseReport.getStatusCode(), is(HttpStatus.OK));
        assertThat(responseReport.getBody(), is(notNullValue()));
        return ResultsSnapshotsAdapter.apply(responseReport.getBody());
    }

    protected void createResult(UUID workUuid, @Nullable Instant timestampCreated, EvidenceGroup evidenceGroup, EvidenceTask defaultEvidenceTask, int defIdIndex, int testIndex) {
        QuestionAnswerCommandViewModel vm = QuestionnaireEvidenceCommandAPITests.createQuestionAnswerCommandViewModel(
                workUuid, timestampCreated, getServiceRecipientId(),
                evidenceGroup, defaultEvidenceTask, defIds[defIdIndex], testIndexAnswers[testIndex]);
        commandActor.executeCommand(vm);
    }

    private static final Function<QuestionnaireAnswersSnapshotViewModel, ResultsSnapshotViewModel> ResultsSnapshotAdapter = (snapshot) ->
        ResultsSnapshotViewModel.builder()
            .evidenceGroupKey(snapshot.evidenceGroupKey)
            .serviceRecipientId(snapshot.serviceRecipientId)
            .results(snapshot.answers.stream()
                    .map(a -> ResultSnapshotViewModel.builder()
                            .id(a.id)
                            .defId(a.questionId)
                            .workDate(a.workDate)
                            .resultProperty(a.answer)
                            .build()
                    ).collect(toList())
            )
            .build();

    private static final Function<QuestionnaireAnswersSnapshotViewModel[], ResultsSnapshotViewModel[]> ResultsSnapshotsAdapter = (snapshots) ->
            Arrays.stream(snapshots)
                    .map(ResultsSnapshotAdapter)
                    .toArray(ResultsSnapshotViewModel[]::new);
}
