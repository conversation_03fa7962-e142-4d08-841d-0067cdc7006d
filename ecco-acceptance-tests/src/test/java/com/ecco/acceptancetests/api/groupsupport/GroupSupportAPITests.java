package com.ecco.acceptancetests.api.groupsupport;

import com.ecco.acceptancetests.api.BaseJsonTest;
import com.ecco.data.client.ServiceOptions;
import com.ecco.dto.ChangeViewModel;
import com.ecco.webApi.evidence.ReferralViewModel;
import com.ecco.webApi.groupSupport.*;
import com.ecco.webApi.listsConfig.ListDefinitionEntryViewModel;
import org.joda.time.LocalDate;
import org.joda.time.LocalDateTime;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.Random;
import java.util.UUID;
import java.util.stream.Collectors;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.Assert.assertEquals;


public class GroupSupportAPITests extends BaseJsonTest {

    /** Single 'now' used as reference for multiple tests.. timestamps are never unique */
    protected final static LocalDateTime now = new LocalDateTime();

    static private Random random = new Random();
    static private ReferralViewModel rvm;
    static private ListDefinitionEntryViewModel venue;
    static private ListDefinitionEntryViewModel activityType;

    protected final ServiceOptions service;

    /** populated with a date up to 20 days in future at beginning of each test */
    protected LocalDateTime startDateUpTo20DaysInFuture;

    /** The UUID we provide when creating a new activity */
    private UUID activityUuid;


    public GroupSupportAPITests() {
        super();
        this.service = ServiceOptions.FLOATING_SUPPORT;
    }

    @BeforeEach
    public void createGroupSupportActivityRequirements() {
        activityUuid = UUID.randomUUID(); // should be diff uuid per test
        startDateUpTo20DaysInFuture = LocalDateTime.now().plusDays(random.nextInt(20));

        // emulating @BeforeClass but we want our injected services
        if (rvm == null) {
            LocalDate received = LocalDate.now();
            rvm = referralActor.createReferralAsStarted("archduke", "inglenook", received, service);
//            cvm = clientActor.getClientById(rvm.clientId).getBody();
        }

        // GIVEN: a venue exists as 'venue'
        if (venue == null) {
            venue = ensureListDef("venue", "George's place", "venue-george");
            assertThat(venue.businessKey).isNotBlank();
        }

        // GIVEN: a group support activity type exists as 'activityType'
        if (activityType == null) {
            activityType = ensureListDef("activityType", "Morris Dancing", "morris-dancing");
            assertThat(activityType.businessKey).isNotBlank();
        }
    }

    private ListDefinitionEntryViewModel ensureListDef(String listName, String name, String value) {
        var venues = listDefActor.getAllListDefs().getBody().get(listName);
        if (venues.size() == 0) {
            var ld = ListDefinitionEntryViewModel.builder()
                    .listName(listName)
                    .name(name)
                    .businessKey(value)
                    .build();
            venues = listDefActor.createListDefinitionEntry(ld, false).get(listName);
        }
        return venues.stream().findFirst().orElseThrow();
    }

    protected GroupActivityCommandViewModel createActivity(String description, UUID activityUuid,
                                                           LocalDateTime startDateTime, int durationMins, int capacity, Integer venueId) {

        return GroupActivityCommandViewModel.createSupport(activityUuid)
                    .activityTypeIdChange(null, activityType.id.intValue())
                    .capacityChange(null, capacity)
                    .dateChange(null, startDateTime)
                    .descriptionChange(null, "description")
                    .durationChange(null, durationMins)
                    .serviceIdChange(null, rvm.serviceIdAcl) // a bit hacky!
                    .venueIdChange(null, venueId);
    }

    protected GroupActivityInvitationCommandViewModel createInvitationViewModel(UUID activityUuid, long activityId, long referralId) {
        GroupActivityInvitationCommandViewModel vm = GroupActivityInvitationCommandViewModel.add(activityUuid, referralId);
        vm.activityId = activityId;
        vm.invited = ChangeViewModel.changeNullTo(true);
        return vm;
    }

    protected List<GroupSupportActivitySummaryRowResource> fetchActivities() {
        return groupSupportActor.findAll().getBody().getData();
    }

    protected List<ClientAttendanceViewModel> fetchEligibleAndInvitedClientsByActivityId(long activityId) {
        return groupSupportActor.findEligibleAndInvitedClientsByActivityId(activityId).getBody();
    }

    @Test
    public void canCreateActivityAndInviteAndFindInLists() {

        // GIVEN
        // a venue exists as venue and a referral

        // WHEN
        // I create an activity
        GroupActivityCommandViewModel eacvm = createActivity("parents are amazing", activityUuid,
                startDateUpTo20DaysInFuture, 45, 10, venue.id);
        commandActor.executeCommand(eacvm);
        // AND associate a referral (requires the activityId first)
        List<GroupSupportActivitySummaryRowResource> activities = fetchActivities();
        GroupSupportActivitySummaryRowResource activity = activities.stream()
                .peek(act -> log.info("pre-filter: id: {}, uuid: {}", act.id, act.uuid))
                .filter(a -> a.uuid.equals(activityUuid))
                .findFirst().get();

        // log clients on the activity
        List<ClientAttendanceViewModel> clients = fetchEligibleAndInvitedClientsByActivityId(activity.id);
        clients.forEach(cavm -> log.info("Eligible clients for new activity {}: srId={}", activity.id, cavm.serviceRecipientId));

        GroupActivityInvitationCommandViewModel eicvm = createInvitationViewModel(activityUuid, activity.id, rvm.referralId);
        log.info("Creating invitation for activityUuid={}, activityId={}, referralId={}, srId={}", activityUuid, activity.id,
                rvm.referralId, rvm.serviceRecipientId);
        commandActor.executeCommand(eicvm);

        // THEN
        // I see the client invited
        clients = fetchEligibleAndInvitedClientsByActivityId(activity.id);
        List<ClientAttendanceViewModel> clientsInvited = clients.stream().filter(cavm -> cavm.invited).collect(Collectors.toList());
        clientsInvited.forEach(cavm -> log.info("Invited clients on activity {}: srId={}", activity.id, cavm.serviceRecipientId));
        assertEquals(rvm.serviceRecipientId.intValue(), clientsInvited.get(0).serviceRecipientId);
    }

}