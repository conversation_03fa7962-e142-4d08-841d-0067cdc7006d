package com.ecco.acceptancetests.api.scenarios;

import com.ecco.servicerecipient.AcceptState;
import com.ecco.dom.Referral;
import com.ecco.test.support.UniqueDataService;
import com.ecco.acceptancetests.api.BaseJsonTest;
import com.ecco.data.client.ReferralOptions;
import com.ecco.data.client.ServiceOptions;
import com.ecco.data.client.steps.DefaultReferralStepsWebApiSupport;
import com.ecco.data.client.steps.ReferralData;
import com.ecco.data.client.steps.ReferralStepDefinitions;
import com.ecco.serviceConfig.viewModel.ProjectViewModel;
import com.ecco.dto.ServiceViewModel;
import com.ecco.infrastructure.time.Clock;
import com.ecco.serviceConfig.viewModel.ServiceTypeViewModel;
import com.ecco.webApi.contacts.AgencyViewModel;
import com.ecco.webApi.evidence.ReferralViewModel;
import com.ecco.webApi.featureConfig.SessionDataViewModel;
import com.ecco.webApi.listsConfig.FundingSourceViewModel;
import com.ecco.webApi.listsConfig.ListDefinitionEntryViewModel;
import com.google.common.collect.ImmutableMap;
import org.joda.time.DateTime;
import org.joda.time.Duration;
import org.joda.time.LocalDateTime;

import org.jspecify.annotations.Nullable;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.BiFunction;
import java.util.function.Function;

import static java.util.stream.Collectors.toList;


/**
 * A random data generator.
 * This serves a different role to csv data imports as the emphasis here is on the scenario
 * rather than the data - a scenario would be cumbersome in CSV and we don't care for the data.
 * Using the test-frameworks can also mean we have better overall testing.
 */
public class ReferralScenarioGenerator extends BaseJsonTest {

    private final UniqueDataService unique = UniqueDataService.instance;

    private Clock clock = Clock.DEFAULT;
    private DateTime now = clock.now();

    private final String dataProtectionSignature = "<?xml version=\"1.0\" encoding=\"UTF-8\""
            + " standalone=\"no\"?><!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\""
            + " \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">"
            + "<svg xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\" width=\"174\" height=\"38\">"
            + "<path fill=\"none\" stroke=\"#000000\" stroke-width=\"2\" stroke-linecap=\"round\" "
            + "stroke-linejoin=\"round\" d=\"M 1 30 c 0.32 0.05 11.82 2.37 18 3 c 17.32 1.76 33.57 3.08 51 4 c 8.52"
            + " 0.45 16.81 0.47 25 0 c 3.34 -0.19 6.86 -1.1 10 -2 c 1.38 -0.39 2.98 -1.09 4 -2 c 2.09 -1.88 5.2"
            + " -4.51 6 -7 c 1.56 -4.89 2.21 -12.71 2 -18 c -0.09 -2.23 -3.44 -6.67 -3 -7 c 0.46 -0.35 4.56 2.97 7"
            + " 4 c 3.82 1.61 7.87 3.02 12 4 c 8.64 2.05 17.21 3.02 26 5 c 4.83 1.09 14 4 14 4\"/></svg>";

    private final Map<String, BiFunction<ReferralData, String, ReferralData>> commands;

    private SessionDataViewModel sessionData;
    private AgencyViewModel[] agencies;
    private com.ecco.serviceConfig.viewModel.IdNameViewModel[] usersWithAccessTo;

    /**
     * Main class to support command line usage - possibly on servers to avoid latency
     */
    public static void main(String[] args) {

        // TODO accept the ScenarioOptions as JSON
        ReferralScenarioGenerator scenario = new ReferralScenarioGenerator();

        scenario.runScenarioForAllServices(new ScenarioOptions()
                .fromStartDateInPast(new LocalDateTime().minusMonths(3))
                .withMeanTaskPercent(75)
                .withVarianceTaskPercent(20)
                .withReferralCount(10)); // for each service

        scenario.logout();
    }

    public ReferralScenarioGenerator() {
        final ImmutableMap.Builder<String, BiFunction<ReferralData, String, ReferralData>> builder = ImmutableMap.builder();
        DefaultReferralStepsWebApiSupport stepDefinitions = new DefaultReferralStepsWebApiSupport(agreementActor,
                sessionDataActor, referralActor, clientActor, contactActor, calendarActor, userActor, listDefActor, serviceActor, cacheActor);
        stepDefinitions.registerStepDefinitions(builder);
        commands = builder.build();

        loginAs("ecco_import", "CONFIGURE ME in" + ReferralScenarioGenerator.class.getSimpleName().toString());

        ensureOrGetReferenceData();
    }

    private void runScenarioForAllServices(ScenarioOptions scenarioOptions) {

        // generate one random seed, so the result of all referrals will be as the scenario dictates
        Random random = new Random();

        // for each service
        /*for (svcCat : sessionData.getRestrictedServiceCategorisations()) {

            // let do positive services only
            if (service.getId() < 0) {
                continue;
            }

            // TODO potentially log in as someone else here

            // apply referrals appropriate to the service
            ServiceOptions serviceOptions = new ServiceOptions(service);
            for (int i = 0; i < scenarioOptions.getReferralCount(); i++) {
                ReferralOptions referralOptions = uniqueReferralOptions(i, scenarioOptions, serviceOptions, random);
                processReferral(serviceOptions, referralOptions);
            }
        }*/
    }

    /**
     * Populate the random data for the referral
     */
    private ReferralOptions uniqueReferralOptions(int referralCount, ScenarioOptions scenarioOptions, ServiceOptions serviceOptions, Random random) {
        ReferralOptions referralOptions = new ReferralOptions();

        long serviceTypeId = serviceOptions.getServiceViewModel().serviceTypeId;
        ServiceTypeViewModel serviceType = serviceTypeActor.getServiceTypeById(serviceTypeId).getBody();
        List<String> allStepsOnConfig = serviceType.taskDefinitionEntries.stream().map(tde -> tde.name).collect(toList());
        int uniqueStepsToComplete = getNumberOfStepsToComplete(random, scenarioOptions, allStepsOnConfig);
        // prevent further checks, just process the referral and nothing else
        // although this isn't really valid - we do need the 'details of referral' date (see v2 'new referral')
        if (uniqueStepsToComplete == 0) {
            return referralOptions;
        }

        int maxDaysForEachTask = getDaysForEachTask(scenarioOptions.getStartDate().toDateTime(), this.now.withTimeAtStartOfDay(), uniqueStepsToComplete);
        int daysCanDelayStart = (allStepsOnConfig.size() - uniqueStepsToComplete) * maxDaysForEachTask;
        DateTime baseDate = scenarioOptions.getStartDate().toDateTime().plusDays(new Random().nextInt(daysCanDelayStart));
        int daysForEachTask = getDaysForEachTask(baseDate, this.now.withTimeAtStartOfDay(), uniqueStepsToComplete);

        uniqueReferralOptionsSteps(allStepsOnConfig, uniqueStepsToComplete, referralOptions);
        uniqueReferralOptionsData(referralCount, scenarioOptions, serviceOptions.getServiceViewModel(), referralOptions, baseDate, daysForEachTask);

        return referralOptions;
    }

    /**
     * Determine the steps for this referral
     */
    private void uniqueReferralOptionsSteps(List<String> allStepsOnConfig, int stepsToComplete, ReferralOptions referralOptions) {

        referralOptions.stopStepsAtTaskName = allStepsOnConfig.get(stepsToComplete-1);
        referralOptions.stepTaskOrder = allStepsOnConfig.subList(0, stepsToComplete-1);

        for (int i = 0; i < stepsToComplete-1; i++) {
            String stepTask = allStepsOnConfig.get(i);
            switch (stepTask) {
                case ReferralStepDefinitions.DESTINATION:
                    referralOptions.requiresProjects(true);
                    break;
                case ReferralStepDefinitions.SOURCE_OF_REFERRAL:
                    referralOptions.requiresSource(true);
                    break;
                case ReferralStepDefinitions.REFERRAL_DETAILS:
                    referralOptions.requiresDetails(true);
                    break;
                case ReferralStepDefinitions.WAITING_LIST_CRITERIA:
                    referralOptions.requiresWaitingListScore(true);
                    break;
                case ReferralStepDefinitions.DATA_PROTECTION:
                    referralOptions.requiresDataProtection(true);
                    break;
                case ReferralStepDefinitions.EMERGENCY_DETAILS:
                    referralOptions.requiresEmergencyDetails(true);
                    break;
                // NOT implemented yet, see DefaultReferralStepsWebApiSupport
                //case ReferralStepDefinitions.ACCOMMODATION:
                //    referralOptions.requiresAccommodation(true);
                //    break;
                case ReferralStepDefinitions.DELIVERED_BY:
                    referralOptions.requiresDeliveredBy(true);
                    break;
                case ReferralStepDefinitions.FUNDING:
                    referralOptions.requiresFunding(true);
                    break;
                case ReferralStepDefinitions.SETUP_INITIAL_ASSESSMENT:
                    referralOptions.requiresInitialAssessment(true);
                    break;
                case ReferralStepDefinitions.ACCEPT_ON_SERVICE:
                    referralOptions.requiresAcceptOnService(true);
                    break;
                case ReferralStepDefinitions.START:
                    referralOptions.requiresStart(true);
                    break;
                case ReferralStepDefinitions.START_ACCOMMODATION:
                    referralOptions.requiresStartAccommodation(true);
                    break;
                case ReferralStepDefinitions.NEEDS_ASSESSMENT:
                    referralOptions.requiresNeedsAssessment(true);
                    break;
                case ReferralStepDefinitions.EXIT:
                    referralOptions.requiresExited(true);
                    break;
            }
        }
    }

    /**
     * Determine the data for this referral
     */
    private void uniqueReferralOptionsData(int referralCount, ScenarioOptions scenarioOptions, ServiceViewModel service,
                                              ReferralOptions referralOptions, DateTime baseDate, int daysForEachTask) {

        Function<Integer, DateTime> nextStepDate = i -> baseDate.plusDays(daysForEachTask * i);
        Function<Integer, Boolean> countIsAtPercent = percent -> referralCount % Math.floor(100 / percent) == 0;
        int dateStepCount = 0;

        referralOptions.withFirstName(unique.clientFirstNameFor("Scenario"))
                .withLastName(unique.clientLastNameFor("Index" + scenarioOptions.getReferralCount()));

        ProjectViewModel project = findRandomProject(service.projects);
        if (project != null) {
            referralOptions.withProjectName = project.name;
            ensureOrGetUsersForService(service.getId(), (long) project.getId());
        } else {
            ensureOrGetUsersForService(service.getId(), null);
        }

        referralOptions.withEmergencyMedicationDetails = unique.appendId("my medication details");

        if (referralOptions.requiresSource()) {
            if (countIsAtPercent.apply(scenarioOptions.getStepSourcePercentSplit())) {
                referralOptions.withSourceSelf = true;
            } else {
                referralOptions.withSourceAgency = findAnyAgency();
            }
        }

        if (referralOptions.requiresDetails()) {
            referralOptions.withDetailsReceivedDate = nextStepDate.apply(dateStepCount++).toLocalDate();
        }

        if (referralOptions.requiresWaitingListScore()) {
            referralOptions.withWaitingListScore = 10;
        }

        if (referralOptions.requiresDataProtection()) {
            referralOptions.withDataProtectionDate = nextStepDate.apply(dateStepCount++);
            if (countIsAtPercent.apply(scenarioOptions.getStepSourcePercentSplit())) {
                referralOptions.withDataProtectionSignature = this.dataProtectionSignature;
            }
        }

        if (referralOptions.requiresFunding()) {
            referralOptions.withFundingDecisionDate = nextStepDate.apply(dateStepCount++);
            referralOptions.withFundingSourceId = findAnyFundingSource();
            referralOptions.withFundingAmount = new BigDecimal(new Random().nextInt(100));
        }

        if (referralOptions.requiresInitialAssessment()) {
            referralOptions.withInitialAssessmentFirstOfferedInterviewDate = nextStepDate.apply(dateStepCount++);
            referralOptions.withInitialAssessmentDecisionDate = nextStepDate.apply(dateStepCount++);
            referralOptions.withInitialAssessmentInterviewer1Id = findAnyStaff();
            referralOptions.withInitialAssessmentInterviewer12d = findAnyStaff();
        }

        if (referralOptions.requiresAcceptOnService()) {
            referralOptions.withAcceptOnServiceDate = nextStepDate.apply(dateStepCount++).toLocalDate(); // NB should be users local date
            if (countIsAtPercent.apply(scenarioOptions.getStepAcceptOnServicePercentSplit())) {
                referralOptions.withAcceptOnServiceState = AcceptState.ACCEPTED;
            } else {
                referralOptions.withAcceptOnServiceState = AcceptState.SIGNPOSTED;
                if (referralCount % 2 == 0) { // 1/2 is sent back
                    referralOptions.withAcceptOnServiceSignpostBack = true;
                } else {
                    referralOptions.withAcceptOnServiceSignpostAgencyId = findAnyAgency().contactId;
                }
                referralOptions.withAcceptOnServiceSignpostComment = unique.appendId("signposted comment");
                referralOptions.withAcceptOnServiceSignpostReasonId = findAnySignpostReasonId();
            }
        }

        if (referralOptions.requiresStart()) {
            referralOptions.withStartStartedState = referralCount % 5 != 0; // 1/5th is NOT started
            referralOptions.withStartReceivingServiceDate = nextStepDate.apply(dateStepCount++).toLocalDate();
            referralOptions.withStartAllocatedWorkerContactId = findAnyStaff();
        }

        if (referralOptions.requiresNeedsAssessment()) {
            referralOptions.withNeedsAssessmentComment = unique.appendId("this is a needs assessment entry");
            referralOptions.withNeedsAssessmentWorkDate = nextStepDate.apply(dateStepCount++);
        }

        if (referralOptions.requiresExited()) {
            referralOptions.withExitedDate = nextStepDate.apply(dateStepCount++).toLocalDate();
        }
    }

    /**
     * Get the number of steps to complete for this referral. This is determined by random, according to normal
     * distribution.
     * nextGaussian produces about 70% of its values around its mean (0) and standard deviation (1), which results
     * in most values between -1 and 1. Therefore we shift (add to) the result so that the focus is on the desired
     * task number, and we scale the result for the number of tasks to fall within the first deviation around 70%.
     * see http://www.javamex.com/tutorials/random_numbers/gaussian_distribution_2.shtml
     * see http://stackoverflow.com/questions/31754209/can-random-nextgaussian-sample-values-from-a-distribution-with-different-mean
     * and a reminder on standard deviation! http://www.mathsisfun.com/data/standard-deviation.html
     */
    private int getNumberOfStepsToComplete(Random random, ScenarioOptions scenarioOptions, List<String> allStepsOnConfig) {
        double firstDeviationNumberOfTasks = scenarioOptions.getVarianceTaskPercent() / 100;
        double meanStepNumberShift = (allStepsOnConfig.size() * scenarioOptions.getMeanTaskPercent()) / 100;
        int stepsToComplete = (int) Math.round((random.nextGaussian() * firstDeviationNumberOfTasks) + meanStepNumberShift);
        // guassian can produce values outsite our normal range - so just limit between 0 and size
        return Math.min(Math.max(stepsToComplete, 0), allStepsOnConfig.size());
    }

    private int getDaysForEachTask(DateTime from, DateTime to, int stepsToComplete) {
        long daysBetween = new Duration(from, to).getStandardDays();
        return (int) Math.floor(daysBetween / stepsToComplete);
    }

    private ProjectViewModel findRandomProject(List<ProjectViewModel> projects) {
        if (projects != null && !projects.isEmpty()) {
            int randomProject = new Random().nextInt(projects.size());
            return projects.get(randomProject);
        }
        return null;
    }

    private void ensureOrGetReferenceData() {
        sessionData = sessionDataActor.getSessionData().getBody();
        ensureOrGetAgencies();
        ensureOrGetFundingSources();
    }

    private void ensureOrGetAgencies() {
        agencies = contactActor.getAllAgencies().getBody();
        if (agencies.length == 0) {
            AgencyViewModel avm = new AgencyViewModel();
            avm.companyName = unique.appendId("acme");
            contactActor.createAgency(avm);
            agencies = contactActor.getAllAgencies().getBody();
        }
    }

    private void ensureOrGetFundingSources() {
        if (!sessionData.fundingSources.iterator().hasNext()) {
            FundingSourceViewModel fvm = new FundingSourceViewModel();
            fvm.name = unique.appendId("Funding Source");
            sessionDataActor.createFundingSource(fvm);
            sessionData = sessionDataActor.getSessionData().getBody();
            ensureOrGetFundingSources();
        }
    }

    private void ensureOrGetUsersForService(int serviceId, @Nullable Long projectId) {
        usersWithAccessTo = userActor.usersWithAccessTo((long) serviceId, projectId).getBody();
        if (usersWithAccessTo.length == 0) {
            throw new IllegalArgumentException("need to implement userActor and create a user");
        }
    }

    private AgencyViewModel findAnyAgency() {
        return this.agencies[new Random().nextInt(this.agencies.length)];
    }

    private Integer findAnyFundingSource() {
        List<FundingSourceViewModel> lists = new ArrayList<>();
        this.sessionData.fundingSources.spliterator().forEachRemaining(lists::add);
        return lists.get(new Random().nextInt(lists.size())).getId();
    }

    private int findAnySignpostReasonId() {
        List<ListDefinitionEntryViewModel> lists = new ArrayList<>();
        this.sessionData.listDefinitions.get(Referral.SIGNPOSTREASON_LISTNAME).spliterator().forEachRemaining(lists::add);
        return lists.get(new Random().nextInt(lists.size())).getId();
    }

    private long findAnyStaff() {
        return this.usersWithAccessTo[new Random().nextInt(this.usersWithAccessTo.length)].getId();
    }

    /**
     * Process the referral with the service and referral options generated
     */
    private void processReferral(ServiceOptions serviceOptions, ReferralOptions referralOptions) {

        ReferralViewModel rvm = referralActor.createMinimalReferralAndClient(referralOptions.withClientCode(),
                referralOptions.withFirstName() , referralOptions.withLastName(),
                serviceOptions, referralOptions.withProjectName);
        ReferralData referralData = new ReferralData(rvm, serviceOptions, referralOptions, workflowActor.getTasksByTaskName(rvm));

        for (String stepTaskName : referralData.referralOptions.stepTaskOrder) {

            // NB:
            // unknown tasks to DefaultReferralStepsWebApiSupport will just return the incoming value
            // DESTINATION isn't needed as its passed in to minimalReferral
            // ACCOMMODATION isn't implemented
            // CASE_NOTES isn't implemented

            // check we aren't stopped
            if (stepTaskName.equals(referralData.referralOptions.stopStepsAtTaskName)) {
                return;
            }

            // do the work
            if (commands.get(stepTaskName) != null) {
                referralData = commands.get(stepTaskName).apply(referralData, referralData.taskHandleFor(stepTaskName));
            }

            // TODO move the tasks along
        }

    }

}
