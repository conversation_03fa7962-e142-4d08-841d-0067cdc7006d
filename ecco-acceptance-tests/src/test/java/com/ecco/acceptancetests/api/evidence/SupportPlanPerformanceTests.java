package com.ecco.acceptancetests.api.evidence;

import com.ecco.acceptancetests.api.BaseJsonTest;
import com.ecco.data.client.ServiceOptions;
import com.ecco.serviceConfig.viewModel.OutcomeViewModel;
import com.ecco.webApi.contacts.ClientViewModel;
import com.ecco.webApi.evidence.ReferralViewModel;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import java.util.List;

/**
 * Tests performance. Run this test in an environment with a high latency database connection and see what happens.
 * For Mac OS X Network Link Conditioner, I use the following preset:
 * DNS Delay: 100ms, Downlink/Uplink 10Mbps, 2% packet drops, 20ms latency.
 *
 * @since 18/09/2014
 */
@Disabled("Needs updating to build work using Commands, not old stuff")
public class SupportPlanPerformanceTests extends BaseJsonTest {

    protected final long sysadminContactId = 1L;

    @Test
    public void findByReferralIdDoesntTakeTooLong() {
        // get first outcome from the service
        List<OutcomeViewModel> outcomes = outcomeActor.findAllByServiceName(ServiceOptions.ACCOMMODATION.getServiceName()).getBody();
        @SuppressWarnings("unused")
        OutcomeViewModel ovm = outcomes.get(0);

        // create a referral
        LocalDate dte = new LocalDate(2014, 5, 6);
        final ReferralViewModel rvm = referralActor.createReferralAsStarted("Bluetooth", "Keyboard", dte, ServiceOptions.ACCOMMODATION);
        @SuppressWarnings("unused")
        ClientViewModel cvm = clientActor.getClientById(rvm.clientId).getBody();

/*        // create needs assessment
        RecordEvidenceViewModel vm = buildAssessmentWork(rvm.referralId, ovm, cvm.contactId, sysadminContactId);
        ResponseEntity<Result> response = supportEvidenceActor.createWork(vm);
        assertEquals(HttpStatus.CREATED, response.getStatusCode());

        // Now update target dates
        vm = buildSetTargetDateWork(rvm.referralId, ovm, cvm.contactId, sysadminContactId);
        response = supportEvidenceActor.createWork(vm);
        assertEquals(HttpStatus.CREATED, response.getStatusCode());

        // This is the bit to measure
        final Callable<List<EvidenceViewModel>> task = new Callable<List<EvidenceViewModel>>() {
            @Override
            public List<EvidenceViewModel> call() {
                return supportEvidenceActor.findByReferralId(rvm.referralId).getBody();
            }
        };
        ExecutorService executor = Executors.newFixedThreadPool(1);
        log.info("Here we go...");
        // load all the history
        long startTime = System.currentTimeMillis();
        final Future<List<EvidenceViewModel>> future = executor.submit(task);
        try {
            List<EvidenceViewModel> svmList = future.get(500, TimeUnit.MILLISECONDS);
        } finally {
            System.out.println("execution time = " + (System.currentTimeMillis() - startTime) + "ms");
            future.cancel(true);
        }

 */
        // If we got here, we didn't time out!
    }
}
