package com.ecco.acceptancetests.api.calendar;

import com.ecco.acceptancetests.api.BaseJsonTest;
import com.ecco.acceptancetests.api.referral.ServiceRecipientCalendarEntryCommandAPITests;
import com.ecco.data.client.actors.CalendarActor;
import com.ecco.calendar.dom.EventType;
import com.ecco.dom.agreements.DaysOfWeek;
import com.ecco.test.support.UniqueDataService;
import com.ecco.webApi.calendar.CalendarAttendeeCommandViewModel;
import com.ecco.webApi.calendar.CalendarEntryCommandViewModelParent;
import com.ecco.webApi.contacts.IndividualUserSummaryViewModel;
import com.ecco.webApi.contacts.IndividualViewModel;
import com.ecco.webApi.evidence.BaseCommandViewModel;
import com.ecco.dto.ChangeViewModel;
import com.ecco.webApi.viewModels.Result;
import com.ecco.calendar.core.webapi.EventAttendee;
import com.ecco.calendar.core.webapi.EventResource;
import org.hamcrest.CoreMatchers;
import org.joda.time.*;
import org.jspecify.annotations.Nullable;
import org.junit.jupiter.api.Test;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StringUtils;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

import static java.lang.Long.parseLong;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.*;
import static org.junit.Assert.*;

/**
 * @see ServiceRecipientCalendarEntryCommandAPITests
 */
@SuppressWarnings({"ResultOfMethodCallIgnored", "DataFlowIssue"})
public abstract class BaseCalendarEntryCommandAPITestSupport<T extends CalendarEntryCommandViewModelParent> extends BaseJsonTest {

    protected final UniqueDataService unique = UniqueDataService.instance;

    protected Integer eventCategoryId1, eventCategoryId2;
    protected String ownerFullName;
    protected Long ownerContactId;
    protected String ownerCalendarId;
    protected IndividualUserSummaryViewModel attendee;

    /**
     * HAPPY PATHS
     */

    @Test
    public void canCreateAppointment_allDayHappyPath() {
        T vm = createCommand(BaseCommandViewModel.OPERATION_ADD, null);

        vm.getCalendarEntryViewModel().setStartDate(ChangeViewModel.changeNullTo(new LocalDate()));
        vm.getCalendarEntryViewModel().setAllDay(ChangeViewModel.changeNullTo(true));
        Result result = commandActor.executeCommand(vm.getCalendarEntryViewModelParent()).getBody();
        assertTrue(result.isCommandSuccessful());
        assertNotNull(result.getId());

        EventResource e = findEventResourceAndVerifyBasics(result);
        assertEquals(vm.getCalendarEntryViewModel().getAllDay().to, e.isAllDay());
        LocalDateTime dt = vm.getCalendarEntryViewModel().getStartDate().to.toLocalDateTime(LocalTime.MIDNIGHT);
        assertEquals(dt, e.getStart());
        this.assertDefaults(true, true, dt, e, vm.getCommandUuid());

        // test event in range contains the entry once, when there is another contact with nothing in the calendar
        IndividualUserSummaryViewModel emptyCal = ensureEmptyIndividualWithUser();
        var contactId = getContactIdFromEvent(e);
        var entries = calendarActor.getEntriesByTime(e.getStart().toLocalDate(), e.getStart().toLocalDate(), new Long[]{contactId, emptyCal.individualId}, null).getBody();
        assert entries != null;
        var count = Arrays.stream(entries).filter(er -> er.getEntryId().equals(e.getEntryId())).count();
        assertEquals(1, count);
    }

    protected abstract long getContactIdFromEvent(EventResource e);

    @Test
    public void canCreateAppointment_notAllDayHappyPath() {
        T vm = createCommand(BaseCommandViewModel.OPERATION_ADD, null);
        LocalDate startDate = new LocalDate();
        LocalTime startTime = new LocalTime();
        vm.getCalendarEntryViewModel().setStartDate(ChangeViewModel.changeNullTo(startDate));
        vm.getCalendarEntryViewModel().setAllDay(ChangeViewModel.changeNullTo(false));
        vm.getCalendarEntryViewModel().setStartTime(ChangeViewModel.changeNullTo(startTime));
        // allDay false. needs end time
        LocalDateTime endLocal = startDate.toLocalDateTime(startTime).plusMinutes(45);
        vm.getCalendarEntryViewModel().setEndDate(ChangeViewModel.changeNullTo(endLocal.toLocalDate()));
        vm.getCalendarEntryViewModel().setEndTime(ChangeViewModel.changeNullTo(endLocal.toLocalTime()));
        Result result = commandActor.executeCommand(vm.getCalendarEntryViewModelParent()).getBody();
        assertTrue(result.isCommandSuccessful());
        assertNotNull(result.getId());

        EventResource e = findEventResourceAndVerifyBasics(result);
        assertEquals(vm.getCalendarEntryViewModel().getAllDay().to, e.isAllDay());
        LocalDateTime dt = vm.getCalendarEntryViewModel().getStartDate().to.toLocalDateTime(vm.getCalendarEntryViewModel().getStartTime().to);
        // server side clobbers the seconds
        dt = dt.withSecondOfMinute(0).withMillisOfSecond(0);
        assertEquals(dt, e.getStart());
        this.assertDefaults(false, true, dt, e, vm.getCommandUuid());
    }

    // This 'attendees1' test is because there are two ways of generating attendees - contactIds and CalendarAttendeeViewModels
    // This approach uses contactIds, 'attendees2' below uses CalendarAttendeeViewModels.
    // NB the attendee is assumed to be the the current user if none is supplied
    @Test
    public void canCreateAppointment_attendees1HappyPath() {
        // create the command for the owner
        T vm = createCommand(BaseCommandViewModel.OPERATION_ADD, null);
        Result created;
        { // GIVEN we add an attendee
            vm.getCalendarEntryViewModel().setContactIds(ChangeViewModel.changeNullTo(Collections.singletonList(attendee.individualId)));
            created = createDefaultCalendarEntry(true, vm);
        }

        { // THEN
            EventResource e = findEventResourceAndVerifyBasics(created);
            List<EventAttendee> eventAttendees = e.getAttendees();
            assertEquals(2, eventAttendees.size());
            List<String> names = eventAttendees.stream().map(eventAttendee -> eventAttendee.name).collect(Collectors.toList());
            assertThat("Expected attendees", names, containsInAnyOrder(attendee.firstName + " " + attendee.lastName,
                    ownerFullName));
            this.assertDefaults(true, false, e.getStart(), e, vm.getCommandUuid());
        }
    }

    // NB this operates attendees using EventAttendee
    @Test
    public void canCreateAppointment_attendees2HappyPath() {
        T vm = createCommand(BaseCommandViewModel.OPERATION_ADD, null);
        Result created;
        { // GIVEN
            CalendarAttendeeCommandViewModel avm = new CalendarAttendeeCommandViewModel(BaseCommandViewModel.OPERATION_ADD, attendee.calendarId);
            vm.getCalendarEntryViewModel().setAttendees(new CalendarAttendeeCommandViewModel[]{avm});
            //vm.getCalendarEntryViewModel().setContactIds(ChangeViewModel.changeNullTo(Collections.singletonList(attendee.individualId));
            created = createDefaultCalendarEntry(true, vm);
        }

        { // THEN
            EventResource e = findEventResourceAndVerifyBasics(created);
            List<EventAttendee> eventAttendees = e.getAttendees();
            assertEquals(2, eventAttendees.size());
            List<String> names = eventAttendees.stream().map(eventAttendee -> eventAttendee.name).collect(Collectors.toList());
            assertThat("Expected attendees", names, containsInAnyOrder(attendee.firstName + " " + attendee.lastName,
                    ownerFullName));
            this.assertDefaults(true, false, e.getStart(), e, vm.getCommandUuid());
        }
    }

    @Test
    public void canCreateAppointment_repeatHappyPath() {
        T vm = createCommand(BaseCommandViewModel.OPERATION_ADD, null);
        // Joda-Time uses the ISO standard Monday to Sunday week, so set to Friday for our repeatEveryDays
        LocalDate start = new LocalDate().withDayOfWeek(5);
        LocalDate repeatEndDate = start.plusMonths(6);
        DaysOfWeek dow = new DaysOfWeek();
        dow.setSunday(true);
        dow.setWednesday(true);

        var entryVM = vm.getCalendarEntryViewModel().toBuilder()
            .startDate(ChangeViewModel.changeNullTo(start))
        // need to set allDay since we don't set a time
            .allDay(ChangeViewModel.changeNullTo(true))
            .repeatEveryDays(ChangeViewModel.changeNullTo(dow.daysAttending()))
            .repeatEveryWeeks(ChangeViewModel.changeNullTo(2)) // trigger recurring
            .repeatEndDate(ChangeViewModel.changeNullTo(repeatEndDate))
            .build();
        vm.setCalendarEntryViewModel(entryVM);
        Result result = commandActor.executeCommand(vm.getCalendarEntryViewModelParent()).getBody();
        assertTrue(result.isCommandSuccessful());
        assertNotNull(result.getId());

        EventResource e = findEventResourceAndVerifyBasics(result);
        // allDay is not something that is transferred on RecurringEntryDefinition
        assertEquals(entryVM.getAllDay().to, e.isAllDay());
        LocalDateTime dt = entryVM.getStartDate().to.toLocalDateTime(LocalTime.MIDNIGHT);

        // test start date of repeating entry - its Friday+2 = Sunday
        assertEquals(dt.plusDays(2), e.getStart());
        this.assertDefaults(true, true, e.getStart(), e, vm.getCommandUuid());

        // test future entries
        // 3 weeks from Friday every 2 weeks means:
        //      wk0 Friday - the start day is NOT included
        //      wk1 Sunday - because 'every 2 weeks' begins the week of the start day
        //      wk1 - miss week - because its 'every 2 weeks'
        //      wk2 Wednesday - because Wed was specified
        //      wk2 Sunday - because Sunday was specified
        //      wk3 - miss week - because its 'every 2 weeks' (3 weeks Friday is here)
        ResponseEntity<EventResource[]> eventsInRange = calendarActor.getEntriesByTime(dt.toLocalDate(), dt.plusWeeks(3).toLocalDate(), new Long[] {ownerContactId}, Collections.emptyList());
        var matchingEvents = Arrays.stream(eventsInRange.getBody()).filter(ev -> ev.getEntryId().startsWith(e.getEntryId())).count();
        assertEquals(3, matchingEvents);

        // test future entries stop
        // after 6 months
        ResponseEntity<EventResource[]> eventsOutsideRange = calendarActor.getEntriesByTime(dt.plusMonths(7).toLocalDate(), dt.plusMonths(8).toLocalDate(), new Long[] {ownerContactId}, Collections.emptyList());
        var matchingEventsOutside = Arrays.stream(eventsOutsideRange.getBody()).filter(ev -> ev.getEntryId().startsWith(e.getEntryId())).count();
        assertEquals(0, matchingEventsOutside);
    }

    @Test
    public void canCreateAppointment_repeatNoDaysHappyPath() {
        T vm = createCommand(BaseCommandViewModel.OPERATION_ADD, null);
        // Joda-Time uses the ISO standard Monday to Sunday week, so set to Sunday so testing the counts later don't change
        LocalDate start = new LocalDate().withDayOfWeek(7);
        vm.getCalendarEntryViewModel().setStartDate(ChangeViewModel.changeNullTo(start));
        LocalTime startTime = new LocalTime(11,0);
        vm.getCalendarEntryViewModel().setStartTime(ChangeViewModel.changeNullTo(startTime));
        // need to set allDay since we don't set a time
        vm.getCalendarEntryViewModel().setAllDay(ChangeViewModel.changeNullTo(false));
        vm.getCalendarEntryViewModel().setEndTime(ChangeViewModel.changeNullTo(vm.getCalendarEntryViewModel().getStartTime().to.plusMinutes(35)));

        /* NO day of the week specified means the start date is assumed (because that's all it can do)
        DaysOfWeek dow = new DaysOfWeek();
        dow.setSunday(true);
        dow.setWednesday(true);
        vm.getCalendarEntryViewModel().setRepeatEveryDays(ChangeViewModel.changeNullTo(dow.daysAttending()));
        */

        vm.getCalendarEntryViewModel().setRepeatEveryWeeks(ChangeViewModel.changeNullTo(1)); // trigger recurring
        Result result = commandActor.executeCommand(vm.getCalendarEntryViewModelParent()).getBody();
        assertTrue(result.isCommandSuccessful());
        assertNotNull(result.getId());

        // get the recurring entry (the master) - not an individual recurrence
        EventResource e = findEventResourceAndVerifyBasics(result);

        // test recurring entry, not recurrence - this is just a test based on observation rather than opinion, since its recurring below!
        assertFalse("this should not be a recurrence", e.isRecurrence());

        // test allDay/start/end - allDay is not transferred on RecurringEntryDefinition but calculated from 'Duration.standardDays(1)'
        assertEquals(vm.getCalendarEntryViewModel().getAllDay().to, e.isAllDay());
        LocalDateTime eventStart = vm.getCalendarEntryViewModel().getStartDate().to.toLocalDateTime(startTime);
        LocalDateTime eventEnd = vm.getCalendarEntryViewModel().getStartDate().to.toLocalDateTime(startTime.plusMinutes(35));

        // test start date of repeating entry
        assertEquals(eventStart, e.getStart());
        this.assertDefaults(false, true, eventStart, e, vm.getCommandUuid());

        // test end date/time of master entry - not the end date of the recurring entry, which could be endless
        assertEquals(eventEnd, e.getEnd());

        // test future entries
        // Sun 11-11.35 for wk1 and wk2
        ResponseEntity<EventResource[]> eventsInRange = calendarActor.getEntriesByTime(eventStart.toLocalDate(), eventStart.plusWeeks(2).toLocalDate(), new Long[] {ownerContactId}, Collections.emptyList());

        String eventId = CalendarActor.getEventId(result);
        var matchingEvents = Arrays.stream(eventsInRange.getBody()).filter(ev -> ev.getEntryId().startsWith(eventId)).count();
        assertEquals(3, matchingEvents);
    }

    @Test
    public void canCreateAppointment_notAllDayWithRepeatHappyPath() {
        T vm = createCommand(BaseCommandViewModel.OPERATION_ADD, null);
        LocalDate startDate = new LocalDate();
        LocalTime startTime = new LocalTime();
        vm.getCalendarEntryViewModel().setStartDate(ChangeViewModel.changeNullTo(startDate));
        vm.getCalendarEntryViewModel().setAllDay(ChangeViewModel.changeNullTo(false));
        vm.getCalendarEntryViewModel().setStartTime(ChangeViewModel.changeNullTo(startTime));
        // allDay false. needs end time
        LocalDateTime endLocal = startDate.toLocalDateTime(startTime).plusMinutes(45);
        vm.getCalendarEntryViewModel().setEndDate(ChangeViewModel.changeNullTo(endLocal.toLocalDate()));
        vm.getCalendarEntryViewModel().setEndTime(ChangeViewModel.changeNullTo(endLocal.toLocalTime()));
        // repeat
        DaysOfWeek dow = new DaysOfWeek();
        // include today
        dow.setCalendarDayFromISO(startDate.getDayOfWeek(), true);
        dow.setWednesday(true);
        vm.getCalendarEntryViewModel().setRepeatEveryDays(ChangeViewModel.changeNullTo(dow.daysAttending()));
        vm.getCalendarEntryViewModel().setRepeatEveryWeeks(ChangeViewModel.changeNullTo(2));

        Result result = commandActor.executeCommand(vm.getCalendarEntryViewModelParent()).getBody();
        assertTrue(result.isCommandSuccessful());
        assertNotNull(result.getId());

        EventResource e = findEventResourceAndVerifyBasics(result);
        // allDay is not something that is transferred on RecurringEntryDefinition
        assertEquals(vm.getCalendarEntryViewModel().getAllDay().to, e.isAllDay());
        LocalDateTime startDt = vm.getCalendarEntryViewModel().getStartDate().to.toLocalDateTime(vm.getCalendarEntryViewModel().getStartTime().to);
        // server side clobbers the seconds
        startDt = startDt.withSecondOfMinute(0).withMillisOfSecond(0);
        assertEquals(startDt, e.getStart());
        LocalDateTime endDt = vm.getCalendarEntryViewModel().getEndDate().to.toLocalDateTime(vm.getCalendarEntryViewModel().getEndTime().to);
        endDt = endDt.withSecondOfMinute(0).withMillisOfSecond(0);
        assertEquals(endDt, startDt.plusMinutes(45));
        this.assertDefaults(false, true, startDt, e, vm.getCommandUuid());
    }

    @Test
    public void canCreateAppointment_eventCategoryHappyPath() {
        T vm = createCommand(BaseCommandViewModel.OPERATION_ADD, null);
        vm.getCalendarEntryViewModel().setStartDate(ChangeViewModel.changeNullTo(new LocalDate()));
        vm.getCalendarEntryViewModel().setAllDay(ChangeViewModel.changeNullTo(true));
        vm.getCalendarEntryViewModel().setEventCategoryId(ChangeViewModel.changeNullTo(eventCategoryId1));
        Result result = commandActor.executeCommand(vm.getCalendarEntryViewModelParent()).getBody();
        assertTrue(result.isCommandSuccessful());
        assertNotNull(result.getId());
        EventResource e = findEventResourceAndVerifyBasics(result);
        assertEquals(vm.getCalendarEntryViewModel().getAllDay().to, e.isAllDay());
        LocalDateTime dt = vm.getCalendarEntryViewModel().getStartDate().to.toLocalDateTime(LocalTime.MIDNIGHT);
        assertEquals(dt, e.getStart());
        assertEquals(vm.getCalendarEntryViewModel().getEventCategoryId().to, e.getEventCategoryId());
        this.assertDefaults(true, true, dt, e, vm.getCommandUuid());
    }

    @Test
    public void canUpdateAppointment_startDateTimeHappyPath() {
        T vm = createCommand(BaseCommandViewModel.OPERATION_ADD, null);
        vm.getCalendarEntryViewModel().setStartDate(ChangeViewModel.changeNullTo(new LocalDate()));
        vm.getCalendarEntryViewModel().setAllDay(ChangeViewModel.changeNullTo(true));
        vm.getCalendarEntryViewModel().setEventCategoryId(ChangeViewModel.changeNullTo(eventCategoryId1));

        Result created;
        { // GIVEN
            created = commandActor.executeCommand(vm.getCalendarEntryViewModelParent()).getBody();
            assertTrue(created.isCommandSuccessful());
            assertNotNull(created.getId());
        }

        T vmUpdate;
        { // WHEN
            String entryUuid = CalendarActor.getEventId(created);
            vmUpdate = createCommand(BaseCommandViewModel.OPERATION_UPDATE, entryUuid);
            vmUpdate.getCalendarEntryViewModel().setAllDay(ChangeViewModel.create(vm.getCalendarEntryViewModel().getAllDay().to, Boolean.FALSE));
            vmUpdate.getCalendarEntryViewModel().setStartTime(ChangeViewModel.changeNullTo(LocalTime.MIDNIGHT.plusMinutes(400)));
            vmUpdate.getCalendarEntryViewModel().setEndTime(ChangeViewModel.changeNullTo(LocalTime.MIDNIGHT.plusMinutes(800)));
            Result update = commandActor.executeCommand(vmUpdate.getCalendarEntryViewModelParent()).getBody();
            assertTrue(update.isCommandSuccessful());
            assertNotNull(update.getId());
        }

        { // THEN
            EventResource e = findEventResourceAndVerifyBasics(created);
            assertEquals(vmUpdate.getCalendarEntryViewModel().getAllDay().to, e.isAllDay());
            LocalDateTime dtStart = e.getStart().toLocalDate().toLocalDateTime(vmUpdate.getCalendarEntryViewModel().getStartTime().to);
            assertEquals(dtStart, e.getStart());
        }
    }

    @Test
    public void canUpdateAppointment_endDateTimeHappyPath() {
        T vm = createCommand(BaseCommandViewModel.OPERATION_ADD, null);
        Result created;
        { // GIVEN
            created = createDefaultCalendarEntry(false, vm);
        }

        T vmUpdate;
        Result update;
        { // WHEN
            String entryUuid = CalendarActor.getEventId(created);
            vmUpdate = createCommand(BaseCommandViewModel.OPERATION_UPDATE, entryUuid);
            vmUpdate.getCalendarEntryViewModel().setEndDate(ChangeViewModel.create(vm.getCalendarEntryViewModel().getStartDate().to,
                    vm.getCalendarEntryViewModel().getStartDate().to.plusDays(4)));
            vmUpdate.getCalendarEntryViewModel().setEndTime(ChangeViewModel.create(vm.getCalendarEntryViewModel().getStartTime().to,
                    vm.getCalendarEntryViewModel().getStartTime().to.plusMinutes(400)));
            update = commandActor.executeCommand(vmUpdate.getCalendarEntryViewModelParent()).getBody();
            assertTrue(update.isCommandSuccessful());
            assertNotNull(update.getId());
        }

        { // THEN
            EventResource e = findEventResourceAndVerifyBasics(created);
            assertEquals(vm.getCalendarEntryViewModel().getAllDay().to, e.isAllDay());
            LocalDateTime dtEnd = vmUpdate.getCalendarEntryViewModel().getEndDate().to.toLocalDateTime(vmUpdate.getCalendarEntryViewModel().getEndTime().to);
            assertEquals(dtEnd, e.getEnd());
            this.assertDefaults(false, true, null, e, vmUpdate.getCommandUuid());
        }
    }

    @Test
    public void canUpdateAppointment_allDayHappyPath() {
        T vm = createCommand(BaseCommandViewModel.OPERATION_ADD, null);
        Result created;
        { // GIVEN
            created = createDefaultCalendarEntry(false, vm);
        }

        T vmUpdate;
        Result updated;
        { // WHEN
            String entryUuid = CalendarActor.getEventId(created);
            vmUpdate = createCommand(BaseCommandViewModel.OPERATION_UPDATE, entryUuid);
            vmUpdate.getCalendarEntryViewModel().setAllDay(ChangeViewModel.create(false, Boolean.TRUE));
            updated = commandActor.executeCommand(vmUpdate.getCalendarEntryViewModelParent()).getBody();
            assertTrue(updated.isCommandSuccessful());
            assertNotNull(updated.getId());
        }

        { // THEN
            EventResource e = findEventResourceAndVerifyBasics(created);
            assertEquals(vmUpdate.getCalendarEntryViewModel().getAllDay().to, e.isAllDay());
            // changing to an allDay event sets the DATETIME to midnight
            assertEquals(LocalTime.MIDNIGHT, e.getStart().toLocalTime());
            // the initial createDefaultCalendarEntry sets an end date to 3 days
            assertEquals(e.getStart().plusDays(3), e.getEnd());
            this.assertDefaults(false, true, null, e, vmUpdate.getCommandUuid());
        }
    }

    @Test
    public void canUpdateAppointment_eventCategoryHappyPath() {
        T vm = createCommand(BaseCommandViewModel.OPERATION_ADD, null);
        Result created;
        { // GIVEN
            vm.getCalendarEntryViewModel().setEventCategoryId(ChangeViewModel.changeNullTo(eventCategoryId1));
            created = createDefaultCalendarEntry(false, vm);
        }

        T vmUpdate;
        Result updated;
        { // WHEN
            String entryUuid = CalendarActor.getEventId(created);
            vmUpdate = createCommand(BaseCommandViewModel.OPERATION_UPDATE, entryUuid);
            vmUpdate.getCalendarEntryViewModel().setEventCategoryId(ChangeViewModel.create(eventCategoryId1, eventCategoryId2));

            updated = commandActor.executeCommand(vmUpdate.getCalendarEntryViewModelParent()).getBody();
            assertTrue(updated.isCommandSuccessful());
            assertNotNull(updated.getId());
        }

        { // THEN
            EventResource e = findEventResourceAndVerifyBasics(created);
            assertEquals(eventCategoryId2, e.getEventCategoryId());
            this.assertDefaults(false, true, null, e, vmUpdate.getCommandUuid());
        }
    }

    @Test
    public void canUpdateAppointment_allDayEndDateHappyPath() {
        T vm = createCommand(BaseCommandViewModel.OPERATION_ADD, null);
        Result created;
        { // GIVEN
            created = createDefaultCalendarEntry(false, vm);
        }

        T vmUpdate;
        Result updated;
        { // WHEN
            String entryUuid = CalendarActor.getEventId(created);
            vmUpdate = createCommand(BaseCommandViewModel.OPERATION_UPDATE, entryUuid);
            vmUpdate.getCalendarEntryViewModel().setAllDay(ChangeViewModel.create(false, Boolean.TRUE));
            vmUpdate.getCalendarEntryViewModel().setEndDate(ChangeViewModel.create(null, vm.getCalendarEntryViewModel().getStartDate().to.plusDays(4)));
            updated = commandActor.executeCommand(vmUpdate.getCalendarEntryViewModelParent()).getBody();
            assertTrue(updated.isCommandSuccessful());
            assertNotNull(updated.getId());
        }

        { // THEN
            EventResource e = findEventResourceAndVerifyBasics(created);
            assertEquals(vmUpdate.getCalendarEntryViewModel().getAllDay().to, e.isAllDay());
            assertEquals(LocalTime.MIDNIGHT, e.getStart().toLocalTime());
            LocalDateTime dtEnd = vmUpdate.getCalendarEntryViewModel().getEndDate().to.toLocalDateTime(LocalTime.MIDNIGHT);
//            assertEquals(dtEnd, e.getEnd()); // TODO: Fails.  Adam?
            this.assertDefaults(false, true, null, e, vmUpdate.getCommandUuid());
        }
    }

    @Test
    public void canDeleteAppointment_happyPath() {
        T vm = createCommand(BaseCommandViewModel.OPERATION_ADD, null);
        Result created;
        { // GIVEN
            created = createDefaultCalendarEntry(true, vm);
        }

        T vmUpdate;
        Result update;
        { // WHEN
            String entryUuid = CalendarActor.getEventId(created);
            vmUpdate = createCommand(BaseCommandViewModel.OPERATION_REMOVE, entryUuid);
            update = commandActor.executeCommand(vmUpdate.getCalendarEntryViewModelParent()).getBody();
            assertTrue(update.isCommandSuccessful());
            assertNotNull(update.getId());
        }

        { // THEN
            assertNull(update.getLinks());
        }
    }


    /**
     * Create a recurring entry, and set an attendee on the parent entry.
     * Based on canCreateAppointment_repeatHappyPath and canUpdateAppointment_attendees2HappyPath.
     * The attendees are on the parent, and subsequent recurrences.
     */
    @Test
    public void canCreateAppointment_repeatWithAttendeesHappyPath() {

        // GIVEN repeating entry with attendees
        Result result;
        T vm;
        LocalDate expectedStart = new LocalDate().withDayOfWeek(5).plusDays(2);
        LocalDateTime expectedStartTime = expectedStart.toLocalDateTime(LocalTime.MIDNIGHT);
        {
            // create calendar cmd with serviceRecipientId
            vm = createCommand(BaseCommandViewModel.OPERATION_ADD, null);
            // Joda-Time uses the ISO standard Monday to Sunday week, so set to Friday for our repeatEveryDays
            LocalDate start = new LocalDate().withDayOfWeek(5);
            vm.getCalendarEntryViewModel().setStartDate(ChangeViewModel.changeNullTo(start));
            // need to set allDay since we don't set a time
            vm.getCalendarEntryViewModel().setAllDay(ChangeViewModel.changeNullTo(true));
            DaysOfWeek dow = new DaysOfWeek();
            // repeat on Sunday, after the Friday start date
            dow.setSunday(true);
            vm.getCalendarEntryViewModel().setRepeatEveryDays(ChangeViewModel.changeNullTo(dow.daysAttending()));
            // repeat every other week
            vm.getCalendarEntryViewModel().setRepeatEveryWeeks(ChangeViewModel.changeNullTo(2)); // trigger recurring

            // add attendee (not the same as the default srId attendee)
            CalendarAttendeeCommandViewModel avm = new CalendarAttendeeCommandViewModel(BaseCommandViewModel.OPERATION_ADD, attendee.calendarId);
            vm.getCalendarEntryViewModel().setAttendees(new CalendarAttendeeCommandViewModel[]{avm});
            //vm.getCalendarEntryViewModel().setContactIds(ChangeViewModel.changeNullTo(Collections.singletonList(attendee.individualId));

            result = commandActor.executeCommand(vm.getCalendarEntryViewModelParent()).getBody();
            assertTrue(result.isCommandSuccessful());
            assertNotNull(result.getId());
        }

        // THEN check the recurring entry
        EventResource e = findEventResourceAndVerifyBasics(result);
        {
            // NB the parent event isn't a recurrence/recurring entry itself
            assertFalse(e.isRecurrence());
            // the event can be determined if its the masterNote via BaseEventStamp#isRecurring
            assertTrue(e.isRecurringEntry());
            assertEquals(vm.getCalendarEntryViewModel().getAllDay().to, e.isAllDay());
            LocalDateTime dt = vm.getCalendarEntryViewModel().getStartDate().to.toLocalDateTime(LocalTime.MIDNIGHT);
            // test start date of repeating entry - its Friday+2 = Sunday
            assertEquals(expectedStartTime, e.getStart());
            this.assertDefaults(true, false, e.getStart(), e, vm.getCommandUuid());
        }

        // THEN check attendee on recurring entry
        {
            List<EventAttendee> eventAttendees = e.getAttendees();
            assertEquals(2, eventAttendees.size());
            List<String> names = eventAttendees.stream().map(eventAttendee -> eventAttendee.name).collect(Collectors.toList());
            assertThat("Expected attendees", names, containsInAnyOrder(attendee.firstName + " " + attendee.lastName,
                    ownerFullName));
        }

        // THEN check the recurrences (find events excludes parents - see CosmoCalendarService#findEntries exclude PROPERTY_INCLUDE_MASTER_ITEMS)
        {
            EventResource[] allEntries = calendarActor.getEntriesByTime(expectedStart, expectedStart.plusWeeks(3), new Long[]{attendee.individualId}, Collections.emptyList()).getBody();
            var recurrence = Arrays.stream(allEntries).filter(r -> !r.getStart().isAfter(expectedStartTime)).findFirst().orElseThrow();

            assertTrue(recurrence.isRecurrence());
            assertFalse(recurrence.isRecurringEntry());

            List<EventAttendee> eventAttendees = recurrence.getAttendees();
            assertEquals(2, eventAttendees.size());
            List<String> names = eventAttendees.stream().map(eventAttendee -> eventAttendee.name).collect(Collectors.toList());
            assertThat("Expected attendees", names, containsInAnyOrder(attendee.firstName + " " + attendee.lastName,
                    ownerFullName));
        }

    }

    /**
     * Previously, updating an event with different attendees was not done
     * because the existing UI didn't let us - nor did the code.
     * Not sure if this was because we didn't want to change generated events, or all that ItemEventConverterImpl could handle?
     */
    @Test
    public void canUpdateAppointment_attendeesAddHappyPath() {
        T vm = createCommand(BaseCommandViewModel.OPERATION_ADD, null);
        Result created;
        { // GIVEN a default entry (which always adds the client to the attendees)
            created = createDefaultCalendarEntry(true, vm);
        }

        T vmUpdate;
        Result update;
        { // WHEN update the event with a new contactId
            String entryUuid = CalendarActor.getEventId(created);
            vmUpdate = createCommand(BaseCommandViewModel.OPERATION_UPDATE, entryUuid);
            vmUpdate.getCalendarEntryViewModel().setContactIds(ChangeViewModel.changeNullTo(Collections.singletonList(attendee.individualId)));
            update = commandActor.executeCommand(vmUpdate.getCalendarEntryViewModelParent()).getBody();
            assertTrue(update.isCommandSuccessful());
            assertNotNull(update.getId());
        }

        { // THEN expect two attendees
            EventResource e = findEventResourceAndVerifyBasics(created);
            List<EventAttendee> eventAttendees = e.getAttendees();
            assertEquals(2, eventAttendees.size());
            List<String> names = eventAttendees.stream().map(eventAttendee -> eventAttendee.name).collect(Collectors.toList());
            assertThat("Expected attendees", names, containsInAnyOrder(attendee.firstName + " " + attendee.lastName,
                    ownerFullName));
            this.assertDefaults(true, false, e.getStart(), e, vmUpdate.getCommandUuid());
        }
    }

    /**
     * FAILURES
     */

    // Attendees who don't have calendars (only users do at the minute)
    @Test
    public void canNotCreateAppointment_attendeesWithoutCalendars() {
        T vm = createCommand(BaseCommandViewModel.OPERATION_ADD, null);
        Result created;
        { // GIVEN
            IndividualViewModel attendeeNoUser = this.createIndividualWithoutUser("some", "nonUser");
            vm.getCalendarEntryViewModel().setContactIds(ChangeViewModel.changeNullTo(Collections.singletonList(attendeeNoUser.contactId)));
            assertThrows(HttpClientErrorException.class, () -> createDefaultCalendarEntry(true, vm));
//            created = createDefaultCalendarEntry(true, vm);
        }

        { // THEN
//            EventResource e = findEventResourceAndVerifyBasics(created);
//            List<EventAttendee> eventAttendees = e.getAttendees();
//             ODD that there is no error - no calendarIds are silently failed
//            assertEquals(1, eventAttendees.size());
//            List<String> names = eventAttendees.stream().map(eventAttendee -> eventAttendee.name).collect(Collectors.toList());
//            assertThat("Expected attendees", names, contains("sysadmin"));
//            this.assertDefaults(true, true, e.getStart(), e, vm.getCommandUuid());
        }
    }

    @Test
    public void invalidCreateAppointment_startDateMissing() {
        T vm = createCommand(BaseCommandViewModel.OPERATION_ADD, null);
        vm.getCalendarEntryViewModel().setAllDay(ChangeViewModel.changeNullTo(true));
        assertThrows(HttpServerErrorException.class, () ->
                commandActor.executeCommand(vm.getCalendarEntryViewModelParent()).getBody());
    }

    @Test
    public void invalidCreateAppointment_allDayMissing() {
        T vm = createCommand(BaseCommandViewModel.OPERATION_ADD, null);
        vm.getCalendarEntryViewModel().setStartDate(ChangeViewModel.changeNullTo(new LocalDate()));
        assertThrows(HttpServerErrorException.class, () ->
                commandActor.executeCommand(vm.getCalendarEntryViewModelParent()).getBody());
    }

    @Test
    public void invalidCreateAppointment_startTimeMissing() {
        T vm = createCommand(BaseCommandViewModel.OPERATION_ADD, null);
        vm.getCalendarEntryViewModel().setStartDate(ChangeViewModel.changeNullTo(new LocalDate()));
        assertThrows(HttpServerErrorException.class, () ->
                commandActor.executeCommand(vm.getCalendarEntryViewModelParent()).getBody());
    }

    // HTTP 500 - Internal server error (NPE when can't find srId)
    @Test
    public void cannotAddUnknownServiceRecipient() {
        T vm = createCommand(BaseCommandViewModel.OPERATION_ADD, -99, null);
        vm.getCalendarEntryViewModel().setStartDate(ChangeViewModel.changeNullTo(new LocalDate()));
        vm.getCalendarEntryViewModel().setAllDay(ChangeViewModel.changeNullTo(true));
        assertThrows(HttpServerErrorException.class, () ->
                commandActor.executeCommand(vm.getCalendarEntryViewModelParent()).getBody());
    }

    protected abstract T createCommand(String operation, @Nullable String eventUuid);
    protected abstract T createCommand(String operation, int parentId, @Nullable String eventUuid);
    protected abstract void verifyCommand(UUID commandUuid);

    private IndividualViewModel createIndividualWithoutUser(String firstName, String lastName) {
        IndividualViewModel ivm = new IndividualViewModel();
        ivm.setFirstName(firstName);
        ivm.setLastName(lastName);
        Result r = contactActor.createIndividual(ivm).getBody();
        ivm = contactActor.getContactById(parseLong(r.getId())).getBody();
        return ivm;
    }

    /**
     * @param defaultMinutesDuration relies on setting minutesDuration to 240, otherwise specify one here
     */
    private Result createDefaultCalendarEntry(boolean defaultMinutesDuration, T vm) {
        vm.getCalendarEntryViewModel().setStartDate(ChangeViewModel.changeNullTo(new LocalDate()));
        vm.getCalendarEntryViewModel().setAllDay(ChangeViewModel.changeNullTo(false));
        vm.getCalendarEntryViewModel().setStartTime(ChangeViewModel.changeNullTo(new LocalTime().withSecondOfMinute(0).withMillisOfSecond(0)));
        if (defaultMinutesDuration) {
            // startDate is, and needs to be, always here for defaultMinutesDuration
            assertNotNull("startDate can't be null on defaultMinutesDuration", vm.getCalendarEntryViewModel().getStartDate());
            assertNotNull("startDate can't be null on defaultMinutesDuration", vm.getCalendarEntryViewModel().getStartDate().to);
            LocalDate ld = vm.getCalendarEntryViewModel().getStartDate().to;
            LocalTime lt = vm.getCalendarEntryViewModel().getStartTime() != null ? vm.getCalendarEntryViewModel().getStartTime().to : null;
            LocalDateTime dtEnd = lt != null
                    ? ld.toLocalDateTime(lt)
                    : ld.toLocalDateTime(LocalTime.MIDNIGHT);
            DateTime dt = dtEnd.toDateTime(DateTimeZone.forID("Europe/London"));
            DateTime dtMins = dt.plusMinutes(240);
            vm.getCalendarEntryViewModel().setEndDate(ChangeViewModel.changeNullTo(dtMins.toLocalDate()));
            vm.getCalendarEntryViewModel().setEndTime(ChangeViewModel.changeNullTo(dtMins.toLocalTime()));
        } else {
            vm.getCalendarEntryViewModel().setEndDate(ChangeViewModel.changeNullTo(vm.getCalendarEntryViewModel().getStartDate().to.plusDays(3)));
            vm.getCalendarEntryViewModel().setEndTime(ChangeViewModel.changeNullTo(vm.getCalendarEntryViewModel().getStartTime().to.plusMinutes(300))); // NOTE: This is probably wrong when spanning midnight
        }
        Result created = commandActor.executeCommand(vm.getCalendarEntryViewModelParent()).getBody();
        assertTrue(created.isCommandSuccessful());
        assertNotNull(created.getId());
        return created;
    }

    // TODO remove (and cannotRemoveWhatIsNotThere)

    private EventResource findEventResourceAndVerifyBasics(Result result) {
        String entryUuid = CalendarActor.getEventId(result);
        ResponseEntity<EventResource[]> events = calendarActor.getEntries(entryUuid);
        EventResource e = events.getBody()[0];
        assertEquals(entryUuid, e.getEntryId());
        return e;
    }

    /**
     * Ensures a user with username 'contact-no-cal-entries' exists.
     * If the user doesn't exist, it will be created.
     *
     * @return The individual user summary view model for the user
     */
    protected IndividualUserSummaryViewModel ensureEmptyIndividualWithUser() {
        final String username = "contact-no-cal-entries";
        try {
            // Try to get the user if it already exists
            return userActor.getIndividualUser(username).getBody();
        } catch (Exception e) {
            // If the user doesn't exist, create it
            return userActor.createIndividualWithUser(username, "ecco", "contact", unique.clientLastNameFor("no-cal-entries"), null);
        }
    }

    private void assertDefaults(boolean defaultEndDate, boolean defaultAttendee, LocalDateTime startDateTime,
                                EventResource e, UUID commandUuid) {
        assertNull(e.getClassNames());
        assertTrue(StringUtils.hasText(e.getRequiredLink("edit-adhoc").getHref()));
        assertEquals(EventType.Other, e.getEventType());

        assertEquals(ownerCalendarId, e.getOwnerCalendarId());

        assertEquals("", e.getTitle());
        assertThat("should be null or empty", e.getLocation(), CoreMatchers.is(emptyOrNullString()));
        // Unknown(-1), DNA(0), Success(1), Rescheduled(2);
        assertNull(e.getEventStatusId());

        // NB a default attendee comes from cosmo - where we provide a rootCalendarId if none is assigned
        // otherwise, there would be an event not associated with anyone
        // However, this does mean that the event itself does not have the attendee
        // NB if an attendee is provided, that takes precedence and is included in the events table (and the default current
        // user is not included).
        if (defaultAttendee) {
            assertEquals(1, e.getAttendees().size());
            assertEquals(ownerFullName, e.getAttendees().get(0).name);
        }
        if (defaultEndDate) {
            if (!e.isAllDay()) {
                assertEquals(startDateTime.plusMinutes(240), e.getEnd());
            } else {
                // end date can be null, but cosmo sets us with an end date (perhaps when we
                // specify a start DATETIME - not DATE - or a duration? - see other code comments in this commit)
                assertEquals(startDateTime.plusDays(1), e.getEnd());
            }
        }

        // check the audit persisted
        verifyCommand(commandUuid);
    }

}
