package com.ecco.acceptancetests.api.evidence;

import com.ecco.dom.EvidenceAction;
import com.ecco.dom.EvidenceGroup;
import com.ecco.dto.ChangeViewModel;
import com.ecco.serviceConfig.viewModel.OutcomeViewModel;
import com.ecco.webApi.evidence.GoalUpdateCommandViewModel;
import com.ecco.webApi.evidence.Schedule;
import org.joda.time.DateTimeZone;
import org.joda.time.Instant;
import org.joda.time.LocalDateTime;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.UUID;

import static com.ecco.infrastructure.time.JodaToJDKAdapters.localDateToJDk;
import static com.ecco.dto.ChangeViewModel.changeNullTo;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.Assert.*;


/**
 * This test goes (or will) through all items related to a care visit.
 * This can include normal planned actions such as (get client out of bed, give them breakfast),
 * applying a cream, which would be similar to eMAR but instead visualized using a body map.
 * Physio is similar. We use goal name to describe the issue, and goal plan to describe what is to be done.
 * keywords: eMAR, medication, body map
 * See also: DEV-2230 for eMAR chart
 */
@SuppressWarnings("FieldCanBeLocal")
public class CareVisitCommandAPITests extends SupportCommonCommandAPITests {

    private final UUID visit1WorkUuid = UUID.randomUUID();
    private final UUID visit2WorkUuid = UUID.randomUUID();
    private final LocalDateTime nowOnDay1 = now.minusDays(2);
    private final Instant nowOnDay1AsJodaInstant = nowOnDay1.toDateTime(DateTimeZone.UTC).toInstant();
    private final LocalDateTime nowOnDay2 = now.minusDays(1);
    private final LocalDateTime nowOnDay3 = now;
    private final LocalDateTime visit11amOnDay2 = nowOnDay2.withTime(11, 0, 0, 0);
    private final Instant visit11amOnDay2AsJodaInstant = visit11amOnDay2.toDateTime(DateTimeZone.UTC).toInstant();
    private final LocalDateTime visit11amOnDay3 = visit11amOnDay2.plusDays(1);
    private final Instant visit11amOnDay3AsJodaInstant = visit11amOnDay3.toDateTime(DateTimeZone.UTC).toInstant();
    private Integer oralMedicationActionDefId;
    private GoalUpdateCommandViewModel prednisolone30mgTwiceDailyFor4Weeks;
    private GoalUpdateCommandViewModel prednisolone30mgEveningFromDay2;
    private GoalUpdateCommandViewModel prednisolone30mg11amFromDay3;

    public CareVisitCommandAPITests() {
        super();
    }

    @Override
    protected void ensureDefinitionIds() {
        super.ensureDefinitionIds();
        if (oralMedicationActionDefId == null) {
            List<OutcomeViewModel> outcomes = outcomeActor.findAllByServiceName(service.getServiceName()).getBody();
            //noinspection OptionalGetWithoutIsPresent,ConstantConditions - deliberately want exception in a test
            oralMedicationActionDefId = outcomes.stream()
                    .filter(input -> "eMAR".equals(input.name))
                    .findFirst().get().actionGroups.get(0)
                    .actions.stream().filter(action -> "oral medication".equals(action.name))
                    .findFirst().get().id;
        }
    }

    @Test
    public void fullMedication_eMAR_scenario_happyPath() {
        canUseCommandsToSetUpCarePlanAndRetrievePlanSnapshot();

        visit11amOnDay2_completeOnlyOneDueOnDay2();

        verifySnapshotForVisit11amOnDay3();

        visit11amOnDay3_completeBothDueOnDay3();

        verifySnapshotAfterVisit11amOnDay3();
    }

    private void canUseCommandsToSetUpCarePlanAndRetrievePlanSnapshot() {
        setupCarePlan();

        verifyCarePlan();
    }

    /**
     * Setup a 'care plan' 2 days ago.
     * With:
     *      - goal1 task from yesterday every day for 4 weeks (without a time)
     *      - goal2 task from today (at 11am) every day for 4 weeks
     */
    private void setupCarePlan() {

        whenAddTwiceDailyMedicationParentGoal();

        whenScheduleMedsSubGoalsStartingEveningOnDay2();

        // NB other scenarios on DEV-106s, eg:
        //      Flair up of athletes foot means the antifungal cream should be applied at the frequency prescribed for several days till it clears up
        //      When Lactulose is prescribed as “when required” this means that it has to be given at the frequency prescribed for several days to be effective
        //      Take one when required up to three times a day”. This means one can be taken whenever at recommended intervals but not more than three times in 24 hours.
        var cVm = createCommentCommand(workUuid, "setup oral steroid medication schedule",
                nowOnDay1AsJodaInstant, nowOnDay1);
        commandActor.executeCommand(cVm);
    }

    private void verifyCarePlan() {
        // check the latest actions target dates based on workUuid
        {
            var lastEvidence = readBackEvidence(this.workUuid);
            assertNotNull(lastEvidence);
            assertThat(lastEvidence.actions).hasSize(3);
            var parentSnapshot = findWorkActionInstanceUuid(lastEvidence, prednisolone30mgTwiceDailyFor4Weeks.actionInstanceUuid);
            var child1Snapshot = findWorkActionInstanceUuid(lastEvidence, prednisolone30mgEveningFromDay2.actionInstanceUuid);
            var child2Snapshot = findWorkActionInstanceUuid(lastEvidence, prednisolone30mg11amFromDay3.actionInstanceUuid);

            assertNull(parentSnapshot.targetDateTime);
            assert child1Snapshot.targetDateTime != null;
            assert child2Snapshot.targetDateTime != null;
            assertEquals(nowOnDay2.toLocalDate(), child1Snapshot.targetDateTime.toLocalDate());
            assertEquals(nowOnDay3.toLocalDate(), child2Snapshot.targetDateTime.toLocalDate());
        }

        // check the latest actions target dates based on snapshot at 11am when the visit is done
        {
            var snapshot = supportEvidenceActor.findTimestampSupportSnapshot(getServiceRecipientId(), EvidenceGroup.NEEDS,
                    visit11amOnDay2.toDateTime(DateTimeZone.UTC), null).getBody();
            assert snapshot != null;
            assertThat(snapshot.latestActions).hasSize(3);
            var parentSnapshot = snapshot.latestActions.stream()
                    .filter(a -> a.actionInstanceUuid.equals(prednisolone30mgTwiceDailyFor4Weeks.actionInstanceUuid))
                    .findFirst().orElseThrow();
            var child1Snapshot = snapshot.latestActions.stream()
                    .filter(a -> a.actionInstanceUuid.equals(prednisolone30mgEveningFromDay2.actionInstanceUuid))
                    .findFirst().orElseThrow();
            var child2Snapshot = snapshot.latestActions.stream()
                    .filter(a -> a.actionInstanceUuid.equals(prednisolone30mg11amFromDay3.actionInstanceUuid))
                    .findFirst().orElseThrow();

            // checks, as above
            assertNull(parentSnapshot.targetDateTime);
            assert child1Snapshot.targetDateTime != null;
            assert child2Snapshot.targetDateTime != null;
            assertEquals(nowOnDay2.toLocalDate(), child1Snapshot.targetDateTime.toLocalDate());
            assertEquals(nowOnDay3.toLocalDate(), child2Snapshot.targetDateTime.toLocalDate());
        }
    }

    private void whenAddTwiceDailyMedicationParentGoal() {
        prednisolone30mgTwiceDailyFor4Weeks = new GoalUpdateCommandViewModel(workUuid, getServiceRecipientId(), evidenceGroup,
                defaultEvidenceTask, oralMedicationActionDefId, UUID.randomUUID(), null);
        prednisolone30mgTwiceDailyFor4Weeks.timestamp = nowOnDay1AsJodaInstant;
        prednisolone30mgTwiceDailyFor4Weeks.goalNameChange = changeNullTo("Prednisolone");
        prednisolone30mgTwiceDailyFor4Weeks.goalPlanChange = changeNullTo("take 6 5mg tablets twice a day for 4 weeks, then 3 5mg tablets for 4 weeks");
        commandActor.executeCommand(prednisolone30mgTwiceDailyFor4Weeks);
    }

    /** Set up self-admin at bedtime from firstDay + 1, and then supervised at 11am from firstDay + 2 */
    private void whenScheduleMedsSubGoalsStartingEveningOnDay2() {

        {
            // see also @Before createUniqueClientAndReferral

            // create an actionInstanceUuid with work date and created 2 days ago
            prednisolone30mgEveningFromDay2 = new GoalUpdateCommandViewModel(workUuid, getServiceRecipientId(), evidenceGroup,
                    defaultEvidenceTask, oralMedicationActionDefId, UUID.randomUUID(), prednisolone30mgTwiceDailyFor4Weeks.actionInstanceUuid);

            prednisolone30mgEveningFromDay2.goalNameChange = changeNullTo("Prednisolone evening - self-admin bedtime"); // they won't sleep!!
            prednisolone30mgEveningFromDay2.goalPlanChange = changeNullTo("Prednisolone take 6 5mg tablets");
            var start = localDateToJDk(nowOnDay2.toLocalDate());
            prednisolone30mgEveningFromDay2.targetScheduleChange = changeNullTo(Schedule.from(start, "*", start.plusWeeks(4)).getScheduleString());

            prednisolone30mgEveningFromDay2.timestamp = nowOnDay1AsJodaInstant;
            prednisolone30mgEveningFromDay2.statusChange = changeNullTo(EvidenceAction.isRelevant);
            prednisolone30mgEveningFromDay2.hierarchyChange = changeNullTo((short) 1);
            var parentGoalIndex = "0";
            prednisolone30mgEveningFromDay2.positionChange = changeNullTo(parentGoalIndex + "-0");

            commandActor.executeCommand(prednisolone30mgEveningFromDay2);
        }

        // WHEN add another smart step with the previous as the parent
        {
            // see also @Before createUniqueClientAndReferral

            // create an actionInstanceUuid with work date and created 2 days ago
            prednisolone30mg11amFromDay3 = new GoalUpdateCommandViewModel(workUuid, getServiceRecipientId(), evidenceGroup,
                    defaultEvidenceTask, oralMedicationActionDefId, UUID.randomUUID(), prednisolone30mgTwiceDailyFor4Weeks.actionInstanceUuid);

            prednisolone30mg11amFromDay3.goalNameChange = changeNullTo("Prednisolone morning - supervised 11am");
            prednisolone30mg11amFromDay3.goalPlanChange = changeNullTo("Prednisolone take 6 5mg tablets");
            var start = nowOnDay3.toLocalDate();
            prednisolone30mg11amFromDay3.targetDateChange = changeNullTo(start);
            var startSched = localDateToJDk(start);
            prednisolone30mg11amFromDay3.targetScheduleChange = changeNullTo(
                    Schedule.from(startSched, "*", java.time.LocalTime.of(11, 0), startSched.plusWeeks(4)).getScheduleString()
            );

            prednisolone30mg11amFromDay3.timestamp = nowOnDay1AsJodaInstant;
            prednisolone30mg11amFromDay3.statusChange = changeNullTo(EvidenceAction.isRelevant);
            prednisolone30mg11amFromDay3.hierarchyChange = changeNullTo((short) 1);
            var parentGoalIndex = "0";
            prednisolone30mg11amFromDay3.positionChange = changeNullTo(parentGoalIndex + "-1");

            commandActor.executeCommand(prednisolone30mg11amFromDay3);
        }
    }

    /**
     * Administer the medicine for goal1 the same day the first schedule is due, at 11am
     * NB based on SupportCommandAPITests.actionStatusChangeReasonListName_happyPath
     */
    private void visit11amOnDay2_completeOnlyOneDueOnDay2() {

        // GIVEN
        {
            // see also @Before createUniqueClientAndReferral

            // a comment and piece of work exists
            var cVm = createCommentCommand(visit1WorkUuid, "this is my visit 1",
                    visit11amOnDay2AsJodaInstant, visit11amOnDay2);
            commandActor.executeCommand(cVm);

            // AND a list def exists
            //ensureDefaultListDef();
        }

        // WHEN user submits a goal update (status reason change) on the same work
        {
            // save some work with a statusChangeReason
            var vm = new GoalUpdateCommandViewModel(visit1WorkUuid, getServiceRecipientId(), evidenceGroup,
                    defaultEvidenceTask, oralMedicationActionDefId, this.prednisolone30mgEveningFromDay2.actionInstanceUuid, null);
            vm.statusChange = ChangeViewModel.create(EvidenceAction.isRelevant, EvidenceAction.achievedAndStillRelevant);
            vm.statusChangeReason = ChangeViewModel.create(null, listDefId);
            vm.forceStatusChange = true;
            commandActor.executeCommand(vm);
        }

        // THEN check the status change was made
        {
            var lastEvidence = readBackEvidence(this.visit1WorkUuid);
            assertNotNull(lastEvidence);
            assertThat(lastEvidence.actions).hasSize(1);
            var snapshot = findWorkActionInstanceUuid(lastEvidence, this.prednisolone30mgEveningFromDay2.actionInstanceUuid);
            assertEquals(snapshot.status, EvidenceAction.achievedAndStillRelevant);
            assertEquals(listDefId, lastEvidence.actions.get(0).statusChangeReasonId);
        }

    }

    private void verifySnapshotForVisit11amOnDay3() {
        // check the latest actions target dates based on snapshot after visit1 (i.e. tomorrow)
        {
            var snapshot = supportEvidenceActor.findTimestampSupportSnapshot(getServiceRecipientId(), EvidenceGroup.NEEDS,
                    visit11amOnDay3.toDateTime(DateTimeZone.UTC), null).getBody();
            assert snapshot != null;
            assertThat(snapshot.latestActions).hasSize(3);
            var parentSnapshot = snapshot.latestActions.stream()
                    .filter(a -> a.actionInstanceUuid.equals(prednisolone30mgTwiceDailyFor4Weeks.actionInstanceUuid))
                    .findFirst().orElseThrow();
            var child1Snapshot = snapshot.latestActions.stream()
                    .filter(a -> a.actionInstanceUuid.equals(prednisolone30mgEveningFromDay2.actionInstanceUuid))
                    .findFirst().orElseThrow();
            var child2Snapshot = snapshot.latestActions.stream()
                    .filter(a -> a.actionInstanceUuid.equals(prednisolone30mg11amFromDay3.actionInstanceUuid))
                    .findFirst().orElseThrow();

            // checks, as above
            assertNull(parentSnapshot.targetDateTime);
            assert child1Snapshot.targetDateTime != null;
            assert child2Snapshot.targetDateTime != null;
            assertEquals(nowOnDay3.toLocalDate(), child2Snapshot.targetDateTime.toLocalDate());
            assertEquals(nowOnDay3.toLocalDate(), child1Snapshot.targetDateTime.toLocalDate());
        }
    }

    /**
     * Administer the medicine for goal1 and goal2 the next day the first schedule is due, at 11am
     * NB based on SupportCommandAPITests.actionStatusChangeReasonListName_happyPath
     */
    private void visit11amOnDay3_completeBothDueOnDay3() {

        // GIVEN
        {
            // see also @Before createUniqueClientAndReferral
            var cVm = createCommentCommand(visit2WorkUuid, "this is my visit 2",
                    visit11amOnDay3AsJodaInstant, visit11amOnDay3);
            commandActor.executeCommand(cVm);
        }

        // WHEN include a goal1 update (status reason change) on the same work
        {
            var vm = new GoalUpdateCommandViewModel(visit2WorkUuid, getServiceRecipientId(), evidenceGroup,
                    defaultEvidenceTask, oralMedicationActionDefId, this.prednisolone30mgEveningFromDay2.actionInstanceUuid, null);
            vm.statusChange = ChangeViewModel.create(EvidenceAction.isRelevant, EvidenceAction.achievedAndStillRelevant);
            vm.statusChangeReason = ChangeViewModel.create(null, listDefId);
            vm.forceStatusChange = true;
            commandActor.executeCommand(vm);
        }

        // WHEN include a goal2 update (status reason change) on the same work
        {
            var vm2 = new GoalUpdateCommandViewModel(visit2WorkUuid, getServiceRecipientId(), evidenceGroup,
                    defaultEvidenceTask, oralMedicationActionDefId, this.prednisolone30mg11amFromDay3.actionInstanceUuid, null);
            vm2.statusChange = ChangeViewModel.create(EvidenceAction.isRelevant, EvidenceAction.achievedAndStillRelevant);
            vm2.statusChangeReason = ChangeViewModel.create(null, listDefId);
            vm2.forceStatusChange = true;
            commandActor.executeCommand(vm2);
        }

        // THEN check the status change was made
        {
            var lastEvidence = readBackEvidence(this.visit2WorkUuid);
            assertNotNull(lastEvidence);
            assertThat(lastEvidence.actions).hasSize(2);
            var snapshot1 = findWorkActionInstanceUuid(lastEvidence, this.prednisolone30mgEveningFromDay2.actionInstanceUuid);
            var snapshot2 = findWorkActionInstanceUuid(lastEvidence, this.prednisolone30mg11amFromDay3.actionInstanceUuid);
            assertEquals(snapshot1.status, EvidenceAction.achievedAndStillRelevant);
            assertEquals(snapshot2.status, EvidenceAction.achievedAndStillRelevant);
            assertEquals(listDefId, snapshot1.statusChangeReasonId);
            assertEquals(listDefId, snapshot2.statusChangeReasonId);
        }

    }

    private void verifySnapshotAfterVisit11amOnDay3() {
        // check the latest actions target dates based on snapshot after visit1 (i.e. tomorrow)
        {
            var snapshot = supportEvidenceActor.findTimestampSupportSnapshot(getServiceRecipientId(), EvidenceGroup.NEEDS,
                    visit11amOnDay3.plusDays(1).toDateTime(DateTimeZone.UTC), null).getBody();
            assert snapshot != null;
            assertThat(snapshot.latestActions).hasSize(3);
            var parentSnapshot = snapshot.latestActions.stream()
                    .filter(a -> a.actionInstanceUuid.equals(prednisolone30mgTwiceDailyFor4Weeks.actionInstanceUuid))
                    .findFirst().orElseThrow();
            var child1Snapshot = snapshot.latestActions.stream()
                    .filter(a -> a.actionInstanceUuid.equals(prednisolone30mgEveningFromDay2.actionInstanceUuid))
                    .findFirst().orElseThrow();
            var child2Snapshot = snapshot.latestActions.stream()
                    .filter(a -> a.actionInstanceUuid.equals(prednisolone30mg11amFromDay3.actionInstanceUuid))
                    .findFirst().orElseThrow();

            // checks, as above
            assertNull(parentSnapshot.targetDateTime);
            assert child1Snapshot.targetDateTime != null;
            assert child2Snapshot.targetDateTime != null;
            assertEquals(nowOnDay3.plusDays(1).toLocalDate(), child1Snapshot.targetDateTime.toLocalDate());
            // NB We can't verify if we are due at the right time, since the targetDate doesn't include a time - only the schedule generates it correctly
            assertEquals(nowOnDay3.plusDays(1).toLocalDate(), child2Snapshot.targetDateTime.toLocalDate());
        }
    }
}

