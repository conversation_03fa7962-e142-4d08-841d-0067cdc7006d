package com.ecco.acceptancetests.api;

import static com.ecco.config.service.FeatureEnablementVoter.Vote.ENABLED_BY_DEFAULT;
import static java.util.Objects.requireNonNull;

import com.ecco.acceptancetests.Constants;
import com.ecco.dto.ChangeViewModel;
import com.ecco.test.support.UniqueDataService;
import com.ecco.acceptancetests.api.contract.ContractStepsWebApi;
import com.ecco.acceptancetests.api.referral.ReferralStepsWebApi;
import com.ecco.acceptancetests.api.rota.RotaStepsWebApi;
import com.ecco.acceptancetests.api.userManagement.UserManagementStepsWebApi;
import com.ecco.acceptancetests.steps.ContractSteps;
import com.ecco.acceptancetests.steps.ReferralSteps;
import com.ecco.acceptancetests.steps.UserManagementSteps;
import com.ecco.data.client.WebApiSettings;
import com.ecco.data.client.actors.*;
import com.ecco.infrastructure.config.web.ConvertersConfig;
import com.ecco.infrastructure.rest.RestClient;
import com.ecco.infrastructure.rest.ThrowOnlyOn400And5xxErrorHandler;
import com.ecco.webApi.featureConfig.FeatureVoteChangeCommandViewModel;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableMap.Builder;

import org.eeichinger.testing.web.TestTracerExtension;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.client.RestTemplate;

import java.util.Map.Entry;

@org.junit.jupiter.api.Timeout(100) // so we get Timeout on JUnit5 tests too
public abstract class BaseJsonTest {

    protected final Logger log = LoggerFactory.getLogger(getClass());

    protected final String baseUrl = WebApiSettings.APPLICATION_URL;
    protected final String apiBaseUrl = WebApiSettings.APPLICATION_URL + "/api/";
    protected final RestClient restClient = new RestClient(baseUrl);
    protected final RestTemplate restTemplate = restClient.template();
    protected final ObjectMapper objectMapper = ConvertersConfig.getObjectMapper();

    protected final UniqueDataService unique = UniqueDataService.instance;

    @RegisterExtension
    TestTracerExtension testTracerExtension = new TestTracerExtension(restClient);

    {
        restTemplate.setErrorHandler(new ThrowOnlyOn400And5xxErrorHandler());
    }

    protected final AddressActor addressActor = new AddressActor(restTemplate);
    protected final AdminActor adminActor = new AdminActor(restTemplate);
    protected final BuildingActor buildingActor = new BuildingActor(restTemplate);
    protected final BaseCommandActor commandActor = new BaseActor(restTemplate);
    protected final CacheActor cacheActor = new CacheActor(restTemplate);
    protected final CalendarActor calendarActor = new CalendarActor(restTemplate);
    protected final ClientActor clientActor = new ClientActor(restTemplate);
    protected final UpdateAuditTimestampCommandActor updateAuditTimestampCommandActor = new UpdateAuditTimestampCommandActor(restTemplate);
    protected final CommentCommandActor commentCommandActor = new CommentCommandActor(restTemplate);
    protected final ContactActor contactActor = new ContactActor(restTemplate);
    protected final EvidenceAssociatedContactCommandActor associatedContactActor = new EvidenceAssociatedContactCommandActor(restTemplate);
    protected final EvidenceFormSnapshotActor evidenceFormSnapshotActor = new EvidenceFormSnapshotActor(restTemplate);
    protected final GroupSupportActor groupSupportActor = new GroupSupportActor(restTemplate);
    protected final CalendarEventSnapshotActor loneWorkerActor = new CalendarEventSnapshotActor(restTemplate);
    protected final ListDefActor listDefActor = new ListDefActor(restTemplate);
    protected final OutcomeActor outcomeActor = new OutcomeActor(restTemplate);
    protected final ActionGroupActor actionGroupActor = new ActionGroupActor(restTemplate);
    protected final ActionActor actionActor = new ActionActor(restTemplate);
    protected final ProjectActor projectActor = new ProjectActor(restTemplate);
    protected final AgreementActor agreementActor = new AgreementActor(restTemplate);
    protected final ContractActor contractActor = new ContractActor(restTemplate);
    protected final FinanceReceiptActor financeReceiptActor = new FinanceReceiptActor(restTemplate);
    protected final FinanceChargeActor financeChargeActor = new FinanceChargeActor(restTemplate);
    protected final RotaActivityInvoiceActor rotaActivityInvoiceActor = new RotaActivityInvoiceActor(restTemplate);
    protected final IncidentActor incidentActor = new IncidentActor(restTemplate);
    protected final RepairActor repairActor = new RepairActor(restTemplate);
    protected final ManagedVoidActor managedVoidActor = new ManagedVoidActor(restTemplate);
    protected final QuestionGroupActor questionGroupActor = new QuestionGroupActor(restTemplate);
    protected final QuestionnaireEvidenceActor questionnaireEvidenceActor = new QuestionnaireEvidenceActor(restTemplate);
    protected final ReferralActor referralActor = new ReferralActor(restTemplate);
    protected final ResourcesActor resourcesActor = new ResourcesActor(restTemplate);
    protected final ReportActor reportActor = new ReportActor(restTemplate);
    protected final RiskEvidenceActor riskEvidenceActor = new RiskEvidenceActor(restTemplate);
    protected final RotaActor rotaActor = new RotaActor(restTemplate);
    protected final SchemaActor schemaActor = new SchemaActor(restTemplate);
    protected final SecurePayloadActor securePayloadActor = new SecurePayloadActor(restTemplate);
    protected final ServiceActor serviceActor = new ServiceActor(restTemplate);
    protected final ServiceRecipientActor serviceRecipientActor = new ServiceRecipientActor(restTemplate);
    protected final ServiceTypeActor serviceTypeActor = new ServiceTypeActor(restTemplate);
    protected final SessionDataActor sessionDataActor = new SessionDataActor(restTemplate);
    protected final SettingActor settingActor = new SettingActor(restTemplate);
    protected final SingleValueHistoryActor singleValueHistoryActor = new SingleValueHistoryActor(restTemplate);
    protected final SupportEvidenceActor supportEvidenceActor = new SupportEvidenceActor(restTemplate);
    protected final TaskDefinitionActor taskDefActor = new TaskDefinitionActor(restTemplate);
    protected final TaskStatusActor taskStatusActor = new TaskStatusActor(restTemplate);
    protected final UserActor userActor = new UserActor(restClient.getLastLoggedInUsernameSupplier(), restTemplate);
    protected final UserDeviceActor userDeviceActor = new UserDeviceActor(restTemplate);
    protected final WorkflowActor workflowActor = new WorkflowActor(restTemplate);
    protected final WorkerActor workerActor = new WorkerActor(restTemplate);

    protected final ContractSteps contractSteps = new ContractStepsWebApi(restTemplate, contractActor);

    protected final ReferralSteps referralSteps = new ReferralStepsWebApi(restTemplate, referralActor, agreementActor,
            workflowActor, sessionDataActor, clientActor, contactActor, calendarActor, userActor, listDefActor, serviceActor, cacheActor);

    // RotaSteps alone doesn't allow access to some underlying command creation
    protected RotaStepsWebApi rotaSteps = new RotaStepsWebApi(restTemplate, sessionDataActor, workerActor, agreementActor, referralActor, calendarActor,
            buildingActor, rotaActor, serviceRecipientActor, serviceActor, referralSteps);

    protected UserManagementSteps userManagementSteps = new UserManagementStepsWebApi(restTemplate, userActor);

    @BeforeEach
    public void loginAsSysadmin() {
        loginAs(Constants.SYSADMIN_USERNAME, Constants.SYSADMIN_PASSWORD);
    }
    public void loginAs(String username, String password) {
        restClient.login(username, password);
    }

    @AfterEach
    public void logout() {
        restClient.logout();
    }

    protected ImmutableMap<String, Object> mapOf(Entry<String, ?>... entries) {
        Builder<String, Object> builder = ImmutableMap.builder();
        for (Entry<String, ?> entry : entries) {
            builder.put(entry);
        }
        return builder.build();
    }

    public void ensureFeatureToggleEnabled(String featureName) {
        var viewModel = new FeatureVoteChangeCommandViewModel();
        viewModel.name = featureName;
        viewModel.voteChange = ChangeViewModel.changeNullTo(ENABLED_BY_DEFAULT);
        commandActor.executeCommand(viewModel);
    }

    public void ensureCurrentUserHasGroup(String group) {
        var userViewModel = userActor.getLoggedInUser().getBody();
        if (!requireNonNull(userViewModel).groups.contains(group)) {
            userActor.addUserGroups(userViewModel.userId, group);
        }
    }
}
