package com.ecco.acceptancetests.api.evidence;

import com.ecco.test.support.UniqueDataService;
import com.ecco.acceptancetests.api.BaseJsonTest;
import com.ecco.data.client.ServiceOptions;
import com.ecco.webApi.contacts.ClientViewModel;
import com.ecco.dto.ChangeViewModel;
import com.ecco.webApi.evidence.ReferralViewModel;
import com.ecco.webApi.serviceConfig.TaskDefinitionViewModel;
import org.assertj.core.api.Condition;
import org.assertj.core.util.Objects;
import org.joda.time.LocalDate;
import org.joda.time.LocalDateTime;
import org.junit.jupiter.api.BeforeEach;

import org.jspecify.annotations.NonNull;
import java.util.List;
import java.util.Random;

public class BaseReferralCommandAPITests extends BaseJsonTest {

    /** Single 'now' used as reference for multiple tests.. timestamps are never unique */
    protected final static LocalDateTime now = new LocalDateTime();

    static protected Random random = new Random();
    protected ReferralViewModel rvm;
    protected ClientViewModel cvm;
    protected List<TaskDefinitionViewModel> taskDefinitions;
    protected final ServiceOptions service;
    protected final UniqueDataService unique = UniqueDataService.instance;

    @NonNull
    static <C> Condition<ChangeViewModel<C>> changesFromNullTo(C expected) {
        return new Condition<>(change -> change != null && change.to.equals(expected) && change.from == null,
                "a change from null to %s",
                expected);
    }

    @NonNull
    static <C> Condition<C> resultOfChange(ChangeViewModel<C> change) {
        return new Condition<>(value -> change != null && Objects.areEqual(change.to, value),
                "the result of a change to: %s",
                change.to);
    }

    public BaseReferralCommandAPITests(ServiceOptions service) {
        super();
        this.service = service;
    }

    @BeforeEach
    public void createUniqueClientAndReferral() {
        // emulating @BeforeClass but we want our injected services
        if (rvm == null) {
            this.taskDefinitions = java.util.Objects.requireNonNull(sessionDataActor.getSessionData().getBody()).taskDefinitions;
            LocalDate received = LocalDate.now();
            // impl that talks API for those
            String firstNameUnique = unique.clientFirstNameFor("zeberdee");
            String lastNameUnique = unique.clientLastNameFor("inglenook");
            rvm = referralActor.createReferralAsStarted(firstNameUnique, lastNameUnique, received, service);
            cvm = clientActor.getClientById(rvm.clientId).getBody();
        }
    }

    protected void clearReferral() {
        rvm = null;
    }

    protected long getReferralId() {
        return rvm.referralId;
    }

    protected int getServiceRecipientId() {
        return rvm.serviceRecipientId;
    }

    protected ClientViewModel getClient() {
        return cvm;
    }

}
