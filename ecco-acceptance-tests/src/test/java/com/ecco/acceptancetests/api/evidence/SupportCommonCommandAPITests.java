package com.ecco.acceptancetests.api.evidence;

import com.ecco.data.client.ServiceOptions;
import com.ecco.dom.EvidenceGroup;
import com.ecco.evidence.EvidenceTask;
import com.ecco.serviceConfig.viewModel.OutcomeViewModel;
import com.ecco.webApi.evidence.*;
import com.ecco.webApi.listsConfig.ListDefinitionEntryViewModel;
import org.joda.time.*;

import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

import static com.ecco.dom.EvidenceGroup.NEEDS;
import static com.ecco.dto.ChangeViewModel.changeNullTo;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.Assert.*;


public class SupportCommonCommandAPITests extends BaseEvidenceCommandAPITests<EvidenceSupportWorkViewModel> {

    protected static Integer listDefId;
    protected static Integer actionDefId; // 119, which has a hact mapping
    protected static Long fileId;

    public SupportCommonCommandAPITests() {
        super(NEEDS, EvidenceTask.NEEDS_ASSESSMENT, ServiceOptions.ACCOMMODATION);
    }

    protected EvidenceSupportWorkViewModel getAndVerifyUpdateGotApplied(BaseGoalUpdateCommandViewModel updateCmd) {
        EvidenceSupportWorkViewModel evidence = readBackEvidence(updateCmd.workUuid);
        assertNotNull(evidence);
//      assertEquals(updateCmd.targetDateChange.to, evidence.supportActions.get(0).targetDate);
        return evidence;
    }

    protected EvidenceSmartStepViewModel checkSnapshotContains(UUID instanceUuid) {
        SupportSmartStepsSnapshotViewModel snapshot = findLatestSnapshotByServiceRecipientId(NEEDS);
        List<EvidenceSmartStepViewModel> actions = snapshot.latestActions.stream()
                .filter(entry -> entry.actionInstanceUuid.equals(instanceUuid))
                .collect(Collectors.toList());
        assertThat(actions).hasSize(1);
        return actions.get(0);
    }

    protected EvidenceSupportWorkViewModel readBackEvidence(UUID workUuid) {
        return findWorkByUuid(workUuid);
    }

    @Override
    protected void ensureDefinitionIds() {
        if (actionDefId == null) {
            List<OutcomeViewModel> outcomes = outcomeActor.findAllByServiceName(service.getServiceName()).getBody();
            //noinspection OptionalGetWithoutIsPresent,ConstantConditions - deliberately want exception in a test
            actionDefId = outcomes.stream()
                    .filter(input -> "use of time".equals(input.name))
                    .findFirst().get().actionGroups.get(0).actions.get(0).id;
        }
    }

    protected void ensureListDefinitionIds() {
        if (listDefId != null) {
            return;
        }

        ListDefinitionEntryViewModel vm = new ListDefinitionEntryViewModel();
        vm.listName = "testing_statusReasonChange";
        vm.name = "unachieved smart step because client left";

        // if we have an error, check its an OK one
        listDefId = listDefActor.ensureAndReturnListDefinitionEntry(vm.listName, vm).iterator().next().id;
    }

    @Override
    protected List<EvidenceSupportWorkViewModel> findWorkSummaryByServiceRecipientId() {
        return supportEvidenceActor.findAllSupportWorkSummaryByServiceRecipientId(getServiceRecipientId(), NEEDS).getBody().getContent();
    }

    // TODO refactor to use the underlying /snapshots
    protected SupportSmartStepsSnapshotViewModel findLatestSnapshotByServiceRecipientId(EvidenceGroup evidenceGroup) {
        return supportEvidenceActor.findLatestSupportSnapshot(getServiceRecipientId(), evidenceGroup).getBody();
    }

    protected List<EvidenceSupportWorkViewModel> findWorkSummaryWithAttachmentsByServiceRecipientId() {
        return supportEvidenceActor.findAttachmentsSupportWorkSummaryByServiceRecipientId(getServiceRecipientId(), NEEDS).getBody().getContent();
    }

    protected GoalUpdateCommandViewModel sendDefaultGoalUpdateCommand() {
        GoalUpdateCommandViewModel vm =
                new GoalUpdateCommandViewModel(workUuid, getServiceRecipientId(), evidenceGroup,
                        defaultEvidenceTask, actionDefId, UUID.randomUUID(), null);

        vm.targetDateChange = changeNullTo(new LocalDate(2015,4,7));

        commandActor.executeCommand(vm);
        return vm;
    }

}
