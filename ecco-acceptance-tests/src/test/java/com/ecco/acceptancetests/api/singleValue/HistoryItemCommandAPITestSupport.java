
package com.ecco.acceptancetests.api.singleValue;

import com.ecco.acceptancetests.api.BaseJsonTest;
import com.ecco.data.client.ServiceOptions;
import com.ecco.webApi.contacts.ClientViewModel;
import com.ecco.dto.ChangeViewModel;
import com.ecco.webApi.evidence.ReferralViewModel;
import com.ecco.webApi.singleValue.HistoryItemCommandViewModel;
import com.ecco.webApi.singleValue.HistoryItemViewModel;
import com.ecco.webApi.viewModels.Result;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.web.client.HttpServerErrorException;

import java.time.LocalDateTime;
import java.util.Random;

import static org.junit.Assert.*;

public abstract class HistoryItemCommandAPITestSupport<T extends HistoryItemViewModel, R extends HistoryItemCommandViewModel> extends BaseJsonTest {

    //protected final static LocalDateTime now = new LocalDateTime();
    //private final UniqueDataService unique = UniqueDataService.instance;
    protected static String ADD = HistoryItemCommandViewModel.OPERATION_ADD;
    protected static String UPDATE = HistoryItemCommandViewModel.OPERATION_UPDATE;
    protected static String REMOVE = HistoryItemCommandViewModel.OPERATION_REMOVE;

    static protected Random random = new Random();
    static protected ReferralViewModel rvm;
    static protected ClientViewModel cvm;
    static protected final LocalDateTime pastDate = LocalDateTime.now().minusDays(50).withNano(0);
    static protected int daysCounter = 0; // could use AtomicInteger if it mattered

    protected final ServiceOptions service = ServiceOptions.ACCOMMODATION;

    protected interface HistoryItemTestParams<T> {
        T createRandomItem(); // SVH implement this with differing number, so use counter++
        boolean testVars(T expected, T actual); //   return value.equals(value.longValue());
    }
    protected HistoryItemTestParams<T> testParams;

    @BeforeEach
    public void createClientAndReferral() {

        // emulating @BeforeClass but we want our injected services
        if (rvm == null) {
            org.joda.time.LocalDate received = org.joda.time.LocalDate.now();
            rvm = referralActor.createReferralAsStarted("single", "value", received, service);
            cvm = clientActor.getClientById(rvm.clientId).getBody();
        }
    }

    @Test
    public void canAcceptSomeCommand() {
        T params = testParams.createRandomItem();

        R tvm = createCmd(ADD, params);

        Result result = commandActor.executeCommand(tvm).getBody();
        assertTrue(result.isCommandSuccessful());
        assertNotNull(result.getId());
    }

    @Test
    public void canCreate() {
        T item = testParams.createRandomItem();

        this.createEntry(item);

        T[] vms = getHistory();
        assertNotNull(vms);
        assertTrue(testParams.testVars(item, vms[0]));
        assertEquals(pastDate.plusDays(daysCounter), vms[0].validFrom.withNano(0));
        assertNull(vms[0].validTo);

        // TODO test the value in the entity itself, not just the history
        //ReferralViewModel rvmReload = referralActor.getReferralById(rvm.referralId).getBody();
        //assertEquals(value, rvmReload.deliveredById);
    }

    // HTTP 400 - Bad Requst (Valid command but failed to execute) thrown by spring-data when deleting something that doesn't exist
    @Test
    public void cannotRemoveWhatIsNotThere() {

        R vm = createCmd(REMOVE, null);
        vm.id = -99;

        assertThrows(HttpServerErrorException.class, () -> commandActor.executeCommand(vm));
    }

    // HTTP 500 - Internal server error (NPE when can't find srId)
    @Test
    public void cannotAddUnknownServiceRecipient() {
        R vm = this.createCommand(ADD, -99);
        assertThrows(HttpServerErrorException.class, () -> commandActor.executeCommand(vm));
    }

    @Test
    public void canCreateAndRemove() {
        T item = testParams.createRandomItem();
        this.createEntry(item);

        R vm = createCmd(REMOVE, null);
        T[] vms = getHistory();
        vm.id = vms[0].id;
        commandActor.executeCommand(vm);

        T[] vms2 = getHistory();
        assertNotEquals(vm.id, vms2.length > 0 ? vms2[0].id : null);
    }

    @Test
    public void canUpdateValue() {
        T params1 = testParams.createRandomItem();
        this.createEntry(params1);

        T[] vms = getHistory();
        int vmId = vms[0].id;

        T params2 = testParams.createRandomItem();
        R vm = createCmd(UPDATE, params2);
        vm.id = vmId;
        commandActor.executeCommand(vm);

        T[] vms2 = getHistory();
        assertEquals(vmId, vms2[0].id.intValue());
        assertTrue(testParams.testVars(params2, vms2[0]));
    }

    @Test
    public void canUpdateValidFrom() {
        T params = testParams.createRandomItem();
        this.createEntry(params);

        T[] vms = getHistory();;
        int vmId = vms[0].id;

        // validFrom is already changed
        R vm = createCmd(UPDATE, params);
        vm.id = vmId;
        commandActor.executeCommand(vm);

        T[] vms2 = getHistory();
        assertEquals(vmId, vms2[0].id.intValue());
        assertEquals(vms[0].validFrom.plusDays(1), vms2[0].validFrom);
    }

    @Test
    public void validToSetOnNewEntry() {
        T params1 = testParams.createRandomItem();
        // create older entry
        this.createEntry(params1);
        // create newer entry - validFrom becomes the next day
        T params2 = testParams.createRandomItem();
        this.createEntry(params2);

        // check the older entry has a validTo of the newer validFrom
        T[] vms1 = getHistory();
        assertEquals(vms1[1].validTo, vms1[0].validFrom);
    }

    @Test
    public void validFromUnchangedOnUpdatedEntry() {
        // create older entry
        T params1 = testParams.createRandomItem();
        this.createEntry(params1);
        // create newer entry - validFrom becomes the next day
        T params2 = testParams.createRandomItem();
        this.createEntry(params2);

        T[] vms = getHistory();
        int newerId = vms[0].id;
        LocalDateTime olderValidTo = vms[1].validTo;

        // update the newer entry - just the value
        T params3 = testParams.createRandomItem();
        R vm = createCmd(UPDATE, params3);
        vm.validFrom = null; // specifically test that we can edit without  validFrom
        vm.id = newerId;
        commandActor.executeCommand(vm);

        // check the older entry has the same validTo
        T[] vms2 = getHistory();
        assertEquals(olderValidTo, vms2[1].validTo);
    }

    @Test
    public void validFromChangedOnUpdatedEntry() {
        T params1 = testParams.createRandomItem();
        // create older entry
        this.createEntry(params1);
        // create newer entry - validFrom becomes the next day
        T params2 = testParams.createRandomItem();
        this.createEntry(params2);

        T[] vms = getHistory();
        int newerId = vms[0].id;
        LocalDateTime newerValidTo = vms[0].validFrom;

        // update the newer entry - validFrom is already changed
        T params3 = testParams.createRandomItem();
        R vm = createCmd(UPDATE, params3);
        vm.id = newerId;
        //vm.validFrom has been moved on a day for the newerId
        commandActor.executeCommand(vm);

        // check the older entry has the updated validTo
        T[] vms2 = getHistory();
        assertEquals(newerValidTo.plusDays(1), vms2[0].validFrom);
        assertEquals(newerValidTo.plusDays(1), vms2[1].validTo);
    }

    @Test
    public void validToClearedWhenEntryRemoved() {
        T params1 = testParams.createRandomItem();
        // create older entry
        this.createEntry(params1);
        // create newer entry - validFrom becomes the next day
        T params2 = testParams.createRandomItem();
        this.createEntry(params2);

        T[] vms = getHistory();
        int newerId = vms[0].id;

        // check the older entry does have a validTo
        assertEquals(vms[0].validFrom, vms[1].validTo);
        Integer olderId = vms[1].id;

        // remove the newer entry
        R vm = createCmd(REMOVE, null);
        vm.id = newerId;
        commandActor.executeCommand(vm);

        // check the older entry (which is now the newest) has cleared the validTo
        T[] vms2 = getHistory();
        assertEquals(olderId, vms2[0].id);
        assertNull(vms2[0].validTo);
    }

    private void createEntry(T vars) {
        R tvm = createCmd(ADD, vars);
        commandActor.executeCommand(tvm);
    }

    private R createCmd(String operation, T params) {
        R cmd = createCommand(operation, params);
        daysCounter++;
        cmd.validFrom = ChangeViewModel.create(null, pastDate.plusDays(daysCounter));
        return cmd;
    }

    protected abstract R createCommand(String operation, int serviceRecipientId);

    protected abstract R createCommand(String operation, T params);

    protected abstract T[] getHistory();

}