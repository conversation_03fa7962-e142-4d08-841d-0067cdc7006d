package com.ecco.acceptancetests.api.evidence;

import org.junit.jupiter.api.Test;

import com.ecco.acceptancetests.api.BaseJsonTest;

/**
 * Calculations for HACT are in client-side code, hact-test.ts.
 * The server-side is a mix of smart step and questionnaire history, which are already tested.
 * This class serves as a go-to place to see how to get HACT up and running locally, but the guides
 * should be referred to when running and testing.
 */
public class HactAPITests extends BaseJsonTest {

    @Test
    public void configSetupHACT() {
        // enable the hact module - insert into cfg_module (name, enabled) values ("hact", true);
        // enable the -Ddb.extraContexts=hact for live/test
        // NB can exclude hact on services with parameters {["hact.exclude": true]}
        // add 'hactQuestionnaire' to pathway
        // hactoutcomemappings triggers EITHER actionDefId OR a questionDefId (hardcoded name evidencemixed1002 EVIDENCE_QUESTIONNAIRE)
        //      NB the questionDefId needs any answer to trigger
        // the hactoutcomemappings then looks at hactoutcomeevidencesurvey.hactOutcomeDefCode which may not exist

        // OOTB data
        // NB with acceptanceTests or hact, the hact-survey.sql is ran which populates the hactoutcomeevidencesurvey
        // enable the -Ddb.extraContexts=acceptanceTests is sufficient for tests
        // create referral on a service, eg accommodation or floating support
        // mapped needs assessment: 'economic wellbeing' and 'Support to maximise income eg - correct welfare benefits, application for grants etc.'
        // mapped needs assessment: 'use of time' and 'Accept support and engage with keyworker to investigate any opportunities with employment Agency opportunities'
    }

}
