package com.ecco.acceptancetests.api.referral;

import com.ecco.acceptancetests.api.BaseJsonTest;
import com.ecco.data.client.ReferralOptions;
import com.ecco.infrastructure.config.web.ConvertersConfig;
import com.ecco.webApi.evidence.ReferralViewModel;
import com.ecco.webApi.viewModels.DeleteRequestServiceRecipientCommandViewModel;
import com.ecco.webApi.viewModels.DeleteServiceRecipientCommandViewModel;
import com.ecco.webApi.viewModels.Result;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.util.StdDateFormat;
import com.jayway.jsonpath.JsonPath;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpClientErrorException.BadRequest;

import java.util.List;
import java.util.Map;
import java.util.UUID;

import static com.ecco.data.client.ServiceOptions.DEMO_ALL;
import static org.assertj.core.api.Assertions.fail;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.iterableWithSize;
import static org.junit.Assert.*;

@SuppressWarnings({"ConstantConditions", "ResultOfMethodCallIgnored", "CatchMayIgnoreException", "OptionalGetWithoutIsPresent", "SameParameterValue", "rawtypes"})
public class DeleteServiceRecipientAPITests extends BaseJsonTest {

    static ObjectMapper mapper = ConvertersConfig.getObjectMapper()
                    .setDateFormat(new StdDateFormat());

    @Test
    public void deleteRequestReferral() {
        ReferralViewModel referralViewModel = createReferral("Delete-Request", "Test", "AB123456R");

        deleteRequest(referralViewModel.serviceRecipientId, false);

        ReferralViewModel deleteRequested = referralActor.getReferralById(referralViewModel.referralId).getBody();
        assertTrue("Expect to have requestedDelete", deleteRequested.requestedDelete);

        // revoke
        deleteRequest(referralViewModel.serviceRecipientId, true);
        ReferralViewModel deleteRequestRevoked = referralActor.getReferralById(referralViewModel.referralId).getBody();
        assertFalse("Expect to not have requestedDelete", deleteRequestRevoked.requestedDelete);
    }

    public ReferralViewModel createReferral(String firstName, String lastName, String niNumber) {
        ReferralOptions options = new ReferralOptions().requiresProjects(false)
                .requiresDataProtection(true)
                .requiresEmergencyDetails(true);
        long referralId = referralSteps.processReferralToId(options, DEMO_ALL, firstName, lastName, niNumber, null);
        return referralActor.getReferralById(referralId).getBody();
    }

    @Test
    public void deleteReferral() {

        // GIVEN a referral
        ReferralViewModel referralViewModel = createReferral("Delete", "Test", "*********");

        // WHEN delete request
        UUID requestDeletionUuid = deleteRequest(referralViewModel.serviceRecipientId, false);

        // WHEN delete for real
        delete(referralViewModel, requestDeletionUuid, "am fine with this");

        // THEN referral doesn't exist, and delete command does
        {
            try {
                referralActor.getReferralById(referralViewModel.referralId).getBody();
                fail("We should be getting NPE here, because the entity is deleted (the api request, and tx, was completed)");
            } catch (HttpClientErrorException e) {
                Assertions.assertThat(e.getStatusCode()).isEqualTo(HttpStatus.NOT_FOUND);
            }

            verifyDeleteCommand(referralViewModel.serviceRecipientId);
        }

    }

    @Test
    public void deleteReferralFailsWhenInvalidRequestUuid() {

        // GIVEN a referral
        ReferralViewModel referralViewModel = createReferral("DeleteNoReq", "Test", "*********");

        // WHEN delete request is made up
        UUID requestDeletionUuid = UUID.randomUUID();

        // WHEN delete for real
        assertThrows(HttpClientErrorException.class, () ->
                delete(referralViewModel, requestDeletionUuid, "am fine with this"));
    }

    @Test
    public void deleteReferralRevokeFailsIfNotCurrentlyRequested() {

        ReferralViewModel referralViewModel = createReferral("DeleteRevoke", "Test", "*********");

        // WHEN no request to delete is made

        // THEN a revoke request is made but there is no active request, it is rejected
        assertThrows(BadRequest.class, () ->
                deleteRequest(referralViewModel.serviceRecipientId, true));
    }

    @Test
    public void deleteReferralFailsWhenLatestRequestRevoked() {

        ReferralViewModel referralViewModel = createReferral("DeleteRevoke2", "Test", "*********");

        // WHEN delete request is made up (its currently valid to have a revoke on its own)
        UUID requestDeletionUuid = deleteRequest(referralViewModel.serviceRecipientId, false);
        // AND a subsequent request is made to revoke it
        deleteRequest(referralViewModel.serviceRecipientId, true);

        // THEN delete for real throws an error
        assertThrows(BadRequest.class, () ->
                delete(referralViewModel, requestDeletionUuid, "am fine with this"));
    }

    public void verifyDeleteCommand(int serviceRecipientId) {
        // NB We could use a view model on the receiving side of the request.
        // However, currently there is no property on the view model to indicate its a delete,
        // so we just inspect the json.
        // final ResponseEntity<BaseServiceRecipientCommandViewModel> response = getArchiveCommands(serviceRecipientId);
        ResponseEntity<String> jsonArchiveCommands = referralActor.getArchiveCommands(serviceRecipientId);
        String findDeleteCommand = "$..[?(@.commandName=='deleteSvcRec')]";
        final List<Map> archivedCommands = JsonPath.read(jsonArchiveCommands.getBody(), findDeleteCommand);
        assertThat("Expect to see delete command", archivedCommands, iterableWithSize(1));
        assertEquals("Expect to see same serviceRecipientId",
                Integer.parseInt(archivedCommands.get(0).get("serviceRecipientId").toString()), serviceRecipientId);
    }

    public UUID deleteRequest(int serviceRecipientId, boolean revoke) {
        DeleteRequestServiceRecipientCommandViewModel vm =
                new DeleteRequestServiceRecipientCommandViewModel(serviceRecipientId);
        vm.reason = "incorrect entry";
        vm.revoke = revoke;
        Result result = commandActor.executeCommand(vm).getBody();
        assertEquals("updated", result.getMessage());

        // retrieve delete request UUID
        List<DeleteRequestServiceRecipientCommandViewModel> cmdVms = serviceRecipientActor.findDeleteRequestCommands(serviceRecipientId).getBody();
        DeleteRequestServiceRecipientCommandViewModel latestDeleteCmdVm = cmdVms.stream().reduce((prev, curr) -> prev.timestamp.isAfter(curr.timestamp) ? prev : curr).get();
        return latestDeleteCmdVm.uuid;
    }

    private void delete(ReferralViewModel referralViewModel, UUID requestDeletionUuid, String reason) {
        String jsonViewModel = null;
        try {
            jsonViewModel = mapper.writeValueAsString(referralViewModel);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }

        final DeleteServiceRecipientCommandViewModel vm =
                new DeleteServiceRecipientCommandViewModel(referralViewModel.serviceRecipientId, requestDeletionUuid);
        vm.reason = reason;
        vm.jsonViewModel = jsonViewModel;
        Result result = commandActor.executeCommand(vm).getBody();
        assertEquals("deleted", result.getMessage());
        assertNull(result.getId());
    }

}
