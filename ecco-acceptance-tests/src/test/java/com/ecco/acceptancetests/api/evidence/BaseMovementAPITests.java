package com.ecco.acceptancetests.api.evidence;

import com.ecco.data.client.ServiceOptions;
import com.ecco.dom.EvidenceGroup;
import com.ecco.dom.ReportCriteriaDto;
import com.ecco.evidence.EvidenceTask;
import com.ecco.webApi.evidence.BaseWorkViewModel;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.lang3.ArrayUtils;
import org.hamcrest.Matcher;
import org.jspecify.annotations.NonNull;
import org.joda.time.DateTimeZone;
import org.joda.time.Duration;
import org.joda.time.Instant;
import org.joda.time.LocalDateTime;
import org.jspecify.annotations.Nullable;
import org.junit.jupiter.api.Test;

import java.util.*;
import java.util.stream.Collectors;

import static org.hamcrest.CoreMatchers.*;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.hasProperty;

/**
 * NB A baseline taken from QuestionnaireMovementAnswersAPITests.
 * Test for getting the values at points/snapshots in time.
 * NB We use the same referral for each test, which means one test can influence another.
 * To keep tests independent (where needed) we use different work items and different questions.
 */
public abstract class BaseMovementAPITests<T extends BaseWorkViewModel> extends BaseEvidenceCommandAPITests<T> {

    /**
     * Evidence of a snapshot answers per srId
     */
    @Data
    @Builder
    protected static class ResultsSnapshotViewModel {
        public EvidenceGroup evidenceGroupKey;
        public int serviceRecipientId;
        public List<ResultSnapshotViewModel> results;
    }
    @Data
    @Builder
    protected static class ResultSnapshotViewModel {
        public Long id;
        public Long defId;
        public LocalDateTime workDate;
        public String resultProperty;
    }

    static protected Boolean ensureDefs = Boolean.FALSE;
    static protected int[] defIds;

    protected static EvidenceGroup differentEvidenceGroup;
    protected static EvidenceTask differentEvidenceTask;

    public BaseMovementAPITests(EvidenceGroup evidenceGroup, EvidenceTask defaultEvidenceTask, ServiceOptions service) {
        super(evidenceGroup, defaultEvidenceTask, service);
    }

    protected abstract Matcher<ResultSnapshotViewModel> matcherForSomeProperties(long defId, int testIndex);
    protected abstract ResultsSnapshotViewModel[] runReportEarliestSnapshotInRange(ReportCriteriaDto reportCriteriaDto, int page);
    protected abstract ResultsSnapshotViewModel[] runReportLatestSnapshotBeforeRange(ReportCriteriaDto reportCriteriaDto, int page);
    protected abstract ResultsSnapshotViewModel[] runReportLatestSnapshotInRange(ReportCriteriaDto reportCriteriaDto, int page);
    protected abstract void createResult(UUID workUuid, @Nullable Instant timestampCreated, EvidenceGroup evidenceGroup, EvidenceTask defaultEvidenceTask, int defIdIndex, int testIndex);

    /**
     * top-level test wrapping a few methods to guarantee order
     * NB The tests use the same client, but different smartsteps. Therefore the queries returned will include
     * other actionDefId but we discount these by simply using an initial count and testing the specifics.
     * This test though also uses different date ranges so that we aren't affected by other tests in the same run
     * except method 'collectTheLastDataBeforeFrom' which loads all smartsteps prior to this date
     * - each run creates a new client/referral, and the results find the relevant serviceRecipient before returning the results
     */
    @Test
    public void happyPathExample() {
        // report dates are expected to be inclusive
        //  - from 20 days ago
        //  - to 10 days ago
        LocalDateTime relativeDate = LocalDateTime.now().plusDays(100);
        happyPathExample(relativeDate, 10);
    }

    private void happyPathExample(LocalDateTime relativeDate, int startIndex) {
        // discount results for this client from other tests ran
        LocalDateTime reportDateFrom = relativeDate.minusDays(20);
        LocalDateTime reportDateTo = relativeDate.minusDays(10);
        ReportCriteriaDto reportCriteriaDto = this.getReportCriteriaDto(reportDateFrom, reportDateTo);

        collectTheLastDataBeforeFrom(reportCriteriaDto, relativeDate, startIndex);
        collectTheFirstDataWithinFromTo(reportCriteriaDto, relativeDate, startIndex);
        collectTheLastDataWithinFromTo(reportCriteriaDto, relativeDate, startIndex);
    }

    /**
     * example of stage 1/3:
     *  - find the LATEST smartsteps up to the 'from' date, which will be:
     *  [10] = ALWAYSTRUE
     *  [12] = MISSING
     */
    private List<ResultSnapshotViewModel> collectTheLastDataBeforeFrom(ReportCriteriaDto reportCriteriaDto, LocalDateTime relativeDate, int startIndex) {

        // GIVEN some work in the past
        LocalDateTime workDateInPast = relativeDate.minusDays(50);

        // collect any values in this date range from other tests - where the questions ARE DIFFERENT
        // because they are still included in the query results
        // really this should be done for all tests, but this one gets caught out because we are repeated these methods
        long[] usedDefs = new long[] {defIds[startIndex], defIds[startIndex+2]};
        List<ResultSnapshotViewModel> vms = getLatestResultsBeforeRange(reportCriteriaDto);
        int initialResultCountAll = vms.size();
        int initialResultSameDefCount = vms
                .stream().filter(vm -> ArrayUtils.contains(usedDefs, vm.defId)).toArray().length;
        int initialResultsCount = initialResultCountAll - initialResultSameDefCount;
        {
            UUID uuid = UUID.randomUUID();
            sendCommentCommand(uuid, "this is my piece of work 7", workDateInPast);
            createResult(uuid, evidenceGroup, defaultEvidenceTask, startIndex, 0);
            createResult(uuid, evidenceGroup, defaultEvidenceTask, startIndex+2, 1);
        }

        // WHEN some work done more recently
        LocalDateTime workDateRecent = relativeDate.minusDays(30);
        {
            UUID uuid = UUID.randomUUID();
            sendCommentCommand(uuid, "this is my piece of work 8", workDateRecent);
            createResult(uuid, evidenceGroup, defaultEvidenceTask, startIndex, 2);
        }

        // THEN we still expect to see the latest ones before the 'from'
        {
            // dates are inclusive, (from is >=, to is < to.plusDays(1))
            // so this is: workDate < reportDateFrom
            List<ResultSnapshotViewModel> results = getLatestResultsBeforeRange(reportCriteriaDto);
            assertThat(results.size(), is(initialResultsCount + 2));
            assertThat(results, hasItem(matcherForSomeProperties(defIds[startIndex], 2, workDateRecent.withMillisOfSecond(0))));
            assertThat(results, hasItem(matcherForSomeProperties(defIds[startIndex+2], 1, workDateInPast.withMillisOfSecond(0))));
            return results;
        }

    }

    /**
     * example of stage 2/3:
     *  - find the FIRST results within the 'from' and 'to' dates, which will be:
     *  [11] = ALWAYSTRUE
     */
    private List<ResultSnapshotViewModel> collectTheFirstDataWithinFromTo(ReportCriteriaDto reportCriteriaDto, LocalDateTime relativeDate, int startIndex) {

        // GIVEN collectTheLastDataBeforeFrom()
        // collect any values in this date range from other tests - where the questions ARE DIFFERENT
        // because they are still included in the query results
        // really this should be done for all tests, but this one gets caught out because we are repeated these methods
        long[] usedDefs = new long[] {startIndex+1};
        List<ResultSnapshotViewModel> vms = getEarliestResultsInRange(reportCriteriaDto);
        int initialResultsCountAll = vms.size();
        int initialResultsSameDefsCount = vms
                .stream().filter(vm -> ArrayUtils.contains(usedDefs, vm.defId)).toArray().length;
        int initialResultsCount = initialResultsCountAll - initialResultsSameDefsCount;

        // WHEN put some work in between the from and to
        LocalDateTime workDateInBetween = relativeDate.minusDays(18); // 2 days inside 'from'
        {
            UUID uuid = UUID.randomUUID();
            sendCommentCommand(uuid, "this is my piece of work 9", workDateInBetween);
            createResult(uuid, evidenceGroup, defaultEvidenceTask, startIndex+1, 2);
        }

        // THEN we still expect to see it in the data set
        {
            // dates are inclusive, (from is >=, to is < to.plusDays(1))
            // so this is: workDate >= reportDateFrom and workDate <= reportDateTo
            List<ResultSnapshotViewModel> results = getEarliestResultsInRange(reportCriteriaDto);
            assertThat(results.size(), is(initialResultsCount + 1));
            assertThat(results, hasItem(matcherForSomeProperties(defIds[startIndex+1], 2)));
            return results;
        }

    }

    /**
     * example of stage 3/3:
     *  - find the LAST results within the 'from' and 'to' dates
     *  (this could produce the same data points as collectTheFirstDataWithinFromTo
     */
    private List<ResultSnapshotViewModel> collectTheLastDataWithinFromTo(ReportCriteriaDto reportCriteriaDto, LocalDateTime relativeDate, int startIndex) {

        // GIVEN collectTheFirstDataWithinFromTo
        // collect any values in this date range from other tests - where the questions ARE DIFFERENT
        // because they are still included in the query results
        // really this should be done for all tests, but this one gets caught out because the
        // preceeding method, collectTheFirstDataWithinFromTo, saves questionDefId 12 which is also saved here
        long[] usedDefs = new long[] {defIds[startIndex], defIds[startIndex+1],
                defIds[startIndex+2], defIds[startIndex+3]};
        List<ResultSnapshotViewModel> vms = getLatestResultsInRange(reportCriteriaDto);
        int initialResultsCountAll = vms.size();
        int initialResultsSameDefsCount = vms
                .stream().filter(vm -> ArrayUtils.contains(usedDefs, vm.defId)).toArray().length;
        int initialResultsCount = initialResultsCountAll - initialResultsSameDefsCount;

        // WHEN put some work just before end
        LocalDateTime workDateInBetween = relativeDate.minusDays(12); // 2 days inside 'to'
        {
            UUID uuid = UUID.randomUUID();
            sendCommentCommand(uuid, "this is my piece of work 10", workDateInBetween);
            createResult(uuid, evidenceGroup, defaultEvidenceTask, startIndex+3, 3);
            // we also change an existing answer to check we get it back
            createResult(uuid, evidenceGroup, defaultEvidenceTask, startIndex+1, 1);
        }

        // THEN we still expect to see the latest ones before the 'to'
        {
            // dates are inclusive, (from is >=, to is < to.plusDays(1))
            // so this is: workDate >= reportDateFrom and workDate <= reportDateTo
            List<ResultSnapshotViewModel> results = getLatestResultsInRange(reportCriteriaDto);
            assertThat(results.size(), is(initialResultsCount + 2));
            assertThat(results, hasItem(matcherForSomeProperties(defIds[startIndex+3], 3)));
            assertThat(results, hasItem(matcherForSomeProperties(defIds[startIndex+1], 1)));
            return results;
        }

    }

    /**
     * Here, we insert some results then some more with a later workDate, so that the getLatestResultsInRange
     * needs to get the latest by workDate - not just latest id
     */
    @Test
    public void canRetrieveLatestResults_mixedWorkDates() {

        // GIVEN
        LocalDateTime workDateInPast = LocalDateTime.now().minusDays(100);
        LocalDateTime workDateRecent = LocalDateTime.now().minusDays(80);
        LocalDateTime workDateBeyond = LocalDateTime.now();
        ReportCriteriaDto reportCriteriaDto = this.getReportCriteriaDto(workDateInPast, workDateRecent);
        int initialResultsCount = getLatestResultsInRange(reportCriteriaDto).size();
        {
            UUID uuid = UUID.randomUUID();
            sendCommentCommand(uuid, "this is my piece of work 40", workDateInPast);
            createResult(uuid, evidenceGroup, defaultEvidenceTask, 7, 0);
            createResult(uuid, evidenceGroup, defaultEvidenceTask, 8, 0);
            createResult(uuid, evidenceGroup, defaultEvidenceTask, 9, 1);
        }

        // WHEN enter some after the reporting range
        {
            // beyond the reporting period
            UUID uuid = UUID.randomUUID();
            sendCommentCommand(uuid, "this is my piece of work 41", workDateBeyond);
            createResult(uuid, evidenceGroup, defaultEvidenceTask, 7, 2);
            createResult(uuid, evidenceGroup, defaultEvidenceTask, 8, 2);
            createResult(uuid, evidenceGroup, defaultEvidenceTask, 9, 0);
        }

        // THEN we expect the latest to get the workDateInPast
        {
            List<ResultSnapshotViewModel> results = getLatestResultsInRange(reportCriteriaDto);
            assertThat(results.size(), is(initialResultsCount + 3));
            assertThat(results, hasItem(matcherForSomeProperties(defIds[7], 0)));
            assertThat(results, hasItem(matcherForSomeProperties(defIds[8], 0)));
            assertThat(results, hasItem(matcherForSomeProperties(defIds[9], 1)));
        }

    }


    /**
     * Here, we insert some results after the reporting range, so we can
     * test the sub query being just defId and serviceRecipientId
     */
    @Test
    public void cannotRetrieveLatestResults_notOutsideWorkDates() {

        // GIVEN
        LocalDateTime workDateInPast = LocalDateTime.now().minusDays(100);
        LocalDateTime workDateRecent = LocalDateTime.now().minusDays(80);
        LocalDateTime workDateBeyond = LocalDateTime.now();
        ReportCriteriaDto reportCriteriaDto = this.getReportCriteriaDto(workDateInPast, workDateRecent);
        int initialResultsCount = getLatestResultsInRange(reportCriteriaDto).size();
        {
            UUID uuid = UUID.randomUUID();
            sendCommentCommand(uuid, "this is my piece of work 30", workDateRecent);
            createResult(uuid, evidenceGroup, defaultEvidenceTask, 16, 0);
            createResult(uuid, evidenceGroup, defaultEvidenceTask, 17, 0);
            createResult(uuid, evidenceGroup, defaultEvidenceTask, 18, 1);

            // after reporting range
            uuid = UUID.randomUUID();
            sendCommentCommand(uuid, "this is my piece of work 32", workDateBeyond);
            createResult(uuid, evidenceGroup, defaultEvidenceTask, 16, 0);
            createResult(uuid, evidenceGroup, defaultEvidenceTask, 17, 0);
            createResult(uuid, evidenceGroup, defaultEvidenceTask, 18, 1);
        }

        // WHEN enter some later that are actually before
        {
            UUID uuid = UUID.randomUUID();
            sendCommentCommand(uuid, "this is my piece of work 33", workDateInPast);
            createResult(uuid, evidenceGroup, defaultEvidenceTask, 16, 2);
            createResult(uuid, evidenceGroup, defaultEvidenceTask, 17, 2);
            createResult(uuid, evidenceGroup, defaultEvidenceTask, 18, 0);
        }

        // THEN we expect the latest to get the workDateRecent
        {
            List<ResultSnapshotViewModel> results = getLatestResultsInRange(reportCriteriaDto);
            assertThat(results.size(), is(initialResultsCount + 3));
            assertThat(results, hasItem(matcherForSomeProperties(defIds[16], 0)));
            assertThat(results, hasItem(matcherForSomeProperties(defIds[17], 0)));
            assertThat(results, hasItem(matcherForSomeProperties(defIds[18], 1)));
        }

    }

    @Test
    public void canRetrieveLatestResults_notAcrossServiceRecipients() {
        // this can be achieved by performing a test on the current serviceRecipientId
        // then changing the serviceRecipientId, then performing the same test again
        // because then the exact same data exists on another serviceRecipient
        happyPathExample(LocalDateTime.now().plusDays(100), 19); // use a new defId set so it doesn't conflict with the existing test case
        clearReferral();
        createUniqueClientAndReferral();
        happyPathExample(LocalDateTime.now().plusDays(100), 19);
        // verify there is nothing in another serviceRecipientId
        // NB can't do this as the view model is very limited
        //List<QuestionAnswerSnapshotViewModel> results = getLatestResultsInRange(null, LocalDateTime.now().plusDays(100));
        //assertThat(results, !hasItem(Matchers.<QuestionAnswerSnapshotViewModel>hasProperty("workDate", equalTo(LocalDateTime.now())),));
    }

    // NB we can't really test 'AcrossServices' as this is equivalent to 'AcrossServiceRecipients'

    @Test
    public void canRetrieveLatestResults_notAcrossEvidenceGroups() {
        // this can be achieved by performing a test on the current serviceRecipientId
        // then changing the evidenceGroup, then performing the same test again
        // because then the exact same data exists on another evidenceGroup
        happyPathExample(LocalDateTime.now().plusDays(100), 19); // use the same questionDefId since we should now be able to repeat it
        this.evidenceGroup = differentEvidenceGroup;
        this.defaultEvidenceTask = differentEvidenceTask;
        happyPathExample(LocalDateTime.now().minusDays(100), 19);
        // verify there is nothing above 'now' - which would be the previous evidenceGroup
        // NB can't do this as the view model is very limited
        //List<QuestionAnswerSnapshotViewModel> results = getLatestResultsInRange(null, LocalDateTime.now().plusDays(100));
        //assertThat(results, !hasItem(Matchers.<QuestionAnswerSnapshotViewModel>hasProperty("workDate", equalTo(LocalDateTime.now())),));
    }

    @Test
    public void canRetrieveSingleWorkSingleAnswer_def0() {
        // GIVEN
        ReportCriteriaDto reportCriteriaDto = this.getReportCriteriaDto(workDateUpTo50DaysInPast, workDateUpTo50DaysInPast);
        int initialResultsCount = getLatestResultsInRange(reportCriteriaDto).size();

        // WHEN
        {
            UUID uuid = UUID.randomUUID();
            sendCommentCommand(uuid, "this is my piece of work 1", workDateUpTo50DaysInPast);
            createResult(uuid, evidenceGroup, defaultEvidenceTask, 0, 2);
        }

        // THEN
        {
            List<ResultSnapshotViewModel> results = getLatestResultsInRange(reportCriteriaDto);
            assertThat(results.size(), is(initialResultsCount + 1));
            assertThat(results, hasItem(matcherForSomeProperties(defIds[0], 2)));
        }

    }

    @Test
    public void canRetrieveSingleWorkManyResults_def123() {
        // GIVEN
        ReportCriteriaDto reportCriteriaDto = this.getReportCriteriaDto(workDateUpTo50DaysInPast, workDateUpTo50DaysInPast);
        int initialResultsCount = getLatestResultsInRange(reportCriteriaDto).size();

        // WHEN
        {
            UUID uuid = UUID.randomUUID();
            sendCommentCommand(uuid, "this is my piece of work 2", workDateUpTo50DaysInPast);
            createResult(uuid, evidenceGroup, defaultEvidenceTask, 1, 2);
            createResult(uuid, evidenceGroup, defaultEvidenceTask, 2, 3);
            createResult(uuid, evidenceGroup, defaultEvidenceTask, 3, 0);
        }

        // THEN
        {
            List<ResultSnapshotViewModel> results = getLatestResultsInRange(reportCriteriaDto);
            assertThat(results.size(), is(initialResultsCount + 3));
            assertThat(results, hasItem(matcherForSomeProperties(defIds[1], 2)));
            assertThat(results, hasItem(matcherForSomeProperties(defIds[2], 3)));
            assertThat(results, hasItem(matcherForSomeProperties(defIds[3], 0)));
        }

    }

    @Test
    public void canRetrieveManyWorkManyResults_def456() {
        // GIVEN
        ReportCriteriaDto reportCriteriaDto = this.getReportCriteriaDto(workDateUpTo50DaysInPast, workDateUpTo50DaysInPast);
        int initialResultsCount = getLatestResultsInRange(reportCriteriaDto).size();

        // WHEN
        {
            UUID uuid = UUID.randomUUID();
            sendCommentCommand(uuid, "this is my piece of work 3", workDateUpTo50DaysInPast);
            createResult(uuid, evidenceGroup, defaultEvidenceTask, 4, 2);
            createResult(uuid, evidenceGroup, defaultEvidenceTask, 5, 3);

            uuid = UUID.randomUUID();
            sendCommentCommand(uuid, "this is my piece of work 4", workDateUpTo50DaysInPast);
            createResult(uuid, evidenceGroup, defaultEvidenceTask, 6, 0);
        }

        // THEN
        {
            List<ResultSnapshotViewModel> results = getLatestResultsInRange(reportCriteriaDto);
            assertThat(results.size(), is(initialResultsCount + 3));
            assertThat(results, hasItem(matcherForSomeProperties(defIds[4], 2)));
            assertThat(results, hasItem(matcherForSomeProperties(defIds[5], 3)));
            assertThat(results, hasItem(matcherForSomeProperties(defIds[6], 0)));
        }

    }


    @Test
    public void canRetrieveLatestResults_def789() {
        // GIVEN
        LocalDateTime workDateInPast = LocalDateTime.now().minusDays(50);
        LocalDateTime workDateInMiddle = LocalDateTime.now().minusDays(40);
        LocalDateTime workDateRecent = LocalDateTime.now().minusDays(30);
        ReportCriteriaDto reportCriteriaDto = this.getReportCriteriaDto(workDateInPast, workDateRecent);

        int initialResultsCount = getLatestResultsInRange(reportCriteriaDto).size();

        // WHEN
        {
            UUID uuid = UUID.randomUUID();
            sendCommentCommand(uuid, "this is my piece of work 5", workDateInPast);
            createResult(uuid, evidenceGroup, defaultEvidenceTask, 7, 0);
            createResult(uuid, evidenceGroup, defaultEvidenceTask, 8, 0);
            createResult(uuid, evidenceGroup, defaultEvidenceTask, 9, 1);

            // we need to change the timestamp, else the ms become the same and the test fails
            // the same ms is all but impossible from using the screens
            uuid = UUID.randomUUID();
            Instant timestampCreated = new Instant(workDateInMiddle.toDateTime(DateTimeZone.UTC)).plus(Duration.standardMinutes(10));
            sendCommentCommand(uuid, "this is my piece of work 6", timestampCreated, workDateRecent);
            createResult(uuid, timestampCreated, evidenceGroup, defaultEvidenceTask, 7, 1);
            createResult(uuid, timestampCreated, evidenceGroup, defaultEvidenceTask, 8, 1);
            createResult(uuid, timestampCreated, evidenceGroup, defaultEvidenceTask, 9, 1);

            // same work date - get the latest
            uuid = UUID.randomUUID();
            timestampCreated = new Instant(workDateInMiddle.toDateTime(DateTimeZone.UTC)).plus(Duration.standardMinutes(20));
            sendCommentCommand(uuid, "this is my piece of work 7", timestampCreated, workDateRecent);
            createResult(uuid, timestampCreated, evidenceGroup, defaultEvidenceTask, 7, 2);
            createResult(uuid, timestampCreated, evidenceGroup, defaultEvidenceTask, 8, 2);
            createResult(uuid, timestampCreated, evidenceGroup, defaultEvidenceTask, 9, 2);
        }

        // THEN
        {
            List<ResultSnapshotViewModel> results = getLatestResultsInRange(reportCriteriaDto);
            assertThat(results.size(), is(initialResultsCount + 3));
            assertThat(results, hasItem(matcherForSomeProperties(defIds[7], 2)));
            assertThat(results, hasItem(matcherForSomeProperties(defIds[8], 2)));
            assertThat(results, hasItem(matcherForSomeProperties(defIds[9], 2)));
        }

    }

    @Test
    public void canRetrieveEarliestResults_def1314() {

        // GIVEN some work in the past
        LocalDateTime workDateEarlier = LocalDateTime.now().minusDays(50);
        LocalDateTime workDateRecent = LocalDateTime.now().minusDays(40);
        ReportCriteriaDto reportCriteriaDto = this.getReportCriteriaDto(workDateEarlier, workDateRecent.minusDays(1));

        // get the earliest in a date range, because that's what we need to do in 'happyPath'
        int initialResultsCount = getEarliestResultsInRange(reportCriteriaDto).size();
        {
            UUID uuid = UUID.randomUUID();
            sendCommentCommand(uuid, "this is my piece of work 20", workDateEarlier);
            createResult(uuid, evidenceGroup, defaultEvidenceTask, 14, 0);
            createResult(uuid, evidenceGroup, defaultEvidenceTask, 15, 1);
        }

        // WHEN some more recent work
        {
            UUID uuid = UUID.randomUUID();
            sendCommentCommand(uuid, "this is my piece of work 21", workDateRecent);
            createResult(uuid, evidenceGroup, defaultEvidenceTask, 14, 2);
            createResult(uuid, evidenceGroup, defaultEvidenceTask, 15, 2);
        }

        // THEN get the earlier work
        {
            List<ResultSnapshotViewModel> results = getEarliestResultsInRange(reportCriteriaDto);
            assertThat(results.size(), is(initialResultsCount + 2));
            assertThat(results, hasItem(matcherForSomeProperties(defIds[14], 0)));
            assertThat(results, hasItem(matcherForSomeProperties(defIds[15], 1)));
        }

    }

    private enum SnapshotTime {
        EARLIEST, BEFORE, LATEST
    }

    /**
     * Dates are inclusive, since they work their way to PredicateSupport.applyLocalDateRange
     */
    private List<ResultSnapshotViewModel> getLatestResultsBeforeRange(ReportCriteriaDto reportCriteriaDto) {
        return getAllPagedResults(reportCriteriaDto, SnapshotTime.BEFORE);
    }
    private List<ResultSnapshotViewModel> getLatestResultsInRange(ReportCriteriaDto reportCriteriaDto) {
        return getAllPagedResults(reportCriteriaDto, SnapshotTime.LATEST);
    }

    private List<ResultSnapshotViewModel> getEarliestResultsInRange(ReportCriteriaDto reportCriteriaDto) {
        return getAllPagedResults(reportCriteriaDto, SnapshotTime.EARLIEST);
    }

    @NonNull
    private List<ResultSnapshotViewModel> getAllPagedResults(ReportCriteriaDto reportCriteriaDto, SnapshotTime snapshotTime) {
        List<ResultsSnapshotViewModel> results = new ArrayList<>();
        int page = 0;
        boolean recordsLeft = true;
        while (recordsLeft) {
            ResultsSnapshotViewModel[] resultsPage = new ResultsSnapshotViewModel[]{};
            switch (snapshotTime) {
                case EARLIEST:
                    resultsPage = this.runReportEarliestSnapshotInRange(reportCriteriaDto, page);
                    break;
                case BEFORE:
                    resultsPage = this.runReportLatestSnapshotBeforeRange(reportCriteriaDto, page);
                    break;
                case LATEST:
                    resultsPage = this.runReportLatestSnapshotInRange(reportCriteriaDto, page);
                    break;
            }
            results.addAll(Arrays.asList(resultsPage));
            recordsLeft = resultsPage.length > 0;
            page++;
        }

        return results.stream()
                .filter(qas -> qas.serviceRecipientId == getServiceRecipientId())
                .map(snapshot -> snapshot.results)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
    }

    protected Matcher<ResultSnapshotViewModel> matcherForSomeProperties(long defId, int testIndex, LocalDateTime workDate) {
        return allOf(matcherForSomeProperties(defId, testIndex),
                hasProperty("workDate", equalTo(workDate)));
    }


    private ReportCriteriaDto getReportCriteriaDto(LocalDateTime from, LocalDateTime to) {
        ReportCriteriaDto dto = new ReportCriteriaDto();
        dto.setFrom(from == null ? null : from.toLocalDate().toString());
        dto.setTo(to == null ? null : to.toLocalDate().toString()); // inclusive
        dto.setSupportEvidenceGroup(this.evidenceGroup.getName());
        dto.setQuestionnaireEvidenceGroup(this.evidenceGroup.getName());
        dto.setReferralStatus(null);
        return dto;
    }

    private void createResult(UUID workUuid, EvidenceGroup evidenceGroup, EvidenceTask defaultEvidenceTask, int defId, int testIndex) {
        createResult(workUuid, null, evidenceGroup, defaultEvidenceTask, defId, testIndex);
    }

}
