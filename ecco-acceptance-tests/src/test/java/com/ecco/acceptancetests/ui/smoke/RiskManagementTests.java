package com.ecco.acceptancetests.ui.smoke;

import com.ecco.acceptancetests.expectations.RiskManagementExpectation;
import com.ecco.acceptancetests.givens.RiskManagementGiven;
import com.ecco.acceptancetests.steps.RiskManagementContext;
import com.ecco.acceptancetests.ui.BaseSeleniumTest;
import com.ecco.acceptancetests.ui.pages.supportplan.RiskManagementBasePage;
import com.ecco.data.client.ReferralOptions;
import com.ecco.data.client.ServiceOptions;
import com.ecco.webApi.evidence.ReferralViewModel;

import org.joda.time.DateTime;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.test.annotation.IfProfileValue;

import java.util.Date;

public class RiskManagementTests extends BaseSeleniumTest {

    private final ReferralViewModel referral = new ReferralViewModel(); // This will be replaced by 'magic'
    private RiskManagementGiven given;
    private RiskManagementContext when;
    private RiskManagementExpectation expect;

    @BeforeEach
    public void setUp() {
        given = riskManagementSteps.given(this);
        expect = riskManagementSteps.expect();
        when = given.when();
    }

    @Test
    public void userCanCreateARiskManagement() {

        login("sysadmin");

        // given a referral exists for james blunt
        given.ensureReferral("RMT01", "James", "Blunt", ServiceOptions.DEMO_ALL, ReferralOptions.DEMO_ALL).as("referral");

        // when i open a risk management and set some sensible data and then save it
        when
            .openRiskManagement(referral.getClientFirstName(), referral.getClientLastName(), referral.getReferralCode(), ServiceOptions.DEMO_ALL)
            .setDate(new Date())
            .setComment("risk management comment")
            .changeTab(RiskManagementBasePage.TAB_OUTCOME_RTS)
            .save();

        // expect the trigger and control to be saved
        expect.onRiskHistoryTab()
                .canSeeText("risk management comment");

        logout();
    }

    @Test
    @IfProfileValue(name="test.category", values={"all","quarantine"})
    // TODO: fails on because service does not appear to be configured as expected (moved config -> SQL ??)  Tab is also blank
    public void userCanCreateARiskManagement_withTriggerControl() {

        login("sysadmin");

        // given a referral exists for james blunt
        given.ensureReferral("RMTC01", "James", "Blunter", ServiceOptions.DEMO_ALL, ReferralOptions.DEMO_ALL).as("referral");

        // when i open a risk management and set some sensible data and then save it
        when
            .openRiskManagement(referral.getClientFirstName(), referral.getClientLastName(), referral.getReferralCode(), ServiceOptions.DEMO_ALL)
            .setDate(new Date())
            .setComment("risk management t/c")
            .changeTab(RiskManagementBasePage.TAB_OUTCOME_RTS)
            .setTrigger("trigger1")
            .setControl("control1")
            .save();

        // I expect to be back on the referral overview and to find the text on the risk history tab
        expect.canSeeText("risk management required");

        // expect the trigger and control to be saved
        expect.onRiskHistoryTab()
                .canSeeText("trigger: trigger1")
                .canSeeText("control: control1");

        logout();
    }

    @Test
    public void userCanCreateARiskManagement_withOutcomeLevelAmber() {

        login("sysadmin");

        // given a referral exists for james blunt
        given.ensureReferral("RUB01", "Adrian", "Gerrol", ServiceOptions.ACCOMMODATION, ReferralOptions.ACCOMMODATION).as("referral");

        // when i open a risk management and set some sensible data and then save it
        when
            .openRiskManagement(referral.getClientFirstName(), referral.getClientLastName(), referral.getReferralCode(), ServiceOptions.ACCOMMODATION)
            .setDate(new Date())
            .setComment("risk management ola")
            .changeTab(RiskManagementBasePage.TAB_OUTCOME_RTP) // just to move around tabs
            .changeTab(RiskManagementBasePage.TAB_OUTCOME_RTS)
            .setOutcomeLevelAmber()
            .save();

        // i expect the risk management to be saved successfully
        expect.onRiskHistoryTab()
                .canSeeText("risk management ola");

        logout();
    }

    @Test
    public void cannotCreateAnEntryWithoutADate() {

        login("sysadmin");

        // given a referral exists for james blunt
        given.ensureReferral("RMT01", "James", "Blunt", ServiceOptions.DEMO_ALL, ReferralOptions.DEMO_ALL).as("referral");

        // when i try to create a risk management without a date
        when
            .openRiskManagement(referral.getClientFirstName(), referral.getClientLastName(), referral.getReferralCode(), ServiceOptions.DEMO_ALL)
//            .setDate(null) // TODO: implement clearDate() ?
            .setComment("dodgy comment with no work date");

        // i expect to be told enter a date
        expect.fieldHasValidationErrorByLabel("took place on", "is required");

        logout();
    }

    @Test
    public void cannotCreateAnEntryWithoutAComment() {

        login("sysadmin");

        // given a referral exists for james blunt
        given.ensureReferral("RMT01", "James", "Blunt", ServiceOptions.DEMO_ALL, ReferralOptions.DEMO_ALL).as("referral");

        // when i try to create a risk management without a comment
        when.openRiskManagement(referral.getClientFirstName(), referral.getClientLastName(), referral.getReferralCode(), ServiceOptions.DEMO_ALL)
            .setDate(new Date());

        // i expect to be told to enter a comment
        expect.autoSavefieldHasValidationErrorByName("comment", "is required");

        logout();
    }


    @Test
    public void cannotCreateAnEntryWithAFutureDate() {

        login("sysadmin");

        // given a referral exists for james blunt
        given.ensureReferral("RMT01", "James", "Blunt", ServiceOptions.DEMO_ALL, ReferralOptions.DEMO_ALL).as("referral");

        // when i try to create a risk management with a future date
        when.openRiskManagement(referral.getClientFirstName(), referral.getClientLastName(), referral.getReferralCode(), ServiceOptions.DEMO_ALL)
            .setDate(new DateTime().plusDays(2).toDate())
            .setComment("future work date");

        // i expect to be told this is not allowed, but i am not told why.
        expect.fieldHasValidationErrorByLabel("took place on", "cannot be future dated");

        logout();
    }
}
