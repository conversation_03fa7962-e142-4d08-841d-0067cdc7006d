package com.ecco.acceptancetests.expectations;

import com.ecco.acceptancetests.ui.pages.referral.TaskDefinitionsService1;
import com.ecco.acceptancetests.ui.steps.webdriver.WebDriverUI;


public class BaseEvidenceExpectation<T extends BaseEvidenceExpectation, R extends WebDriverUI> {
    private WebDriverUI webDriverUI;

    public BaseEvidenceExpectation(WebDriverUI webDriverUI) {

        this.webDriverUI = webDriverUI;
    }

    public T onSupportHistoryTab() {
        TaskDefinitionsService1 referralPage = new TaskDefinitionsService1(webDriverUI.getWebDriver());
        referralPage.selectSupportHistoryTab();
        return (T) this;
    }

    public T onRiskHistoryTab() {
        TaskDefinitionsService1 referralPage = new TaskDefinitionsService1(webDriverUI.getWebDriver());
        referralPage.selectRiskHistoryTab();
        return (T) this;
    }

    @SuppressWarnings("unchecked")
    public T canSeeText(String text) {
        webDriverUI.checkCanSeeText(text);
        return (T) this;
    }

    // it=document.evaluate("//div[child::label/span[contains(text(),'took place')]]/span[text()='is required']", document)
    // it.iterateNext()
    public void fieldHasValidationErrorByLabel(String fieldName, String errorText) {
        webDriverUI.checkCanSeeTextAtXpath(errorText, "//div[child::label/span[contains(text(),'"+ fieldName
                + "')]]/span");
    }

    public void fieldHasValidationErrorByName(String fieldName, String errorText) {
        webDriverUI.checkCanSeeTextAtXpath(errorText, "//*[@name='"+ fieldName + "']/../span");
    }
    public void autoSavefieldHasValidationErrorByName(String fieldName, String errorText) {
        webDriverUI.checkCanSeeTextAtXpath(errorText, "//*[@name='"+ fieldName + "']/../../span");
    }
}
