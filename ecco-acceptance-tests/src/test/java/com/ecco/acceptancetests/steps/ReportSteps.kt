package com.ecco.acceptancetests.steps

import org.joda.time.DateTime
import org.joda.time.format.DateTimeFormat

interface ReportSteps : CommonSteps {
    class ReportOptions {
        internal var fmt = DateTimeFormat.forPattern("dd/MM/yyyy")

        private var serviceName: String? = null
        internal var from: DateTime? = null
        internal var to: DateTime? = null

        private fun fmtDate(dte: DateTime?): String = if (dte == null) "" else dte.toString(fmt)

        fun usingService(serviceName: String): ReportOptions {
            this.serviceName = serviceName
            return this
        }

        fun serviceName(): String? = serviceName

        fun usingFrom(from: DateTime): ReportOptions {
            this.from = from
            return this
        }

        fun from(): DateTime? = from

        fun fromStr(): String = fmtDate(from)

        fun usingTo(to: DateTime): ReportOptions {
            this.to = to
            return this
        }

        fun to(): DateTime? = to

        fun toStr(): String = fmtDate(to)
    }
}