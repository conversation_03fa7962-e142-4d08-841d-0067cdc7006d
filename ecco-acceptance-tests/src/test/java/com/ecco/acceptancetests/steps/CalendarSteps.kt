package com.ecco.acceptancetests.steps

/**
 * Calendar operations to be carried out by a user.
 */
interface CalendarSteps : CommonSteps {
    fun createCalendarEventForDay(description: String, daysInFuture: Int)

    /** Given the screen is already on the calendar, add an event with optional extra attendees.  */
    fun addCalendarEventForTomorrowWithAttendees(description: String, vararg attendees: String)
}