package com.ecco.acceptancetests.steps

import com.ecco.acceptancetests.expectations.GroupSupportExpectation
import com.ecco.acceptancetests.givens.GroupSupportGiven
import com.ecco.acceptancetests.ui.pages.GroupSupportActivityPage
import com.ecco.acceptancetests.ui.pages.GroupSupportListPage
import com.ecco.acceptancetests.ui.smoke.GroupSupportTests

interface GroupSupportContext : CommonSteps {
    fun given(testClass: GroupSupportTests): GroupSupportGiven

    fun openGroupSupportList(): GroupSupportListPage

    fun openFirstGroupSupportActivity(): GroupSupportActivityPage

    fun clickClientsTab(): GroupSupportActivityPage

    fun expect(): GroupSupportExpectation
}