package com.ecco.acceptancetests.steps

import com.ecco.dom.agreements.DaysOfWeek
import com.ecco.webApi.buildings.FixedContainerViewModel
import com.ecco.webApi.viewModels.Result
import java.time.LocalDate
import java.time.LocalDateTime

interface RotaSteps {
    class WorkerResult(workerId: Long, name: String, workerJobId: Int, calendarId: String, serviceRecipientId: Int) {
        var workerId: Long? = workerId
        var name: String? = name
        var workerJobId: Int? = workerJobId
        var calendarId: String? = calendarId
        var serviceRecipientId: Int? = serviceRecipientId
    }

    /**
     * Creates a worker unique to the running test.
     * firstName and lastName are also derived.
     * @return the referralId and full name in which the referral was created
     */
    fun createWorkerAndJob(username: String): WorkerResult

    fun setWorkerPrimaryLocation(workerId: Long, workerJobId: Int, primaryLocationId: Int)

    /**
     * @return the referralId and full name in which the referral was created
     */
    fun createClientUnfulfilledAdhocAppointmentDemandCreateFirstAgreement(
        appointmentTypeName: String,
        time: LocalDateTime,
        srId: Integer?,
    ): Pair<Long, String>

    fun createClientUnfulfilledAdhocAppointmentDemandCreateFirstAgreement(
        appointmentTypeName: String,
        referralResult: Pair<Long, String>,
        time: LocalDateTime,
        srId: Int?,
    ): Long

    fun createWorkerAndJobWithAvailabilityForToday(username: String, workerPrimaryLocationBuildingId: Int?): WorkerResult

    fun createWorkerAndJobWithAvailabilityForToday(
        username: String,
        firstName: String,
        lastName: String,
        workerPrimaryLocationBuildingId: Int?,
    ): String

    fun checkCanAssignResourceToFirstAppointmentToday(
        resourceFilter: String,
        serviceRecipientDemandFilter: String,
        recipientName: String,
        resourceName: String,
    )

    fun checkCanAssignResourceToSingleAppointmentAtTime(
        resourceFilter: String,
        serviceRecipientDemandFilter: String,
        recipientName: String,
        resourceName: String,
        time: LocalDateTime,
    )

    fun checkCanUnAssignResourceFromSingleAppointmentAtTime(
        resourceFilter: String,
        serviceRecipientDemandFilter: String,
        recipientName: String,
        resourceName: String,
        time: LocalDateTime,
    )

    fun checkCanAssignResourceToRecurringAppointmentAtTime(
        resourceFilter: String,
        serviceRecipientDemandFilter: String,
        recipientName: String,
        resourceName: String,
        time: LocalDateTime,
        daysOfWeek: DaysOfWeek,
        recurringEnd: LocalDate,
    )

    fun checkCanUnAssignResourceFromRecurringAppointmentAtTime(
        resourceFilter: String,
        serviceRecipientDemandFilter: String,
        recipientName: String,
        resourceName: String,
        time: LocalDateTime,
        daysOfWeek: DaysOfWeek,
        recurringEnd: LocalDate,
    )

    fun checkCanCreateAdHocAppointmentOnFirstAgreementAtTime(
        serviceRecipientName: String,
        serviceRecipientId: Int,
        time: LocalDateTime?,
        resourceSrId: Int? = null,
    ): Result

    fun checkCanCreateScheduleOnFirstAgreement(
        serviceRecipientName: String,
        serviceRecipientId: Int,
        startDateTime: LocalDateTime?,
        daysOfWeek: DaysOfWeek?,
        endDate: LocalDate?,
        additionalStaff: Int,
        resourceSrId: Int? = null,
    ): Result

    fun checkClientReferralIsAvailableOffline(clientName: String)

    fun createBuilding(buildingName: String): FixedContainerViewModel

    fun createBuilding(buildingName: String, parentBuilding: FixedContainerViewModel): FixedContainerViewModel

    fun createCareRun(careRunName: String, parentBuilding: FixedContainerViewModel): FixedContainerViewModel

    fun createBuildingAgreement(building: FixedContainerViewModel)

    fun createAppointmentTypeForBuilding(appointmentTypeName: String)

    fun checkCanCreateBuildingUnfulfilledAdHocAppointmentDemandOnFirstAgreement(building: FixedContainerViewModel, time: LocalDateTime)

    fun checkCanCreateBuildingUnfulfilledAdHocAppointmentDemandOnFirstAgreementOfTypeAtTime(
        building: FixedContainerViewModel,
        appointmentTypeName: String,
        time: LocalDateTime,
    )

    fun createBuildingAvailabilityFromToday(buildingId: Int)
}