package com.ecco.acceptancetests.steps

import com.ecco.acceptancetests.ui.pages.Role
import com.ecco.webApi.contacts.IndividualUserSummaryViewModel

interface UserManagementSteps : CommonSteps {
    /**
     * Creates a user which is unique to the test being run
     * Returns the created username
     */
    fun createUser(username: String, vararg group: Role): String

    fun createIndividualWithUser(
        username: String,
        newPassword: String,
        firstName: String,
        lastName: String,
        vararg groups: Role,
    ): IndividualUserSummaryViewModel

    /**
     * Creates a user which is system wide (e.g. staff, admin, worker) if they didn't already exist
     * <br></br>The password will be the same as the username.
     */
    fun createSharedUser(username: String, vararg group: Role)

    fun listUsers(): List<String>

    fun listUsers(initial: String, groupName: String, enabledOnly: Boolean): List<String>

    fun listUserGroups(): List<String>
}