package com.ecco.acceptancetests.steps

import com.ecco.data.client.ReferralOptions
import com.ecco.data.client.ServiceOptions
import com.ecco.webApi.evidence.ReferralViewModel
import org.joda.time.LocalDate

interface ReferralSteps {
    /**
     * Creates a referral unique to the running test.
     * firstName and lastName must be entered, and at least one of niNumber and nhsNumber.
     * If a serviceName is entered it will be chosen for the referral, else the 'any service' checkbox will be selected
     * @return the referralId and full name in which the referral was created
     */
    fun processReferral(
        referralOptions: ReferralOptions,
        service: ServiceOptions,
        firstName: String,
        lastName: String,
        niNumber: String,
        referralComment: String,
    ): Pair<Long, String>

    /**
     * @see .processReferral
     * @return the id of the referral
     */
    fun processReferralToId(
        referralOptions: ReferralOptions,
        service: ServiceOptions,
        firstName: String,
        lastName: String,
        niNumber: String,
        referralComment: String,
    ): Long

    /**
     * Given the screen is already on a referral, process the aspects
     */
    fun processTaskDefinitions(referralOptions: ReferralOptions, service: ServiceOptions)

    /**
     * Add a referral without processing all the referral aspects
     * @param unique Ensure this referral gets created by unique-ifying the client names
     * @param existingClientExpected we are expecting to find an existing client (fails if not)
     * @param useExistingClient when an existing client is found, indicate if we want to use it
     * @param expectedReferralClientName the name expected to appear on the referral
     */
    fun createReferral(
        unique: Boolean,
        existingClientExpected: Boolean,
        useExistingClient: Boolean,
        expectedReferralClientName: String,
        options: ReferralOptions,
        serviceName: ServiceOptions,
        firstName: String,
        lastName: String,
        dob: LocalDate,
        niNumber: String,
        gender: String,
        language: String,
        ethnicity: String,
    ): ReferralViewModel

    /**
     * Find a referral, referals -> find -> click
     */
    fun findClientThenReferral(clientCode: String, firstNamePrefix: String, lastNamePrefix: String, referralCode: String)

    /**
     * @return list of referrals by displayName (firstname space last name) NOT as listed on listReferrals
     */
    fun listReferrals(): List<String>

    /**
     * Generates a referral overview that is suitable for printing
     */
    fun printReferralOverview(firstName: String, lastName: String)

    // void closeSupportPlan(String firstName, String lastName, String reason);

    // /**
    //  * After we've gone through the 'close Support Plan' flow, it should be listed under referrals
    //  * as closed, with the correct reason displayed.
    //  * This method will check the referral is listed as "exited", and return the given reason
    //  */
    // String checkSupportPlanIsClosed(String firstName, String lastName);

    /** Given the screen is already on a referral, navigate to the calendar appointments.  */
    fun navigateToAppointments(firstName: String, lastName: String)

    fun exited(firstName: String, lastName: String, exitedDate: LocalDate)
}