package com.ecco.acceptancetests.steps

import com.ecco.acceptancetests.expectations.RiskManagementExpectation
import com.ecco.acceptancetests.fixtures.RiskManagementFixture
import com.ecco.acceptancetests.givens.RiskManagementGiven
import com.ecco.acceptancetests.ui.smoke.RiskManagementTests
import com.ecco.data.client.ServiceOptions

interface RiskManagementContext : CommonSteps {
    fun given(testClass: RiskManagementTests): RiskManagementGiven

    fun openRiskManagement(firstName: String, lastName: String, referralCode: String, serviceName: ServiceOptions): RiskManagementFixture

    fun expect(): RiskManagementExpectation
}