package com.ecco.acceptancetests.steps

import com.ecco.acceptancetests.expectations.SupportPlanExpectation
import com.ecco.acceptancetests.fixtures.SupportPlanFixture
import com.ecco.acceptancetests.givens.SupportPlanGiven
import com.ecco.acceptancetests.ui.smoke.SupportPlanTests
import org.joda.time.LocalDate

interface SupportPlanContext : CommonSteps {
    fun given(testClass: SupportPlanTests): SupportPlanGiven

    fun openNeedsAssessment(firstName: String, lastName: String): SupportPlanFixture

    fun openSupportPlan(firstName: String, lastName: String): SupportPlanFixture

    fun reviewSetup(firstName: String, lastName: String, newReviewDate: LocalDate): SupportPlanFixture

    fun expect(): SupportPlanExpectation

    // what is this doing here?
    fun getNeedsAssessmentTabs(firstName: String, lastName: String): List<String>
}