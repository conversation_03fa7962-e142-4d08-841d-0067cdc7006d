package com.ecco.acceptancetests.steps

/**
 * English language driver for user operation (e.g. a CSR using a browser).
 *
 * Operations provided here should be readable against user tests.
 *
 * <AUTHOR>
 */
interface LoginSteps : CommonSteps {
    fun quit()

    /**
     * Navigate to home and then login a unique user
     */
    fun login(user: String)

    /**
     * Navigate to home and then login
     */
    fun login(username: String, password: String)

    /**
     * Navigate to home and then login - checks correct menus are visible
     */
    fun loginAsSysadmin()

    fun logout()
}