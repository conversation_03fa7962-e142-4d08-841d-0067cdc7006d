package com.ecco.acceptancetests.steps

import reactor.util.function.Tuple2
import java.time.LocalDate

interface HrSteps : CommonSteps {
    /** Returns the ID and name of the worker created. The name of the worker will be based on that provided.  */
    fun createWorker(workerName: String): Tuple2<Long, String>

    fun createWorkerJob(workerId: Long, start: LocalDate): Int

    fun linkWorkerToUser(workerName: String, username: String)
}