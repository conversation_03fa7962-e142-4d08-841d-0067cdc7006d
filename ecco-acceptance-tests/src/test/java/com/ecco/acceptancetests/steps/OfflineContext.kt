package com.ecco.acceptancetests.steps

import com.ecco.acceptancetests.expectations.OfflineExpectation
import com.ecco.acceptancetests.givens.OfflineGiven
import com.ecco.acceptancetests.ui.BaseSeleniumTest
import com.ecco.acceptancetests.ui.steps.webdriver.OfflineStepsWebDriver

interface OfflineContext : CommonSteps {
    fun forOnline(): OfflineStepsWebDriver

    fun given(testClass: BaseSeleniumTest): OfflineGiven

    fun expect(): OfflineExpectation

    fun loginOffline(): OfflineContext

    fun syncReferrals(): OfflineContext

    fun viewReferrals(): List<String>
}