package com.ecco.acceptancetests.steps

/**
 * 'Steps' are individual user recognisable tasks as would be given to a manual tester.
 *
 * @see http://jbehave.org/reference/stable/concepts.html for def of Story, Scenario, Step
 */
interface CommonSteps {
    fun navigateBack()

    fun navigateToHome()

    fun navigateToWelcome()

    // should not be here - move to an expectations class
    fun checkCanSeeText(text: String)

    fun checkCannotSeeText(text: String)
}