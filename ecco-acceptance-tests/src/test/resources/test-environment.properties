# This file acts as a bridge to bring properties from maven profile into the application
# which allows developers to have their own local server instance against which 
# selenium tests can be run

# The url that the test harness UI is deployed at e.g. https://theserver:9080/uat-webapp
testharness.host.url=${testharness.host.url}

# Some example config for performance testing config
performance.test.clients=${performance.test.clients}
performance.test.repeats=${performance.test.repeats}

