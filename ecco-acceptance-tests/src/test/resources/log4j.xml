<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <appenders>
        <RandomAccessFile name="syncFile" fileName="logs/acceptance-tests.log" append="false">
            <PatternLayout pattern="%d %p [%c] %-15X{username} - %m%n" />
        </RandomAccessFile>
        <Console name="syncStdout" target="SYSTEM_OUT">
            <PatternLayout pattern="%d %p [%c] %-15X{username} - %m%n" />
        </Console>

        <Async name="file" includeLocation="true" blocking="true" bufferSize="262144">
            <AppenderRef ref="syncFile"/>
        </Async>
        <Async name="stdout" includeLocation="true" blocking="true" bufferSize="262144">
            <AppenderRef ref="syncStdout"/>
        </Async>
    </appenders>

    <logger name="com.ecco" additivity="false" level="DEBUG">
        <appender-ref ref= "file" />
    </logger>

    <logger name="com.ecco.acceptancetests" level="INFO">
        <appender-ref ref="stdout" />
        <appender-ref ref= "file" />
    </logger>
    <logger name="com.ecco.test.support" level="INFO">
        <appender-ref ref="stdout" />
        <appender-ref ref= "file" />
    </logger>

    <!-- Suppress the horrid Javascript error that isn't one -->
    <logger name="com.gargoylesoftware.htmlunit.javascript.StrictErrorReporter" additivity="false" level="OFF">
        <appender-ref ref= "file" />
    </logger>

    <!-- Optionally suppress warnings that are helpful (Css errors etc) -->
    <logger name="com.gargoylesoftware.htmlunit" additivity="false" level="ERROR">
        <appender-ref ref= "file" />
    </logger>

    <root>
        <priority value="INFO" />
        <appender-ref ref= "file" />
        <appender-ref ref="stdout" />
    </root>
</configuration>
