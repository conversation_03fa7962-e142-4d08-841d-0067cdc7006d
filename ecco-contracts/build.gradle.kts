/*
 * This file was generated by the Gradle 'init' task.
 *
 * This project uses @Incubating APIs which are subject to change.
 */

plugins {
    id("org.eccosolutions.querydsl-java-library")
}

dependencies {
    implementation(project(":ecco-config"))
    implementation(project(":ecco-evidence"))
    implementation(project(":ecco-infrastructure"))
    implementation(project(":ecco-security"))
    implementation(project(":ecco-service-config"))

    implementation("joda-time:joda-time:2.10.8")
    implementation("org.springframework:spring-aspects")

}

description = "ecco-contracts"
