package com.ecco.buildings.dom;

import com.ecco.dom.AddressedLocation;
import com.ecco.dom.EvidenceCapable;
import com.ecco.serviceConfig.dom.ServiceCategorisation;
import com.ecco.dom.Identified;
import com.querydsl.core.annotations.QueryInit;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Type;
import org.jspecify.annotations.Nullable;
import org.springframework.beans.factory.annotation.Configurable;
import org.springframework.beans.factory.aspectj.AnnotationBeanConfigurerAspect;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import org.jspecify.annotations.NonNull;

/**
 * A FixedContainer is potentially a dwelling or industrial unit, a block of flats, a field, or a sub-unit of one of
 * these where it cannot be separated from the <em>parent</em> container.
 *
 *
 * A building is a type of fixed container.
 * A building has sub-units (i.e. parent-children relationship)
 * A building has an an owner
 * A building has occupiers which are Contacts (people or organisations)
 */
@Entity
@Table(name = "bldg_fixed")
@Configurable
@Getter
public class FixedContainer extends Container<FixedContainer> implements EvidenceCapable {

    public static final int DEFAULT_SERVICE_ALLOCATION_ID = -100;

    /*
     * SOME NOTES:
     * - Address related functionality is NOT based on ContactImpl - we will discover good pattern here
     * and then migrate older domain over.
     * Useful references on Party-Role approach can be found at:
     * http://www.univdata.com/Publications/Articles/IsYourOrganizationTooUniquetoUseUDM/tabid/291/Default.aspx
     * and
     * http://www.univdata.com/Portals/9/udm_Publication_Articles_11_05_Models_Patterns.pdf
     *
     * We have BookableResource which extends ContactImpl and uses the contacts table
     */
    private static final long serialVersionUID = 1L;

    @PersistenceContext
    private transient EntityManager em;

    @Setter
    @Nullable
    @JoinColumn(name = "parentId", updatable = false, insertable = false)
    @ManyToOne(fetch = FetchType.EAGER)
    private FixedContainer parent;

    @Setter
    @Column(name="parentId")
    private Integer parentId;

    @Nullable
    @JoinColumn(name = "addressId", updatable = false, insertable = false)
    @ManyToOne(fetch = FetchType.EAGER)
    @Getter
    private AddressedLocation location;

    @Setter
    @Column(name="addressId")
    private Integer locationId;

    /*public void setLocationId(AddressedLocation location) {
        this.location = location;
        this.locationId = location.getId();
    }*/

    @Setter
    @OneToOne(cascade= {CascadeType.PERSIST, CascadeType.REMOVE}, fetch=FetchType.LAZY)
    @JoinColumn(name="serviceRecipientId", updatable=false)
    @QueryInit("*.*.*")
    private BuildingServiceRecipient serviceRecipient;

    @Column(updatable = false, insertable = false)
    private Integer serviceRecipientId;

    @Column
    @Setter
    private boolean disabled;

    /**
     * List of charge category combinations that this building is applicable for.
     * Each combination includes both chargeCategoryId and chargeNameId for matching against RateCard entries.
     * This allows a building to support multiple charge categories and charge name types.
     * FinanceChargeCalculation uses this (via getNearestChargeCategoryCombinations) to 'determineRateCardEntries' in RateCardCalculationBase
     * which looks in a rateCard to find a rateCardEntry with matching chargeCategory and chargeNameId
     *  - one must exist on a building - search findChargeCategoryForBuilding for the assert
     *  - one can/is provided via BuildingCommandViewModel
     * This is designed to 'support service charge categories' eg BAND B (208) / BAND C (209) - see BaseAddressHistoryCommandAPITestSupport
     * NB chargeCategoryId is the finance equivalent of a care visit's 'matchesOnOutcome' eventStatusRateId - outcome of the visit (set via evidence or dropped appointments)
     * See chargeVisit.
     */
    @Setter
    @Lob
    @Column
    @Type(type = "com.ecco.buildings.hibernate.JSONUserTypeChargeCategoryInfoList")
    private @Nullable Map<String, List<ChargeCategoryCombinations>> chargeCategoryCombinations = new HashMap<>();
    public static final String ChargeCategoryCombinationsKey = "matches";

    @Setter
    @Lob
    @Column
    @Type(type = "com.ecco.infrastructure.hibernate.JSONUserTypeStringToObjectMap")
    private HashMap<String,String> textMap = new HashMap<>();

    @Column
    @Setter
    @Type(type = "com.ecco.infrastructure.hibernate.JSONUserTypeStringToObjectMap")
    private HashMap<String, Integer> choicesMap = new HashMap<>();

    /*
    @Lob
    @Column(name="objectMap")
    @Type(type = "com.ecco.infrastructure.hibernate.JSONUserTypeStringToObjectMap")
    private HashMap<String,Object> customObjectData = new HashMap<>();

    @Lob
    @Column(name="dateMap2")
    @Type(type = "com.ecco.infrastructure.hibernate.JSONUserTypeStringToLocalDateMap")
    private HashMap<String, LocalDate> dateMap = new HashMap<>();
*/

    public FixedContainer() {
        super();
        injectServices();
    }

    public FixedContainer(FixedContainer parent) {
        injectServices();
        this.parent = parent;
    }

    @Override
    public Object readResolve()  {
        injectServices();
        return this;
    }

    private void injectServices() {
        // NOTE: This expects to find @Configurable on the class
        AnnotationBeanConfigurerAspect.aspectOf().configureBean(this);
    }

    @Override
    public Integer countSiblings() {
        return null;
    }

    @Override
    public Identified getGrandParent() {
        return null;
    }

    /**
     * Get the top most parent's addressId location
     */
    public AddressedLocation getTopParentLocation() {
        return getTopParent().location;
    }

    public Integer getThisOrTopParentLocation() {
        return locationId != null ? locationId : getTopParent().locationId;
    }

    FixedContainer getTopParent() {
        if (getParent() == null) {
            return this;
        }
        return getParent();
    }

    // TODO some registration
    @Override
    protected void prePersistCall() {
        createServiceRecipient();
        this.fireCalendarableCreated(); // as we're not using @PreSetEntity infrastructure
    }

    protected void createServiceRecipient() {
        serviceRecipient = new BuildingServiceRecipient();
        serviceRecipient.setBuilding(this);
        serviceRecipient.setServiceAllocation(em.getReference(ServiceCategorisation.class, DEFAULT_SERVICE_ALLOCATION_ID));
    }

    @Override
    public EvidenceCapable getParentEvidenceCapable() {
        return null; // doesn't yet support having a parent
    }

    /**
     * Gets the service allocation ID without triggering lazy loading of the serviceRecipient.
     * For buildings, this typically returns the DEFAULT_SERVICE_ALLOCATION_ID unless the
     * serviceRecipient is already loaded in the current session.
     */
    public Integer getServiceAllocationId() {
        // If serviceRecipient is already loaded and initialized, use it
        if (serviceRecipient != null && org.hibernate.Hibernate.isInitialized(serviceRecipient)) {
            return serviceRecipient.getServiceAllocationId();
        }

        // Otherwise return the default - this avoids lazy loading issues
        return DEFAULT_SERVICE_ALLOCATION_ID;
    }

    /**
     * Returns all charge category combinations for this building, including inherited ones from parent.
     * Each combination includes both chargeCategoryId and chargeNameId for matching against RateCard entries.
     * This building's combinations take precedence over parent combinations.
     */
    public @NonNull List<ChargeCategoryCombinations> getNearestChargeCategoryCombinations() {
        List<ChargeCategoryCombinations> combinations = new ArrayList<>();

        // Add combinations from this building first
        if (chargeCategoryCombinations != null && !chargeCategoryCombinations.isEmpty()) {
            combinations.addAll(getChargeCategoryCombinations());
        }

        // Add combinations from parent (inherited)
        if (getParent() != null) {
            List<ChargeCategoryCombinations> parentCombinations = getParent().getNearestChargeCategoryCombinations();
            // Only add parent combinations that don't conflict with this building's combinations
            for (ChargeCategoryCombinations parentCombo : parentCombinations) {
                boolean alreadyExists = combinations.stream().anyMatch(existing ->
                    Objects.equals(existing.getChargeCategoryId(), parentCombo.getChargeCategoryId()) &&
                    Objects.equals(existing.getChargeNameId(), parentCombo.getChargeNameId())
                );
                if (!alreadyExists) {
                    combinations.add(parentCombo);
                }
            }
        }

        return combinations;
    }

    /**
     * Adds a charge category combination to this building.
     */
    public void addChargeCategoryCombination(@Nullable Integer chargeNameId, @Nullable Integer chargeCategoryId) {
        if (chargeCategoryCombinations == null) {
            chargeCategoryCombinations = new HashMap<>();
        }

        ChargeCategoryCombinations newCombination = new ChargeCategoryCombinations(chargeNameId, chargeCategoryId);

        // Get existing combinations, handling the case where they might be deserialized as Maps
        List<ChargeCategoryCombinations> existingCombinations = getChargeCategoryCombinations();
        existingCombinations.add(newCombination);

        // Store the updated list
        chargeCategoryCombinations.put(ChargeCategoryCombinationsKey, existingCombinations);
    }

    /**
     * Gets the charge category combinations for this building only (not including parent hierarchy).
     */
    public @NonNull List<ChargeCategoryCombinations> getChargeCategoryCombinations() {
        if (chargeCategoryCombinations == null || chargeCategoryCombinations.isEmpty()) {
            return new ArrayList<>();
        }

        List<ChargeCategoryCombinations> combinations = chargeCategoryCombinations.get(ChargeCategoryCombinationsKey);
        return combinations != null ? new ArrayList<>(combinations) : new ArrayList<>();
    }

    /**
     * Sets the charge category combinations from a list of ChargeCategoryInfo objects.
     */
    public void setChargeCategoryCombinations(@NonNull List<ChargeCategoryCombinations> combinations) {
        this.chargeCategoryCombinations = new HashMap<>();

        if (!combinations.isEmpty()) {
            // Store as a list of ChargeCategoryInfo objects
            List<ChargeCategoryCombinations> combinationsList = new ArrayList<>(combinations);
            chargeCategoryCombinations.put(ChargeCategoryCombinationsKey, combinationsList);
        }
    }

    /**
     * Data class to hold both charge category ID and charge name ID
     */
    @lombok.NoArgsConstructor
    @lombok.Getter
    @lombok.Setter
    public static class ChargeCategoryCombinations {
        private @Nullable Integer chargeCategoryId;
        private @Nullable Integer chargeNameId;

        public ChargeCategoryCombinations(@Nullable Integer chargeNameId, @Nullable Integer chargeCategoryId) {
            this.chargeNameId = chargeNameId;
            this.chargeCategoryId = chargeCategoryId;
        }
    }
}
