package com.ecco.dao;

import com.querydsl.core.annotations.QueryProjection;
import org.joda.time.DateTime;

import java.math.BigDecimal;

/**
 * WorkAnalysisGroupSummary
 */
public class WorkAnalysisGroupSummaryViewModel {

    /** Label to show */
    public String key;
    public String key2;

    /** The id of the item we've grouped on */
    public Integer keyId;

    public Integer count;

    public DateTime latestWorkDate;
    public DateTime lastSignedWorkDate;
    public DateTime lastUnSignedWorkDate;
    public Integer totalTimeSpentMins;
    public Integer totalVisits;
    public Integer unsignedWorkCount;

    public BigDecimal averageVisitLength;
//    public Map<String, Integer> countByCommentType;


    public WorkAnalysisGroupSummaryViewModel() {
    }

    @QueryProjection
    public WorkAnalysisGroupSummaryViewModel(Integer keyId, String key, Integer count) {
        this.keyId = keyId;
        this.key = key;
        this.count = count;
        this.totalVisits = count;
    }

    // TODO: we cannot deal with last unsigned and signed in this query unless we have 2 sep groups
    @QueryProjection
    public WorkAnalysisGroupSummaryViewModel(Integer keyId, String key, Integer count, DateTime latestWorkDate,
                                             Integer totalTimeSpentMins, Double averageVisitLength,
                                             Integer signedWorkCount) {
        this.keyId = keyId;
        this.key = key;
        this.count = count;
        this.totalVisits = count;
        this.latestWorkDate = latestWorkDate;
        this.totalTimeSpentMins = totalTimeSpentMins;
        this.averageVisitLength = BigDecimal.valueOf(averageVisitLength);
        this.unsignedWorkCount = count - signedWorkCount;
    }

    @QueryProjection
    public WorkAnalysisGroupSummaryViewModel(Integer keyId, String key, String key2, Integer count, DateTime latestWorkDate,
                                             Integer totalTimeSpentMins, Double averageVisitLength,
                                             Integer signedWorkCount) {
        this.keyId = keyId;
        this.key = key;
        this.key2 = key2;
        this.count = count;
        this.totalVisits = count;
        this.latestWorkDate = latestWorkDate;
        this.totalTimeSpentMins = totalTimeSpentMins;
        this.averageVisitLength = BigDecimal.valueOf(averageVisitLength);
        this.unsignedWorkCount = count - signedWorkCount;
    }
}
