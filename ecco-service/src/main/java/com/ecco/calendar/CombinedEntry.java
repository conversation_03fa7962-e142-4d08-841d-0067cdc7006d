package com.ecco.calendar;

import com.ecco.calendar.core.Entry;
import com.ecco.calendar.dom.EventEntry;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import org.jspecify.annotations.Nullable;
import java.util.UUID;


/**
 * Represents a calendar entry from the ical system and ecco-specific data
 * As per CalendarEntries and CombinedEntry, this is used as an intermediary class, not a dto to the client
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CombinedEntry {

    /**
     * The ical generic event information.
     */
    Entry icalEntry;

    /**
     * The Ecco domain-specific one-time event information.
     */
    @Nullable
    EventEntry eventEntry;

    UUID workUuid;

    /**
     * The calendar this event is loaded from. This is not always available if
     * loading an event directly, since the calendarId's are associated with events.
     * This property is used to determine colours on the calendar ui.
     * NB This is not the same as the Entry.calendarIdUserReferenceUri - that is
     * the owner, where multiple calendarId's can be part of the entry.
     * calendarId is not used to determine the person on an event, see CosmoHelper.syncAttendeesWithCalendars
     * calendarIdUserReferenceUri is used, but we can't use this from ECCO (although is does contain the cosmo user's id)
     */
    @Nullable
    String calendarId;
}
