package com.ecco.referral;

import com.ecco.config.dom.ListDefinitionEntry;
import com.ecco.event.ReferralClosedOff;
import com.ecco.evidence.repositories.TaskStatusRepository;
import com.ecco.infrastructure.bus.MessageBus;
import com.ecco.service.LinearWorkflowService;
import org.springframework.context.ApplicationEvent;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.time.Instant;

/**
 * Subscribes to referral task close events (for workflow-driven and non-workflow-driven services) and notifies
 * external systems of the update.
 *
 * @since 19/12/14
 */
public class ReferralCloseWorkflowSyncAgent {

    @PersistenceContext
    private EntityManager entityManager;

    private final TaskStatusRepository taskStatusRepository;

    public ReferralCloseWorkflowSyncAgent(final TaskStatusRepository taskStatusRepository,
                                          final MessageBus<ApplicationEvent> messageBus) {
        this.taskStatusRepository = taskStatusRepository;
        messageBus.subscribe(ReferralClosedOff.class, this::handleTasks);
    }

    private void handleTasks(ReferralClosedOff message) {
        var tasks = taskStatusRepository.findAllByServiceRecipientIdAndCompletedIsNull(message.getServiceRecipientId());
        tasks.forEach(t -> {
            if (t.getCompleted() == null) {
                t.setCompleted(Instant.now());
                t.setCompletedStatus(entityManager.getReference(ListDefinitionEntry.class, ListDefinitionEntry.TASKSTATUS_PARENTINACTIVE_ID));
                taskStatusRepository.save(t);
            }
        });
    }

    public void undo(int serviceRecipientId) {
        var tasks = taskStatusRepository.findAllByServiceRecipientIdAndCompletedIsNotNull(serviceRecipientId);
        tasks.forEach(t -> {
            // NB completedStatus can be null - perhaps legacy data
            var closedOff = t.getCompletedStatus() != null && (ListDefinitionEntry.TASKSTATUS_PARENTINACTIVE_ID == t.getCompletedStatusId());
            var isCloseOffTask = t.getTaskDefinitionId() != null && (t.getTaskDefinitionId().equals(LinearWorkflowService.CLOSE_OFF_ID));
            if (closedOff || isCloseOffTask) {
                t.setCompletedStatus(null);
                t.setCompleted(null);
                taskStatusRepository.save(t);
            }
        });
    }
}
