package com.ecco.referral;

import com.ecco.config.service.ExternalSystemService.ApiType;
import com.ecco.dto.ClientSupportEvent;

import java.net.URI;

/**
 * Defines a strategy for importing clients from an external system source.
 *
 * @since 08/07/2014
 */
public interface ClientEventNotificationStrategy {
    boolean supports(ApiType apiType);

    void notifyClientSupportEvent(String externalSystem, ApiType apiType, URI uri, ClientSupportEvent event);
}
