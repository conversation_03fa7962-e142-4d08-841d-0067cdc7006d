package com.ecco.referral;

import com.ecco.config.dom.ExternalSystem;
import com.ecco.dom.ClientDetail;
import com.ecco.dto.ClientSupportEvent;
import com.ecco.infrastructure.bus.MessageBus;
import com.ecco.event.ReferralTaskCompleted;
import com.google.common.collect.ImmutableList;
import org.springframework.context.ApplicationEvent;
import org.springframework.web.client.ResourceAccessException;

import java.util.Collection;

/**
 * Subscribes to referral task completion events (for workflow-driven and non-workflow-driven services) and notifies
 * external systems of the update.
 *
 * @since 19/12/14
 */
public class ReferralWorkflowExternalSyncAgent {
    private final Collection<ClientEventNotificationStrategy> delegates = ImmutableList.of();

    public ReferralWorkflowExternalSyncAgent(final MessageBus<ApplicationEvent> messageBus) {

        messageBus.subscribe(ReferralTaskCompleted.class, this::notifyExternalSystem);

    }

    private void notifyExternalSystem(ReferralTaskCompleted message) {
        final ClientDetail client = message.getClient();
        final ExternalSystem notifiable = client.getExternalClientSource();

        if (notifiable.isNotifyWorkflow()) {
            final ClientSupportEvent notification = ClientSupportEvent.BuilderFactory.create()
                    .externalClientRef(client.getExternalClientRef())
                    .externalClientSource(notifiable.getName())
                    .localSupportWorkerCode(message.getSupportWorkerCode())
                    .localSupportWorkerName(message.getSupportWorkerName())
                    .localReferralCode(message.getReferralCode())
                    .eventType("") // TODO: add event type
                    .build();

            for (ClientEventNotificationStrategy delegate : delegates) {
                if (delegate.supports(notifiable.getApiType())) {
                    try {
                        delegate.notifyClientSupportEvent(notifiable.getName(), notifiable.getApiType(), notifiable.getUri(), notification);
                    } catch (ResourceAccessException e) {
                        // TODO: Could do with better approach to failed connections...
                        throw e;
                    }
                }
            }
        }
    }
}
