package com.ecco.referral;

import com.ecco.dao.DemandScheduleRepository;
import com.ecco.dom.agreements.DemandSchedule;
import com.ecco.event.ReferralClosedOff;
import com.ecco.calendar.event.RotaDemandPostChangeEvent;
import com.ecco.infrastructure.bus.MessageBus;
import com.ecco.infrastructure.time.JodaToJDKAdapters;
import org.joda.time.LocalDate;
import org.jspecify.annotations.NonNull;
import org.springframework.context.ApplicationEvent;

import java.time.ZoneOffset;

/**
 * @since 19/08/2014
 */
public class ReferralCloseScheduleSyncAgent {
    private final DemandScheduleRepository demandScheduleRepository;

    @NonNull
    final MessageBus<ApplicationEvent> messageBus;

    public ReferralCloseScheduleSyncAgent(DemandScheduleRepository demandScheduleRepository,
                                          final MessageBus<ApplicationEvent> messageBus) {
        this.demandScheduleRepository = demandScheduleRepository;
        this.messageBus = messageBus;

        messageBus.subscribe(ReferralClosedOff.class, this::truncateDemandSchedules);
    }

    private void truncateDemandSchedules(ReferralClosedOff message) {
        final LocalDate closed = message.getClosedOff().toLocalDate();
        final int srId = message.getServiceRecipientId();


        // Find the demand schedules relating to the referral and truncate them to the exited date.
        // Delete demand schedules which don't start before the exit date.
        // Leave the agreements alone as they are probably reflecting a real-life contract.
        var demandSchedules = demandScheduleRepository.findAllByAgreementServiceRecipientId(srId);

        for (DemandSchedule demandSchedule : demandSchedules) {
            if (!demandSchedule.getStart().isBefore(closed)) {
                demandSchedule.delete();
             } else if (demandSchedule.getEnd() == null || demandSchedule.getEnd().isAfter(closed)) {
                demandSchedule.truncate(closed);
                demandScheduleRepository.save(demandSchedule);
            } else {
                // added in 2a68d48a for "DEV-1989 Always update bounds in calendar when closing off"
                // but the historical issue is probably resolved? DEV-1989
                // so let's not re-save this schedule specifically on close-off if already closed
                // because this schedule may no longer exist
                // BUT it must exist...
                // because 1) due to the series calendar - see z5784
                    // as the client sends the schedule to SRAppointmentScheduleCommandHandler
                    // which calls rotaService.updateAppointmentSchedule
                    // and the client gets the schedule based on demandScheduleRepo
                    // meaning it should exist
                // because 2) due to close off z7155
                    // closing off a referral (even when the schedules are ended the same date)
                    // still generates an error: "Caused by: com.ecco.calendar.core.CalendarException: No entry with handle bc99dc49-a7f7-4fe4-aa16-aa4e2bc08418 exists"
                //demandSchedule.updateBoundsInCalendarEnd(); // To clean up from previous failed updates
            }
        }

        // local date can be seen as a utc midnight date
        var closedInstant = JodaToJDKAdapters.localDateToJDk(closed).atStartOfDay().toInstant(ZoneOffset.UTC);
        messageBus.publishBeforeTxEnd(new RotaDemandPostChangeEvent(message, srId, closedInstant, null, false));

    }
}
