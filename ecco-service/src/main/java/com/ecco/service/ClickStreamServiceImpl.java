package com.ecco.service;

import com.ecco.dao.ClickStreamRepository;
import com.ecco.dom.ClickStream;
import com.ecco.infrastructure.annotations.WriteableTransaction;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service("clickStreamService")
@WriteableTransaction
public class ClickStreamServiceImpl implements ClickStreamService {

    @Autowired
    ClickStreamRepository clickStreamRepository;

    @Override
    public void setClickStream(ClickStream cs) {
        clickStreamRepository.save(cs);
    }

}
