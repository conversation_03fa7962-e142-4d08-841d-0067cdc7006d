package com.ecco.service;

import com.ecco.config.dom.ExternalSystem;
import com.ecco.config.repositories.ExternalSystemRepository;
import com.ecco.dto.DelegateResponse;
import com.ecco.dto.FlagsDefinition;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.client.ResourceAccessException;

import org.jspecify.annotations.NonNull;
import java.util.Collection;
import java.util.List;

@Service
@AllArgsConstructor
public class EvidenceServiceImpl implements EvidenceService {

    private final ExternalSystemRepository externalSystemRepository;
    private final Collection<ClientImportStrategy> delegates;
    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public DelegateResponse<FlagsDefinition> queryClientFlags(@NonNull String externalSourceName, @NonNull String externalClientRef) {
        List<ExternalSystem> clientSources = externalSystemRepository.findByClientSourceTrue();
        var clientSource = clientSources.stream().filter(s -> s.getName().equals(externalSourceName)).findFirst().orElseThrow();
        for (ClientImportStrategy delegate : delegates) {
            if (delegate.supports(clientSource.getApiType())) {
                final String name = clientSource.getName();
                try {
                    final FlagsDefinition elements = delegate.queryClientFlags(name, clientSource.getApiType(),
                            clientSource.getUri(), externalClientRef);
                    return DelegateResponse.ok(elements);
                } catch (ResourceAccessException e) {
                    return DelegateResponse.fail(
                            "This datasource is currently unavailable - please contact support if the problem persists.");
                } catch (HttpStatusCodeException e) {
                    String message = ClientDetailServiceImpl.getMessageText(e, this.objectMapper);
                    return DelegateResponse.fail(message);
                }
            }
        }
        return DelegateResponse.fail("no datasource match for " + externalSourceName);
    }
}
