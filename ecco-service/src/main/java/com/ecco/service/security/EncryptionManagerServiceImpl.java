package com.ecco.service.security;

import com.ecco.dao.security.UserDeviceRepository;
import com.ecco.infrastructure.annotations.ReadOnlyTransaction;
import com.ecco.infrastructure.annotations.WriteableTransaction;
import com.ecco.security.dom.User;
import com.ecco.security.dom.UserDevice;
import com.ecco.security.service.UserManagementService;
import com.google.common.collect.ImmutableList;

import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationCredentialsNotFoundException;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.net.InetAddress;
import java.security.Security;
import java.util.Collection;
import java.util.UUID;

@Service("encryptionManagerService")
public class EncryptionManagerServiceImpl implements EncryptionManagerService {
    private Collection<EncryptionStrategy> delegates = ImmutableList.of(new AesGcmEncryptionStrategy(), new DummyEncryptionStrategy());

    private final UserDeviceRepository userDeviceRepository;

    private final UserManagementService userManagementService;

    static {
        if (Security.getProvider("BC") == null) {
            Security.addProvider(new BouncyCastleProvider());
        }
    }

    EncryptionManagerServiceImpl() {
        // for cglib <sigh>
        this.userDeviceRepository = null;
        this.userManagementService = null;
    }

    @Autowired
    public EncryptionManagerServiceImpl(UserDeviceRepository userDeviceRepository, UserManagementService userManagementService) {
        this.userDeviceRepository = userDeviceRepository;
        this.userManagementService = userManagementService;
    }

    @Override
    @ReadOnlyTransaction
    public UserDevice findValidatedDevice(UUID guid) {
        final UserDevice userDevice = userDeviceRepository.findOneByGuidAndValid(guid, true);
        if (userDevice != null) {
            userDevice.getUser().getUsername(); // Make sure this is populated.
        }
        return userDevice;
    }

    @Override
    @WriteableTransaction
    public UserDevice createDeviceForCurrentUser(String description, String userAgent, InetAddress ipAddress) {
        final User user = userManagementService.loadUserByUsername(getCurrentUsername());
        final UserDevice userDevice = new UserDevice(user, CIPHER_AES_GCM);
        userDevice.setDescription(description);
        userDevice.setUserAgent(userAgent);
        userDevice.setIpAddress(ipAddress);
        userDeviceRepository.save(userDevice);

        return userDevice;
    }

    private String getCurrentUsername() {
        try {
            return SecurityContextHolder.getContext().getAuthentication().getName();
        } catch (Exception e) {
            throw new AuthenticationCredentialsNotFoundException("Could not look up current username", e);
        }
    }

    @Override
    @WriteableTransaction
    public UserDevice revalidateDevice(UUID guid) {
        UserDevice device = userDeviceRepository.findOneByGuid(guid);
        Assert.notNull(device, "Could not find user device");
        device.revalidate(CIPHER_AES_GCM);
        return device;
    }

    @Override
    @WriteableTransaction
    public UserDevice invalidateDevice(UserDevice device, String reason) {
        device = userDeviceRepository.findById(device.getId())
            .orElseThrow(NullPointerException::new);
        device.invalidate(reason);
        return device;
    }

    @Override
    public byte[] decryptData(UserDevice device, byte[] encryptedData) {
        for (EncryptionStrategy delegate : delegates) {
            if (delegate.supports(device.getCipher())) {
                return delegate.decrypt(device.getCipher(), device.getKey(), encryptedData);
            }
        }
        throw new IllegalArgumentException("No encryption strategy known for: " + device.getCipher());
    }

    @Override
    public byte[] encryptData(UserDevice device, byte[] decryptedData) {
        for (EncryptionStrategy delegate : delegates) {
            if (delegate.supports(device.getCipher())) {
                return delegate.encrypt(device.getCipher(), device.getKey(), decryptedData);
            }
        }
        throw new IllegalArgumentException("No encryption strategy known for: " + device.getCipher());
    }

    /** A dummy implementation which doesn't actually encrypt or decrypt. */
    private static class DummyEncryptionStrategy implements EncryptionStrategy {
        @Override
        public boolean supports(String cipher) {
            return cipher.equals(CIPHER_NONE);
        }

        @Override
        public byte[] encrypt(String cipherName, byte[] key, byte[] plainData) {
            return plainData;
        }

        @Override
        public byte[] decrypt(String cipher, byte[] key, byte[] encryptedData) {
            return encryptedData;
        }
    }
}
