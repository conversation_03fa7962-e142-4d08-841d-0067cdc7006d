package com.ecco.service.security;

import com.ecco.security.dom.CommandRequest;
import com.ecco.security.dom.CommandResponse;
import com.ecco.security.dom.User;
import com.ecco.security.dom.UserDevice;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.io.IOException;
import java.net.URI;
import java.util.Base64;
import java.util.Iterator;
import java.util.Map;
import java.util.UUID;

import static com.ecco.security.dom.CommandResponse.BuilderFactory.create;
import static com.ecco.security.dom.CommandResponse.isSuccessful;
import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;
import static org.springframework.web.bind.annotation.RequestMethod.GET;

@SuppressWarnings("SpringJavaAutowiringInspection")
@Service("queryManagerService")
public class QueryManagerServiceImpl implements QueryManagerService {
    private final EncryptionManagerService encryptionManagerService;
    private final ObjectMapper objectMapper = new ObjectMapper();

    private final Logger log = LoggerFactory.getLogger(getClass());


    public QueryManagerServiceImpl() {
        this(null);
    }

    @Autowired
    public QueryManagerServiceImpl(EncryptionManagerService encryptionManagerService) {
        this.encryptionManagerService = encryptionManagerService;
    }

    @Override
    public CommandResponse executeQuery(UUID userDeviceGuid, URI url, final CommandRequestExecutor queryRequestExecutor, ProxyUrlRewriter urlRewriter) {
        final UserDevice userDevice = encryptionManagerService.findValidatedDevice(userDeviceGuid); // TODO: Use this as auth session token??  or do we just exchange for a cookie
        Assert.notNull(userDevice);

        CommandRequest commandRequest = new CommandRequest(GET.name(), url, APPLICATION_JSON_VALUE, APPLICATION_JSON_VALUE, null);
        try {
            CommandResponse commandResponse = executeRequestAsUser(queryRequestExecutor, userDevice.getUser(), commandRequest);

            CommandResponse.Builder response = create()
                    .statusCode(commandResponse.getStatusCode());

            if (isSuccessful(commandResponse)) {
                response = response.body(encryptJsonObject(userDevice, commandResponse.getBody()));
            }
            return response.build();

        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }
    }

    private CommandResponse executeRequestAsUser(CommandRequestExecutor queryRequestExecutor, User user, CommandRequest commandRequest) {
        return new RunAsTemplate(user).execute(() -> {
            try {
                return queryRequestExecutor.processRequest(commandRequest);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });
    }

    private String encryptJsonObject(UserDevice userDevice, String jsonObject) throws IOException {
        JsonNode rootNode = objectMapper.readTree(jsonObject);
        if (rootNode.isArray()) {
            for (JsonNode arrayItem : rootNode) {
                encryptNode(userDevice, arrayItem);
            }
        } else {
            encryptNode(userDevice, rootNode);
        }
        return objectMapper.writeValueAsString(rootNode);
    }

    private void encryptNode(UserDevice userDevice, JsonNode node) throws JsonProcessingException {
        if (node.isObject()) {
            ObjectNode objectItem = (ObjectNode) node;
            encryptObject(userDevice, objectItem);
        }
        else {
            log.warn("Skipped encrypting JSON object of type: " + node.getNodeType().name());
        }
    }

    /**
     * Encrypt o for specified userDevice by adding a field 'base64Payload' with an encrypted object containing
     * the encrypted fields, while leaving the plain fields (keys required for indexes on client end) in the root of the
     * object.
     */
    private void encryptObject(UserDevice userDevice, ObjectNode o) throws JsonProcessingException {
        final Iterator<Map.Entry<String, JsonNode>> fields = o.fields();

        ObjectNode plain = objectMapper.createObjectNode();
        ObjectNode toBeEncrypted = objectMapper.createObjectNode();

        while (fields.hasNext()) {
            Map.Entry<String, JsonNode> field = fields.next();
            fields.remove();
            if (needsEncryption(field)) {
                toBeEncrypted.set(field.getKey(), field.getValue());
            }
            else {
                plain.set(field.getKey(), field.getValue());
            }
        }

        byte[] encryptedData = encryptionManagerService.encryptData(userDevice, objectMapper.writeValueAsBytes(toBeEncrypted));

        /* This is formatted to match EncryptedDto */
        ObjectNode encrypted = objectMapper.createObjectNode();
        // we omit userDeviceId on outbound path as it's always for the user device requesting the data
        encrypted.put("base64Payload", Base64.getEncoder().encodeToString(encryptedData));

        o.set("secret", encrypted);
        o.set("plain", plain);
    }

    private boolean needsEncryption(Map.Entry<String, JsonNode> field) {
        return !(  field.getKey().equalsIgnoreCase("id")
                || field.getKey().endsWith("Id")
                || field.getKey().endsWith("evidenceGroupKey")
                || field.getKey().equalsIgnoreCase("uid") // we're getting entryId = null in result
                || field.getKey().equals("links") );
    }
}
