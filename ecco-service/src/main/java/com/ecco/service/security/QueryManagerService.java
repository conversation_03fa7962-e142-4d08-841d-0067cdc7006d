package com.ecco.service.security;

import com.ecco.security.dom.CommandResponse;

import java.net.URI;
import java.util.UUID;

/**
 * Manages processing queries and returning encrypted results.
 */
public interface QueryManagerService {
    /**
     * Execute a query.
     *
     * @param userDeviceGuid the user device for which the query is being executed
     * @param url the URL of the actual query API
     * @param commandRequestExecutor the executor to turn a {@link com.ecco.security.dom.CommandRequest} into a {@link com.ecco.security.dom.CommandResponse}
     * @param urlRewriter the rewriter for any links in the content to ensure they are proxy-friendly
     * @return a command response object, with encrypted body and status code reflecting the subrequest status
     * @throws IllegalArgumentException if the guid did not represent a user-device
     */
    CommandResponse executeQuery(UUID userDeviceGuid, URI url, CommandRequestExecutor commandRequestExecutor, ProxyUrlRewriter urlRewriter);
}
