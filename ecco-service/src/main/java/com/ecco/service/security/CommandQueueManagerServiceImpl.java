package com.ecco.service.security;

import com.ecco.dao.security.QueuedCommandRepository;
import com.ecco.dao.security.UserDeviceRepository;
import com.ecco.infrastructure.annotations.WriteableTransaction;
import com.ecco.security.dom.CommandRequest;
import com.ecco.security.dom.CommandResponse;
import com.ecco.security.dom.QueuedCommand;
import com.ecco.security.dom.UserDevice;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.joda.time.DateTime;
import org.jspecify.annotations.NonNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.io.IOException;
import java.net.URI;
import java.util.UUID;

import static com.google.common.base.Charsets.UTF_8;
import static org.springframework.http.MediaType.APPLICATION_JSON;

@SuppressWarnings("SpringJavaAutowiringInspection")
@WriteableTransaction
@Service("commandQueueManagerService")
public class CommandQueueManagerServiceImpl implements CommandQueueManagerService {
    private final EncryptionManagerService encryptionManagerService;
    private final UserDeviceRepository userDeviceRepository;
    private final QueuedCommandRepository queuedCommandRepository;
    private final ObjectMapper objectMapper = new ObjectMapper();

    private final Logger log = LoggerFactory.getLogger(getClass());

    CommandQueueManagerServiceImpl() {
        // for cglib
        this(null, null, null);
    }

    @Autowired
    public CommandQueueManagerServiceImpl(EncryptionManagerService encryptionManagerService, UserDeviceRepository userDeviceRepository, QueuedCommandRepository queuedCommandRepository) {
        this.encryptionManagerService = encryptionManagerService;
        this.userDeviceRepository = userDeviceRepository;
        this.queuedCommandRepository = queuedCommandRepository;
    }

    @NonNull
    @Override
    public QueuedCommand queueCommand(UUID userDeviceGuid, byte[] encryptedCommand) {
        final UserDevice device = userDeviceRepository.findOneByGuid(userDeviceGuid);
        Assert.notNull(device, "Device not found");
        QueuedCommand command;
        try {
            CommandRequest decryptedCommand = decryptCommand(device, encryptedCommand);
            command = new QueuedCommand(device, device.isAutoDismiss(), encryptedCommand, decryptedCommand);
        } catch (Exception e) {
            log.warn("Failed to process command: {}", e.getMessage());
            command = new QueuedCommand(device, device.isAutoDismiss(), encryptedCommand);
        }
        return queuedCommandRepository.save(command);
    }

    @NonNull
    @Override
    public QueuedCommand executeCommand(Long queuedCommandId, final CommandRequestExecutor commandRequestExecutor) {
        final QueuedCommand queuedCommand = queuedCommandRepository.findById(queuedCommandId)
                .orElseThrow(NullPointerException::new);
        if (queuedCommand.isExecutable()) {
            final CommandResponse response = new RunAsTemplate(queuedCommand.getUserDevice().getUser()).execute(() -> {
                try {
                    return commandRequestExecutor.processRequest(queuedCommand.getCommandRequest());
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            });
            queuedCommand.setCommandResponse(response);
            return queuedCommandRepository.save(queuedCommand);
        } else {
            throw new IllegalStateException("Cannot execute dismissed or already-executed command: " + queuedCommandId + "\n" + queuedCommand.getCommandRequest());
        }
    }

    @NonNull
    @Override
    public QueuedCommand dismissCommand(Long queuedCommandId) {
        final QueuedCommand queuedCommand = queuedCommandRepository.findById(queuedCommandId)
                .orElseThrow(NullPointerException::new);
        if (queuedCommand.isDismissable()) {
            queuedCommand.dismiss();
            return queuedCommandRepository.save(queuedCommand);
        } else {
            throw new IllegalStateException("Cannot dismiss executed or already-dismissed command: " + queuedCommandId + "\n" + queuedCommand.getCommandRequest());
        }
    }

    @NonNull
    @Override
    public QueuedCommand archiveCommand(Long queuedCommandId) {
        final QueuedCommand queuedCommand = queuedCommandRepository.findById(queuedCommandId)
                .orElseThrow(NullPointerException::new);
        if (queuedCommand.isArchivable()) {
            queuedCommand.archive();
            return queuedCommandRepository.save(queuedCommand);
        } else {
            throw new IllegalStateException("Cannot archive unprocessed or already-archived command: " + queuedCommandId + "\n" + queuedCommand.getCommandRequest());
        }
    }

    public static class CommandRequestDto {
        public String method;
        public String url;
        public String contentType;
        public String acceptType;
        public String body;
    }

    private CommandRequest decryptCommand(UserDevice device, byte[] encryptedCommand) {
        byte[] decryptedJson = encryptionManagerService.decryptData(device, encryptedCommand);
        try {
            final CommandRequestDto commandRequestDto = objectMapper.readValue(decryptedJson, CommandRequestDto.class);
            return new CommandRequest(commandRequestDto.method, URI.create(commandRequestDto.url), commandRequestDto.contentType, commandRequestDto.acceptType, commandRequestDto.body);
        } catch (IOException ex) {
            try {
                // HERE decrypted json is {"commandName": ... "commandUri": etc}, rather than the dto
                final JsonNode commandDto = objectMapper.readTree(decryptedJson);
                String url = commandDto.get("commandUri").asText();
                return new CommandRequest("POST", URI.create("/api/" + url),
                        APPLICATION_JSON.toString(), APPLICATION_JSON.toString(), new String(decryptedJson, UTF_8));
            } catch (IOException e) {
                throw new IllegalArgumentException("could not parse JSON data", e);
            }
        }
    }

    @Scheduled(initialDelayString = "PT4S", fixedDelayString = "P1D")
    public void cleanupOldExecutedCommands() {
        DateTime threshold = DateTime.now().minusMonths(1);
        queuedCommandRepository.deleteExecutedEarlierThan(threshold);
//        queuedCommandRepository.deleteExecutedExceptMostRecent(100L);
    }
}
