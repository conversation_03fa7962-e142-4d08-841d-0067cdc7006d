package com.ecco.service.security;

import com.ecco.security.dom.QueuedCommand;
import org.jspecify.annotations.NonNull;

import java.util.UUID;

/**
 * Manages processing encrypted commands.
 */
public interface CommandQueueManagerService {
    /**
     * Persist an unexecuted command for the user device provided. If the command data cannot be decoded, an record
     * is still returned containing the encrypted data.
     *
     * @param userDeviceGuid the user device for which the command is queued
     * @param encryptedCommand the command payload, encrypted with the user device key and cipher
     * @return a queued command object representing the submitted data
     * @throws IllegalArgumentException if the guid did not represent a user-device
     */
    @NonNull
    QueuedCommand queueCommand(UUID userDeviceGuid, byte[] encryptedCommand);

    /**
     * Execute a command.
     * @param queuedCommandId the command to be executed
     * @param commandRequestExecutor the executor to turn a {@link com.ecco.security.dom.CommandRequest} into a {@link com.ecco.security.dom.CommandResponse}
     * @return the results of executing the command
     * @throws IllegalStateException if the command is not in a state to be executed
     */
    @NonNull
    QueuedCommand executeCommand(Long queuedCommandId, CommandRequestExecutor commandRequestExecutor);

    /**
     * Dismiss a command.
     * @param queuedCommandId the command to be dismissed
     * @return the results of dismissing the command
     * @throws IllegalStateException if the command is not in a state to be executed
     */
    @NonNull
    QueuedCommand dismissCommand(Long queuedCommandId);

    /**
     * Archive a command.
     * @param queuedCommandId the command to be archived
     * @return the results of dismissing the command
     * @throws IllegalStateException if the command is not in a state to be executed
     */
    @NonNull
    QueuedCommand archiveCommand(Long queuedCommandId);
}
