package com.ecco.service.security;

import com.ecco.infrastructure.web.CapturingResponseWrapper;
import com.ecco.security.dom.CommandRequest;
import com.ecco.security.dom.CommandResponse;
import com.google.common.base.Charsets;
import com.google.common.collect.ImmutableMap;
import com.google.common.net.HttpHeaders;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.ReadListener;
import javax.servlet.RequestDispatcher;
import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.InputStreamReader;
import java.util.Collections;
import java.util.Enumeration;
import java.util.Map;

/**
 * Implements a {@code CommandRequestExecutor} that uses {@link javax.servlet.RequestDispatcher} to forward the commands
 * internally.
 */
public class ForwardingCommandRequestExecutorImpl implements CommandRequestExecutor {
    private final Logger log = LoggerFactory.getLogger(getClass());
    private final HttpServletRequest request;
    private final HttpServletResponse response;

    public ForwardingCommandRequestExecutorImpl(HttpServletRequest request, HttpServletResponse response) {
        this.request = request;
        this.response = response;
    }

    @Override
    public CommandResponse processRequest(final CommandRequest commandRequest) throws Exception {
        final RequestDispatcher requestDispatcher = request.getRequestDispatcher(commandRequest.getUrl().getPath());
        final HttpServletRequestWrapper requestWrapper = new HttpServletRequestWrapper(request) {
            final ByteArrayInputStream inputStream = new ByteArrayInputStream(
                    commandRequest.getBody() != null ? commandRequest.getBody().getBytes(Charsets.UTF_8): new byte[0]);
            final Map<String, String> headers;
            {
                final ImmutableMap.Builder<String, String> builder = ImmutableMap.<String, String>builder()
                        .put(HttpHeaders.CONTENT_TYPE, commandRequest.getContentType());
                if (commandRequest.getAcceptType() != null) {
                    builder.put(HttpHeaders.ACCEPT, commandRequest.getAcceptType());
                }
                headers = builder.build();
            }

            @Override
            public String getHeader(String name) {
                return headers.get(name);
            }

            @Override
            public Enumeration<String> getHeaders(String name) {
                final String header = getHeader(name);
                return Collections.enumeration(header == null? Collections.emptySet() : Collections.singleton(headers.get(name)));
            }

            @Override
            public Enumeration<String> getHeaderNames() {
                return Collections.enumeration(headers.keySet());
            }

            @Override
            public String getMethod() {
                return commandRequest.getMethod();
            }

            @Override
            public BufferedReader getReader() {
                return new BufferedReader(new InputStreamReader(getInputStream(), Charsets.UTF_8));
            }

            @Override
            public ServletInputStream getInputStream() {
                return new ServletInputStream() {
                    private boolean finished = false;

                    @Override
                    public int read() {
                        int data = inputStream.read();
                        if (data == -1) {
                            this.finished = true;
                        }
                        return data;
                    }

                    @Override
                    public boolean isFinished() {
                        return finished;
                    }

                    @Override
                    public boolean isReady() {
                        return true;
                    }

                    @Override
                    public void setReadListener(ReadListener readListener) {
                        throw new IllegalStateException("Doesn't support async");
                    }
                };
            }
        };
        final CapturingResponseWrapper responseWrapper = new CapturingResponseWrapper(response);

        log.debug("processRequest: " + commandRequest);

        requestDispatcher.forward(requestWrapper, responseWrapper);

        log.debug("response: " + responseWrapper.getStatus());

        // Assume the response is UTF-8.
        // This is safe because JSON is required to use a Unicode encoding,
        // and in practice Spring will always produce UTF-8.
        return CommandResponse.BuilderFactory.create()
                .statusCode(responseWrapper.getStatus())
                .body(new String(responseWrapper.getBody(), Charsets.UTF_8))
                .build();
    }

}
