package com.ecco.service.security;

import com.ecco.security.dom.UserDevice;

import java.net.InetAddress;
import java.util.UUID;

/**
 * Manages encryption keys for user-devices.
 *
 * @since 16/01/2014
 */
public interface EncryptionManagerService {
    String CIPHER_AES_GCM = "AES-GCM";
    String CIPHER_NONE = "NONE";

    /**
     * Looks up a user-device using the supplied user-device identifier.
     * If the user-device cannot be found or is not valid, then returns null.
     *
     * @param guid a user-device identifier
     * @return the user-device, or null if no valid device was found
     */
    UserDevice findValidatedDevice(UUID guid);

    /**
     * Creates a new user-device record for the current user with the supplied descriptive details.
     *
     * @param description optional human-readable description of the device
     * @param userAgent user agent string from the browser which initiated the request to create the user-device
     * @param ipAddress IP address of the host which initiated the request to create the user-device
     * @return the created user-device
     * @throws org.springframework.security.authentication.AuthenticationCredentialsNotFoundException if there is no current user
     */
    UserDevice createDeviceForCurrentUser(String description, String userAgent, InetAddress ipAddress);

    /**
     * Ensures that a user-device has a valid key.
     * If the user-device has not been invalidated then this method just returns the current record, otherwise
     * a new key is created and the user-device is marked as valid.
     *
     * @param guid a user-device identifier
     * @return the user-device, guaranteed to be valid
     * @throws IllegalArgumentException if no device was found matching the supplied identifier
     */
    UserDevice revalidateDevice(UUID guid);

    /**
     * Marks a user-device as invalid, ensuring it will not be returned by {@link #findValidatedDevice(java.util.UUID)}
     * in future.
     *
     * @param device the user-device to invalidate
     * @param reason optional reason to be stored in place of the previous user-device description
     * @return the user-device, guaranteed to be invalid
     */
    UserDevice invalidateDevice(UserDevice device, String reason);

    /**
     * Decrypts data for a user-device using the key in the record provided (NB not the stored key).
     *
     * @param device the user-device for which to decrypt data
     * @param encryptedData the data to be decrypted
     * @return the decrypted data
     * @throws IllegalArgumentException if the encrypted data could not be decrypted successfully using the user-device key
     */
    byte[] decryptData(UserDevice device, byte[] encryptedData);

    /**
     * Encrypts data for a user-device using the key in the record provided (NB not the stored key).
     *
     * @param device the user-device for which to decrypt data
     * @param decryptedData the data to be encrypted
     * @return the encrypted data
     * @throws IllegalArgumentException if the decrypted data could not be encrypted successfully using the user-device key
     */
    byte[] encryptData(UserDevice device, byte[] decryptedData);
}
