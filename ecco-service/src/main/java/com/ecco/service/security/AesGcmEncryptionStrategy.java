package com.ecco.service.security;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.security.GeneralSecurityException;
import java.security.SecureRandom;
import java.security.spec.AlgorithmParameterSpec;

/**
 * Encryption with AES-GCM.
 * The encrypted data consists of a 16-byte random IV followed by the actual ciphertext. This ensures that a different
 * ciphertext will be produced even for the same key and plaintext.
 */
public class AesGcmEncryptionStrategy implements EncryptionStrategy {
    private static final Logger log = LoggerFactory.getLogger(AesGcmEncryptionStrategy.class);
    private static final int GCM_TAG_BITS = 128;

    @Override
    public boolean supports(String cipher) {
        return cipher.equals(EncryptionManagerService.CIPHER_AES_GCM);
    }

    @Override
    public byte[] encrypt(String cipherName, byte[] key, byte[] data) {
        try {
            Cipher cipher = createCipher();
            final SecureRandom random = new SecureRandom();
            final byte[] iv = new byte[cipher.getBlockSize()];
            random.nextBytes(iv);
            cipher.init(Cipher.ENCRYPT_MODE, new SecretKeySpec(key, "AES"), constructParamSpec(iv));
            final byte[] buffer = new byte[cipher.getOutputSize(data.length)];
            final int outputSize = cipher.doFinal(data, 0, data.length, buffer);
            final byte[] encryptedData = new byte[iv.length + outputSize];
            System.arraycopy(iv, 0, encryptedData, 0, iv.length);
            System.arraycopy(buffer, 0, encryptedData, iv.length, outputSize);
            return encryptedData;
        } catch (GeneralSecurityException e) {
            throw new IllegalStateException(e);
        }
    }

    @Override
    public byte[] decrypt(String cipherName, byte[] key, byte[] encryptedData) {
        try {
            Cipher cipher = createCipher();
            final int blockSize = cipher.getBlockSize();
            cipher.init(Cipher.DECRYPT_MODE, new SecretKeySpec(key, "AES"), constructParamSpec(encryptedData, blockSize));
            return cipher.doFinal(encryptedData, blockSize, encryptedData.length - blockSize);
        } catch (GeneralSecurityException e) {
            throw new IllegalStateException(e);
        }
    }

    Cipher createCipher() throws GeneralSecurityException {
        return Cipher.getInstance("AES/GCM/NoPadding");
    }

    /**
     * Build an <code>AlgorithmParameterSpec</code> instance used to initialize a <code>Cipher</code> instance
     * for AES-GCM block cipher encryption.
     * Falls back to IvParameterSpec if GCMParameterSpec from JDK8 on fails (it shouldn't - we're JDK11+ now).
     *
     * @param iv the initialization vector
     * @return the newly constructed AlgorithmParameterSpec instance, appropriate for the JDK version.
     */
    private AlgorithmParameterSpec constructParamSpec(byte[] iv) {
        // Same as: new GCMParameterSpec(GCM_TAG_BITS, iv)
        log.trace("JDK8 or higher: Attempting to create GCMParameterSpec");
        try {
            var gcmSpecClass = Class.forName("javax.crypto.spec.GCMParameterSpec");
            AlgorithmParameterSpec gcmSpec = (AlgorithmParameterSpec) gcmSpecClass.getConstructor(int.class, byte[].class)
                    .newInstance(GCM_TAG_BITS, iv);
            log.trace("Successfully created GCMParameterSpec");
            return gcmSpec;
        } catch (Exception e) {
            log.debug("Failed to create GCMParameterSpec, falling back to returning IvParameterSpec", e);
            return new IvParameterSpec(iv);
        }
    }

    /**
     * Build an <code>AlgorithmParameterSpec</code> instance used to initialize a <code>Cipher</code> instance
     * for AES-GCM block cipher decryption.
     * Falls back to IvParameterSpec if GCMParameterSpec from JDK8 on fails (it shouldn't - we're JDK11+ now).
     *
     * @param encryptedData the encrypted data
     * @param blockSize the block size (from Cipher)
     * @return the newly constructed AlgorithmParameterSpec instance, appropriate for the JDK version.
     */
    private AlgorithmParameterSpec constructParamSpec(byte[] encryptedData, int blockSize) {
        // Same as: new GCMParameterSpec(GCM_TAG_BITS, encryptedData, 0, blockSize);
        log.trace("JDK8 or higher: Attempting to create GCMParameterSpec");
        try {
            var gcmSpecClass = Class.forName("javax.crypto.spec.GCMParameterSpec");
            AlgorithmParameterSpec gcmSpec = (AlgorithmParameterSpec) gcmSpecClass.getConstructor(int.class, byte[].class, int.class, int.class)
                    .newInstance(GCM_TAG_BITS, encryptedData, 0, blockSize);
            log.trace("Successfully created GCMParameterSpec");
            return gcmSpec;
        } catch (Exception e) {
            log.debug("Failed to create GCMParameterSpec, falling back to returning IvParameterSpec", e);
            return new IvParameterSpec(encryptedData, 0, blockSize);
        }

    }
}
