package com.ecco.service;

import java.util.List;
import com.ecco.calendar.core.Entry;
import org.joda.time.DateTime;

import com.ecco.dom.ClientDetail;
import com.ecco.dom.Referral;
import com.ecco.dto.ClientDefinition;
import com.ecco.dto.ReferralRelationshipDto;
import com.ecco.infrastructure.annotations.WriteableTransaction;
import org.springframework.ui.ModelMap;

@WriteableTransaction
public interface ReferralService {

    /**
     * Returns referrals for clients matching the specified example.
     * Currently last name is mandatory and first name is optional, both are used as prefix searches.
     * All other fields are ignored.
     * @param exemplar the client definition to match
     * @param includeHidden true if hidden referrals should also be returned (by disabling the
     *          <code>{@value com.ecco.infrastructure.hibernate.HibFilterNames#HIDEABLE_ENTITY_FILTER_NAME}</code>).
     */
    Iterable<Referral> queryReferralsByClientExample(ClientDefinition exemplar, boolean includeHidden);

    Referral getReferral(Long id);

    ModelMap getLegacyReferralDetails(long referralId);

    Long getPrimaryReferralId(Referral referral);

    Long getReferralId(Entry entry);

    List<ReferralRelationshipDto> getRelationshipReferralsDto(long primaryReferralId);

    void setReferralWithInterviewDate(Referral referral);

    Long setNewClient(ClientDetail client);
    void setReferral(Referral referral);

    List<Referral> getCustomProperties(List<Long> id, Long serviceId);
    void setCustomProperties(Referral referral);
    void setPropertyTimestamp(long referralId, String property, DateTime datetime);

    void moveReferralToService(long referralId, long serviceId);
    void moveReferralToClient(long referralId, long clientId);
    void hideReferral(long referralId);
    void unhideReferral(long referralId);
    void hideReferralByServiceRecipientId(int serviceRecipientId);
    void unhideReferralByServiceRecipientId(int serviceRecipientId);

}
