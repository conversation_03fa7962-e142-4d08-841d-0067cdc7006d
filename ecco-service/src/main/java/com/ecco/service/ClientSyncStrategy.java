package com.ecco.service;

import java.net.URI;

import com.ecco.config.service.ExternalSystemService.ApiType;
import com.ecco.dto.ClientDefinitionCommandDto;
import com.ecco.dto.ClientEvent;
import com.ecco.dto.FlagsDefinitionCommandDto;
import com.ecco.evidence.event.EvidenceCommentSavedEvent;
import com.ecco.dto.ClientDefinition;

/**
 * Defines a strategy for writing clients to an external system.
 */
public interface ClientSyncStrategy {

    boolean supports(ApiType apiType);

    ClientDefinition syncClient(String systemName, ApiType apiType, URI uri, ClientDefinition client);

    String syncClientUpdate(String systemName, ApiType apiType, URI uri, ClientDefinitionCommandDto cmd);

    String syncEvidenceComment(String systemName, ApiType apiType, URI uri, ClientDefinition client,
                               EvidenceCommentSavedEvent event, ClientEvent.NoteType noteType);

    Boolean syncEvidenceFlags(String externalSourceName, ApiType apiType, URI uri, FlagsDefinitionCommandDto cmd);
}
