package com.ecco.service;

import com.ecco.dom.EvidenceGroup;
import com.ecco.evidence.EvidenceTask;
import com.ecco.infrastructure.annotations.WriteableTransaction;
import com.ecco.serviceConfig.dom.TaskDefinition;
import com.ecco.serviceConfig.repositories.TaskDefinitionRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("taskDefinitionService")
@WriteableTransaction
public class TaskDefinitionServiceImpl implements TaskDefinitionService {

    @Autowired
    private TaskDefinitionRepository repository;

    @Override
    @Cacheable(cacheNames = "taskDefinitionService")
    public List<TaskDefinition> getTaskDefinitions() {
        return repository.findAll();
    }

    @Override
    @Cacheable(cacheNames = "taskDefinitionService", key = "#name")
    public TaskDefinition findOneByNameIgnoreCase(String name) {
        return repository.findOneByNameIgnoreCase(name);
    }

    @Override
    @Cacheable(cacheNames = "taskDefinitionService", key = "#id")
    public TaskDefinition findOneById(long id) {
        return repository.findOneById(id);
    }

    /**
     * Determine the EvidenceGroup from the name.
     * Known groups are first checked, then individual taskdefinitions use their name as the task and group names.
     * NB This matches ecco-evidence/domain.ts fromTaskName where (currently) any task forms part of its respective group,
     * except for NEEDS_NOTSHARED.
     */
    @Override
    public EvidenceGroup findGroupFromGroupName(String evidenceGroupName) {
        String evidenceGroupNameUpper = evidenceGroupName.toUpperCase();

        // EVIDENCE_SUPPORT items share history called 'NEEDS' (except for supportStaffNotes and managerNotes)
        // EVIDENCE_RISK items share history called 'THREAT'
        switch (evidenceGroupNameUpper) {
            case "AUDITONLY":
                return EvidenceGroup.AUDITONLY;
            case "NEEDS":
                return EvidenceGroup.NEEDS;
            case "THREAT":
                return EvidenceGroup.THREAT;
            case "CHECKLIST":
                return EvidenceGroup.CHECKLIST;
            case "SUPPORTSTAFFNOTES":
                return EvidenceGroup.SUPPORTSTAFFNOTES;
            case "ENGAGEMENTCOMMENTS":
                return EvidenceGroup.ENGAGEMENTCOMMENTS;
            case "MANAGERNOTES":
                return EvidenceGroup.MANAGERNOTES;
        }

        // evidenceGroupName could be arbitrary - its just to group related history,
        // however, below we have ra.getType which implies it has to match an existing taskName
        // which is useful anyway so we can return an expected EvidenceGroup
        var ra = findOneByNameIgnoreCase(evidenceGroupName);

        // check if we have a shared history
        switch (ra.getType()) {
            case EVIDENCE_SUPPORT:
                // double check we are not part of standard shared history...
                if (EvidenceGroup.NEEDS_NOTSHARED.stream().noneMatch(n -> n.equalsIgnoreCase(evidenceGroupNameUpper))) {
                    return EvidenceGroup.NEEDS;
                }
                break;
            case EVIDENCE_RISK:
                return EvidenceGroup.THREAT;
            case AUDITONLY:
                return EvidenceGroup.AUDITONLY;
            case EVIDENCE_CHECKLIST:
                return EvidenceGroup.CHECKLIST;
        }

        // if we are not a shared history, then use the group name as task name to create the group
        return EvidenceGroup.fromGroupIdName(evidenceGroupNameUpper, ra.getId());
    }

    @Override
    public TaskDefinition.Type getTaskType(EvidenceTask task) {
        return findOneByNameIgnoreCase(task.getTaskName()).getType();
    }

    @Override
    public boolean isCustomFormBased(TaskDefinition.Type type) {
        return type == TaskDefinition.Type.EVIDENCE_CUSTOMFORM ||
                type == TaskDefinition.Type.AGREEMENT;
    }

    @Override
    public boolean isAuditBased(TaskDefinition.Type type) {
        return type == TaskDefinition.Type.AUDITONLY;
    }

    @Override
    public boolean isQuestionnaireBased(TaskDefinition.Type type) {
        return type == TaskDefinition.Type.EVIDENCE_QUESTIONNAIRE;
    }

    @Override
    public boolean isThreatBased(TaskDefinition.Type type) {
        return type == TaskDefinition.Type.EVIDENCE_RISK;
    }

    @Override
    public boolean isSupportSmartStepBased(TaskDefinition.Type type) {
        return type == TaskDefinition.Type.EVIDENCE_SUPPORT ||
               type == TaskDefinition.Type.EVIDENCE_ROTA ||
               type == TaskDefinition.Type.EVIDENCE_CHECKLIST;
    }

    @Override
    public boolean isRotaBased(TaskDefinition.Type type) {
        return type == TaskDefinition.Type.EVIDENCE_ROTA;
    }

    @Override
    public boolean isChecklistBased(TaskDefinition.Type type) {
        return type == TaskDefinition.Type.EVIDENCE_CHECKLIST;
    }

    @Override
    public boolean isDedicatedBased(TaskDefinition.Type type) {
        return type == TaskDefinition.Type.DEDICATED_TASK;
    }

    @Override
    public boolean isCustomFormAllowed(String name, TaskDefinition.Type type) {
        if (type == TaskDefinition.Type.EVIDENCE_CUSTOMFORM) {
            return true;
        }
        if (type == TaskDefinition.Type.DEDICATED_TASK) {
            return "referralDetails".equals(name) || "waitingListCriteria".equals(name);
        }
        return false;
    }

}
