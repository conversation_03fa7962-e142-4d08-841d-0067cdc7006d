package com.ecco.service

import com.ecco.dao.ReferralRepository
import com.ecco.dom.ReferralServiceRecipient
import com.ecco.evidence.repositories.ServiceRecipientRepository
import com.ecco.infrastructure.hibernate.AntiProxyUtils
import com.ecco.serviceConfig.EntityRestrictionService
import com.ecco.serviceConfig.service.RepositoryBasedServiceCategorisationService
import com.ecco.servicerecipient.ServiceRecipientSummary
import com.ecco.servicerecipient.ServiceRecipientSummaryService
import org.springframework.stereotype.Service
import org.springframework.util.Assert

@Service
open class ServiceRecipientSummaryServiceImpl(
    val referralRepository: ReferralRepository,
    val serviceRecipientRepository: ServiceRecipientRepository,
    val entityRestrictionService: EntityRestrictionService,
    var serviceCategorisationService: RepositoryBasedServiceCategorisationService,
) : ServiceRecipientSummaryService {

    override fun findAndVerifyAccess(srId: Int): ServiceRecipientSummary {
        val sr = findOne(srId)

        EntityRestrictionService.verifyAccess(
            sr.serviceRecipientId,
            sr.serviceIdAcl,
            sr.projectIdAcl,
            entityRestrictionService,
            serviceCategorisationService,
        )

        return sr
    }

    override fun findOne(srId: Int): ServiceRecipientSummary {
        val referral = referralRepository.findOneReferralSummaryByServiceRecipientId(srId)
        if (referral != null) {
            return referral
        }
        var recipient = serviceRecipientRepository.findById(srId).orElseThrow()
        recipient = AntiProxyUtils.deproxy(recipient) // FIXME: This is for recipient.getAssignedServiceId()
        // that Kotlin is clearly avoiding doing properly
        Assert.isTrue(recipient !is ReferralServiceRecipient, "should not be a referral")
        return ServiceRecipientSummary().mapFrom(recipient)
    }
}