package com.ecco.service;

import com.ecco.calendar.CombinedEntry;
import com.ecco.dao.CustomEventRecurringRepository;
import com.ecco.dao.CustomEventRepository;
import com.ecco.dao.DemandScheduleRepository;
import com.ecco.dao.EvidenceSupportWorkRepository;
import com.ecco.dom.ContactImpl;
import com.ecco.dom.CustomEventImpl;
import com.ecco.dom.CustomEventRecurringImpl;
import com.ecco.dom.agreements.DemandSchedule;
import com.ecco.infrastructure.annotations.WriteableTransaction;
import com.ecco.infrastructure.hibernate.EntityUriMapper;
import com.ecco.infrastructure.time.JodaToJDKAdapters;
import com.ecco.security.SecurityUtil;
import com.ecco.security.repositories.ContactRepository;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.ecco.calendar.core.CalendarEntries;
import com.ecco.calendar.core.CalendarService;
import com.ecco.calendar.core.Entry;
import com.ecco.calendar.core.Recurrence.RecurrenceHandle;
import com.ecco.calendar.core.RecurringEntry.RecurringEntryHandle;
import com.ecco.calendar.dom.EventEntry;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.joda.time.Interval;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import org.jspecify.annotations.Nullable;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Handles the non-iCal and iCal events for non-recurring entities, then decorates them with our own information.
 * For recurring entries, see CalendarEntryCommandSupportHandler - although, it may be better to move overlapping concerns here.
 *
 * The decorating of events done here is before EventController then applies the haeteos-style EventResourceAssembler
 * decorating (which actually overrides some settings like type Other and status Unknown, but uses them to create haeteos links).
 * Actually, the decoration here is minimal now (amounts to just correcting entityUri's) so the two CombinedEntry's
 * do not overlap. Which means the decoration on what is provided is through the controller - using EventResourceAssembler.
 * The EventResourceAssembler expects the iCal event to exist, but optional on the local event - so we only need to fill in
 * some of the properties that are decorated (eg serviceRecipientId, eventStatus - always Unknown, eventType - Interview etc,
 * eventCategory, eventStatusRateId.
 *
 *
 * We should probably keep the duplicate information in the events & events_contacts table because:
 *     +ve - non-recurring events may make more sense in our own linear table for easy/simple retrieval
 *     +ve - it could be used to create an event which itself is a type of reminder? (but could use VALARM)
 *     +ve - its already done and working
 *     -ve - duplicate
 * However, it might be possible to merge into a standard calendar if desired, by bringing these properties across:
 *  - EventType: Review, Interview etc - but this could be an event Category?
 *  - isGenerated: indicates if the event can be edited or not from the normal calendar functions - not required with hateos links
 *  - EventStatus (of attendee): DNA, Rescheduled, etc - could add to "Participation Status" using x- (see https://tools.ietf.org/html/rfc5545#page-21)
 * Pushing through the implementation onto a calendaring system is not ideal, especially if expectations change.
 * We should, however, update the calendaring system if we can - eg for Attended or DNA, set COMPLETED or DECLINED - see ical PartStat.
 */
@Service("eventService")
@WriteableTransaction
public class EventServiceImpl implements EventService {

    @Autowired
    private CalendarService calendarService;

    @Autowired
    private DemandScheduleRepository demandScheduleRepository;

    @Autowired
    private EntityUriMapper entityUriMapper;

    @Autowired
    private EvidenceSupportWorkRepository supportWorkRepository;

    @Autowired
    private CustomEventRecurringRepository recurringRepository;

    @Autowired
    private CustomEventRepository customRepository;

    @Autowired
    private ContactRepository contactRepository;

    @Override
    public CombinedEntry getEntry(String uuid) {
        CombinedEntry ce = new CombinedEntry();

        // TODO there is also getRecurringEntry and getRecurringEntryByIcalUid and more
        Entry entry = calendarService.findEntry(uuid);
        decorateCalendarEntry(entry);
        ce.setIcalEntry(entry);

        ce.setWorkUuid(supportWorkRepository.findUuidByEventId(uuid));

        List<EventEntry> events = getEventEntrysFromUids(Lists.newArrayList(uuid));
        if (events.size() == 1) {
            ce.setEventEntry(events.get(0));
            decorateCalendarEntryWithEvent(ce);
        }

        return ce;
    }

    private List<EventEntry> getEventEntrysFromUids(List<String> uids) {
        var events = customRepository.findAllByUidIn(uids);

        // now filter out the ones we loaded, because the rest are going to be recurring entries that remain
        // NB modified/visited recurring items will be removed from the master recurrance and simply created individually
        Set<String> eventUuids = events.stream().map(EventEntry::getUid).collect(Collectors.toSet());
        uids.removeAll(eventUuids);
        // get the master entries because that is what the information is stored under
        Set<String> masterEventUuids = uids.stream().map(e ->
                calendarService.getEntryHandleFromRecurrenceHandle(RecurrenceHandle.fromString(e)).toString())
                .collect(Collectors.toSet());
        for (String uid : masterEventUuids) {
            Optional<CustomEventRecurringImpl> recurring = recurringRepository.findOneByUid(uid);
            recurring.ifPresent(events::add);
        }
        return events;
    }

    @Override
    public Collection<CombinedEntry> getCalendars(List<String> calendarIds, DateTime start, DateTime end) {
        final Interval interval = new Interval(start, end);
        final Set<CalendarEntries> entries = calendarService.findEntries(Sets.newHashSet(calendarIds), interval);

        Map<String, CombinedEntry> entriesMap = new HashMap<>(entries.size() * (interval.toPeriod().getDays() + 1));
        for (CalendarEntries calendarEntries : entries) {
            for (Entry entry : calendarEntries.getEntries()) {
                decorateCalendarEntry(entry); // fix up setManagedByUri if it was wrong
                UUID workUuid = supportWorkRepository.findUuidByEventId(entry.getItemUid());
                entriesMap.put(entry.getItemUid(), new CombinedEntry(entry, null, workUuid, calendarEntries.getCalendarId()));
            }
        }

        // Add any relevant events dealt with in that way - similar to OutputConverterEvent.loadAssociatedData
        final List<EventEntry> events = getEventEntrysFromUids(Lists.newArrayList(entriesMap.keySet()));
        for (String uid : entriesMap.keySet()) {
//
            Optional<EventEntry> event = events.stream().filter(e -> e.getUid().equalsIgnoreCase(
                    calendarService.getEntryHandleFromRecurrenceHandle(RecurrenceHandle.fromString(uid)).toString()))
                    .findFirst();
            if (event.isPresent()) {
                CombinedEntry ce = entriesMap.get(uid);
                ce.setEventEntry(event.get());
                decorateCalendarEntryWithEvent(ce);
            }
        }

        return entriesMap.values();
    }

    @Override
    public Collection<CombinedEntry> getNearbyCalendarEventsForContact(String calendarId, @Nullable LocalDate nearby) {
        List<String> calendarIds = Collections.singletonList(calendarId);
        var dateOrToday = nearby != null ? JodaToJDKAdapters.localDateToJoda(nearby) : org.joda.time.LocalDate.now();
        DateTime start = dateOrToday.minusDays(3).toDateTimeAtStartOfDay();
        DateTime end = start.plusWeeks(2).withTimeAtStartOfDay();

        return getCalendars(calendarIds, start, end);
    }

    @Override
    public String getNameFromCalendarId(String calendarId) {
        return calendarId == null
                ? null
                : contactRepository.findByCalendarId(calendarId).map(ContactImpl::getName).orElse(null);
    }

    @Override
    public CustomEventImpl persistAndSyncAddNonRecurringCalendarEntry(CustomEventImpl e) {
        e.setUid(calendarService.getUUID());

        e = customRepository.save(e);

        var ownerCalendarId= e.getOwnerCalendarIdForNewEvent() != null ? e.getOwnerCalendarIdForNewEvent() : SecurityUtil.getAuthenticatedUserCalendarId();
        calendarService.postEntry(e, ownerCalendarId, SecurityUtil.getAuthenticatedUsername(), DateTimeZone.UTC);
        var uid = e.getUid();
        e.getNewCalendarIds().forEach(calendarId -> {
            // the owner is already added by cosmo as a parent/attachment in postEntry
            if (!StringUtils.equals(calendarId, ownerCalendarId)) {
                calendarService.addAttendeesToEntry(calendarId, uid);
            }
        });

        return e;
    }

    @Override
    public void persistAndSyncRemoveNonRecurringCalendarEntry(CustomEventImpl e) {
        customRepository.delete(e);
        calendarService.deleteEntry(e);
    }

    @Override
    public void persistAndSyncUpdateNonRecurringCalendarEntry(CustomEventImpl e) {
        e = customRepository.save(e);

        calendarService.putEntry(e, SecurityUtil.getAuthenticatedUsername(), DateTimeZone.UTC);
        var uid = e.getUid();
        e.getNewCalendarIds().forEach(calendarId ->
            calendarService.addAttendeesToEntry(calendarId, uid)
        );
    }

    /**
     * NB This is a DUPLICATE to avoid a reference to RotaService - see RotaService#getAppointmentSchedule
      */
    private DemandSchedule determineDemandScheduleFromRotaRef(String eventRef) {
        RecurringEntryHandle ref = calendarService.getEntryHandleFromAnyHandle(eventRef);
        return demandScheduleRepository.findOneByEntryHandleAsString(ref.toString()).orElse(null);
    }

    /**
     * Adds information to a calendar entry which wasn't specified on the entry in Cosmo, but can be deduced from the
     * other information. This is for legacy data.
     *
     * @param entry the entry we want to try to decorate
     */
    private void decorateCalendarEntry(Entry entry) {
        // If the cosmo managed by is empty, try to fix it
        if (entry.getManagedByUri() == null) {
            // If this is a recurrence, then the managing entity must be an AppointmentSchedule, so we'll find it.
            assignManagedByUriForRotaEntry(entry);

        // This is a duff managed-by URI on a rota entry; fix it.
        // See DEV-14
        } else if (entry.getManagedByUri().getPath().equals("/null") && DemandSchedule.class.isAssignableFrom(entityUriMapper.entityClassForUri(entry.getManagedByUri()))) {
            assignManagedByUriForRotaEntry(entry);
        }
    }

    private void assignManagedByUriForRotaEntry(Entry entry) {
        final DemandSchedule ds = determineDemandScheduleFromRotaRef(entry.getItemUid());
        if (ds != null) {
            entry.setManagedByUri(entityUriMapper.uriForEntity(ds));
        }
    }

    /**
     * Adds information to the calendar entries which wasn't actually on the entry in Cosmo. This can be for information
     * about a past event - for which the iCal spec doesn't cater well for. However, it can augment the event with more
     * helpful information to allow better logic or viewing the event from different perspectives (eg client).
     */
    private void decorateCalendarEntryWithEvent(CombinedEntry entry) {

        // if its null now then its from the EventEntry as we've already had a stab at adding rota links etc (see decorateCalendarEntry above)
        if (entry.getIcalEntry().getManagedByUri() == null) {
            entry.getIcalEntry().setManagedByUri(entityUriMapper.uriForEntity(entry.getEventEntry()));
        }

        // NB the fudge in this commit is a result of the below, which could be looked at
        // allDay
        // ======
        // allDay is set from EventResourceAssembler.populateFromICal and EntryConverter.convert so we no longer need an override here.
        // Removing the override keeps the cosmo ical entry separated from an ecco Event - in that data is duplicated or in addition to,
        // rather than overlapping logic on similar properties.

        // There was an allDay set here from commit 9b65a57f (21/11/13) when the endDate needed changing (millis-1)
        // since "GOTCHA! Calendar client expects end dates to be inclusive whereas calendar API expects them to be exclusive".
        // Presumably the millis-1 disturbed the allDay - although the end date shouldn't change the checks on the start date (see ItemEventConverterImpl.convertItemEvent)
        // However, we removed the millis-1 - see EventResourceAssembler.populateFromICal, and can therefore we can now remove the allDay override.
        // Furthermore, the EntryConverter.convert now looks for an anyTime or 'date time' and sets allDay accordingly.
        // Therefore we should not need to override with the EventEntry.

    }

}
