package com.ecco.service;

import java.util.*;

import com.ecco.calendar.dom.EventType;
import com.ecco.config.dom.ListDefinitionEntry;
import com.ecco.config.repositories.ListDefinitionRepository;
import com.ecco.dom.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import com.ecco.infrastructure.annotations.WriteableTransaction;
import com.ecco.dom.IdName;

import static java.util.stream.Collectors.toMap;

@Service("idNameService")
@WriteableTransaction
public class IdNameServiceImpl implements IdNameService {

    // either a setter or a clear method is required to clear the cache
    // since these are only ui-set, we clearFromCache via IdNameTypeDefinition
    @Override
    @CacheEvict(value="idNames", key = "#clazz")
    public void clearFromCache(Class<?> clazz) {
        return;
    }

    // needed to make Class<IdName> more generic - worked from webflow, not code
    @Override
    @Cacheable(value = "idNames", key = "#clazz")
    public List<IdName> getIdNames(Class<?> clazz) {

        if (clazz.isAssignableFrom(EventType.class)) {
            List<IdName> types = new ArrayList<IdName>(4);
            types.add(EventType.Other);
            types.add(EventType.Target);
            types.add(EventType.Meeting);
            types.add(EventType.Review);
            return types;
        }

        if (clazz.isAssignableFrom(YesNoDontKnow.class)) {
            List<IdName> yndk = new ArrayList<IdName>(3);
            yndk.add(YesNoDontKnow.Yes);
            yndk.add(YesNoDontKnow.No);
            yndk.add(YesNoDontKnow.DontKnow);
            return yndk;
        }

        if (clazz.isAssignableFrom(PreferredContactMethod.class)) {
            List<IdName> cms = new ArrayList<IdName>(5);
            cms.add(PreferredContactMethod.Mobile);
            cms.add(PreferredContactMethod.Email);
            cms.add(PreferredContactMethod.Letter);
            cms.add(PreferredContactMethod.Landline);
            cms.add(PreferredContactMethod.Sms);
            return cms;
        }

        /*
        if (clazz.isAssignableFrom(Language.class)) {
            List<Language> languages = LanguageUtils.getLanguages();
            List<IdName> languagesIdName = new ArrayList<IdName>(languages.size());
            for (Iterator iterator = languages.iterator(); iterator.hasNext();) {
                Language language = (Language) iterator.next();
                com.ecco.dom.IdName lang = new com.ecco.dom.IdName(language.getId(), language.getFriendlyName());
                //languagesIdName.add(language);
            }
            languagesIdName.add(0, getBlank());
            return languagesIdName;
        }
        */

        // for the rest, we load from the db so that all idNames can use this service
        return Collections.emptyList();
    }

    @Autowired
    private ListDefinitionRepository listDefinitionRepository;

    // Used from SpEL in InboundReferralResource
    public Map<String, String> findAllInboundLanguages() {
        return this.listDefinitionRepository.findByListName(ClientDetailAbstract.LANGUAGE_LISTNAME)
                .stream()
                .collect(toMap(entity -> entity.getId().toString(), ListDefinitionEntry::getName));
    }

    // Used from SpEL in InboundReferralResource
    public Map<String, String> findAllInboundEthnicOrigins() {
        return this.listDefinitionRepository.findByListName(ClientDetailAbstract.ETHNICORIGIN_LISTNAME)
                .stream()
                .collect(toMap(entity -> entity.getId().toString(), ListDefinitionEntry::getName));
    }

    // Used from SpEL in InboundReferralResource
    public Map<String, String> findAllInboundReligions() {
        return this.listDefinitionRepository.findByListName(ClientDetailAbstract.RELIGION_LISTNAME)
                .stream()
                .collect(toMap(entity -> entity.getId().toString(), ListDefinitionEntry::getName));
    }
}
