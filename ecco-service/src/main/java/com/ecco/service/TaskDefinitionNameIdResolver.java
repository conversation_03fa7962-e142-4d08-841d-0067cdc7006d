package com.ecco.service;

import com.ecco.serviceConfig.dom.TaskDefinition;
import lombok.RequiredArgsConstructor;

import java.util.Optional;
import java.util.OptionalLong;

@RequiredArgsConstructor
public class TaskDefinitionNameIdResolver implements com.ecco.evidence.TaskDefinitionNameIdResolver {

    private final TaskDefinitionService taskDefinitionService;

    @Override
    public OptionalLong resolveNameToId(String taskName) {
        TaskDefinition aspect = taskDefinitionService.findOneByNameIgnoreCase(taskName);
        return aspect == null ? OptionalLong.empty()
                : OptionalLong.of(aspect.getId());
    }

    @Override
    public Optional<String> resolveIdToName(long taskDefIdId) {
        TaskDefinition aspect = taskDefinitionService.findOneById(taskDefIdId);
        return aspect == null ? Optional.empty()
                : Optional.of(aspect.getName());
    }
}
