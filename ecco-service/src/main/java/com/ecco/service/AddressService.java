package com.ecco.service;

import com.ecco.buildings.repositories.AddressRepository;
import com.ecco.dom.AddressedLocation;
import com.ecco.infrastructure.annotations.WriteableTransaction;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service("addressService")
@WriteableTransaction
public class AddressService {

    private final AddressRepository addressRepository;

    @Autowired
    public AddressService(AddressRepository addressRepository) {
        this.addressRepository = addressRepository;
    }

    public AddressedLocation ensureAddress(AddressedLocation addressIn) {

        AddressedLocation address = addressRepository.findOneByPostCodeAndLine1(addressIn.getPostCode(),
                addressIn.getLine1());
        if (address == null) {
            address = addressRepository.save(addressIn);
        } else {
            address.setDisabled(false);
            address = addressRepository.save(address);
        }

        return address;
    }
}
