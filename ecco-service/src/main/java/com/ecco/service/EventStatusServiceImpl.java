package com.ecco.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ecco.dao.EventStatusDao;
import com.ecco.infrastructure.annotations.WriteableTransaction;

@Service("eventStatusService")
@WriteableTransaction
public class EventStatusServiceImpl implements EventStatusService {

    EventStatusDao eventStatusDao;
    @Autowired
    public void setEventStatusDao(EventStatusDao eventStatusDao) {
        this.eventStatusDao = eventStatusDao;
    }

    @Override
    public void setStatusDNA(long eventId, long contactId) {
        eventStatusDao.setStatusDNA(eventId, contactId);
    }

}
