package com.ecco.service;
import com.ecco.dto.DelegateResponse;
import com.ecco.dto.FlagsDefinition;
import com.ecco.infrastructure.annotations.ReadOnlyTransaction;

@ReadOnlyTransaction
public interface EvidenceService {

    /**
     * Returns a map of system names to client flag results.
     * Internal clients will be mapped to {@link ClientDetailService.DEFAULT_CLIENT_SOURCE_SYSTEM}.
     */
    DelegateResponse<FlagsDefinition> queryClientFlags(String externalSourceName, String externalClientRef);

}
