package com.ecco.service;

import com.ecco.infrastructure.annotations.WriteableTransaction;
import com.ecco.serviceConfig.dom.ServiceType;

import java.util.List;

@WriteableTransaction
public interface ServiceTypeService {

    ServiceType getServiceType(long id);

    void deleteServiceType(ServiceType serviceType);

    void setServiceType(ServiceType serviceType);

    List<ServiceType> getServiceTypes();

}
