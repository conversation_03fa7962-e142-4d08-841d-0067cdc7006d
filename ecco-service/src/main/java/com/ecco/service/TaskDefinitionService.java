package com.ecco.service;

import com.ecco.dom.EvidenceGroup;
import com.ecco.evidence.EvidenceTask;
import com.ecco.infrastructure.annotations.WriteableTransaction;
import com.ecco.serviceConfig.dom.TaskDefinition;

import java.util.List;

@WriteableTransaction
public interface TaskDefinitionService {

    List<TaskDefinition> getTaskDefinitions();

    TaskDefinition findOneByNameIgnoreCase(String taskName);

    TaskDefinition findOneById(long id);

    EvidenceGroup findGroupFromGroupName(String name);

    TaskDefinition.Type getTaskType(EvidenceTask task);

    boolean isCustomFormBased(TaskDefinition.Type type);

    boolean isAuditBased(TaskDefinition.Type type);

    boolean isQuestionnaireBased(TaskDefinition.Type type);

    boolean isThreatBased(TaskDefinition.Type type);

    boolean isSupportSmartStepBased(TaskDefinition.Type type);

    boolean isRotaBased(TaskDefinition.Type type);

    boolean isChecklistBased(TaskDefinition.Type type);

    boolean isDedicatedBased(TaskDefinition.Type type);

    boolean isCustomFormAllowed(String name, TaskDefinition.Type type);
}
