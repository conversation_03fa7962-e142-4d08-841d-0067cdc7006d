package com.ecco.service;

import static com.ecco.infrastructure.time.JodaToJavaTimeConverter.toJavaZonedDateTime;

import java.util.List;

import org.joda.time.DateTime;
import com.ecco.dom.ClickStream;
import com.ecco.infrastructure.util.AppContextAware;
import com.opensymphony.clickstream.Clickstream;
import com.opensymphony.clickstream.ClickstreamRequest;
import com.opensymphony.clickstream.logger.ClickstreamLogger;

/**
 * Class which is instantiated by Clickstream and hence is not under spring's control.
 * So we need to access a spring bean to do the saving.
 */
public class ClickStreamLogger implements ClickstreamLogger {

    @Override
    public void log(Clickstream stream) {
        ClickStream csdb = new ClickStream();
        csdb.setSessionId(stream.getSession().getId());
        csdb.setFirstRequest(toJavaZonedDateTime(new DateTime(stream.getStart().getTime())));
        csdb.setLastRequest(toJavaZonedDateTime(new DateTime(stream.getLastRequest().getTime())));
        csdb.setInitialReferrer(stream.getInitialReferrer());
        csdb.setHostName(stream.getHostname());
        csdb.setTotalRequests(stream.getStream().size());
        String username = "";
        List<ClickstreamRequest> requests = stream.getStream();
        if (requests.size() > 0)
            username = ((ClickstreamRequest) stream.getStream().get(stream.getStream().size()-1)).getRemoteUser();

        // sessions get re-created after login, so lots of useless states can exist
        if (username != null) {
            csdb.setUsername(username);
            // had used entityService, but it has an aop of ItemIntegrationAdvice which looks for the current username - but there isn't one when the session times out
            // so we create a service which isn't wrapped by the aop
            ClickStreamService csService = (ClickStreamService) AppContextAware.getBean("clickStreamService");
            if (csService != null)
                csService.setClickStream(csdb);
        }

    }

}
