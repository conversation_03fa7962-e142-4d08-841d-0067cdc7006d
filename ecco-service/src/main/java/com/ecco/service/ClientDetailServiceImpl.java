package com.ecco.service;

import static lombok.AccessLevel.PACKAGE;
import static org.apache.commons.lang3.ArrayUtils.isNotEmpty;

import com.ecco.config.dom.ExternalSystem;
import com.ecco.config.repositories.ExternalSystemRepository;
import com.ecco.dto.ClientDefinition;
import com.ecco.dto.DelegateResponse;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.ImmutableMap;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.Setter;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.client.ResourceAccessException;

import java.io.IOException;
import java.util.Collection;
import java.util.List;
import java.util.Map;

@Service("clientDetailService")
@NoArgsConstructor
@AllArgsConstructor
@Slf4j
class ClientDetailServiceImpl implements ClientDetailService {

    @Setter(PACKAGE)
    @Autowired
    private Collection<ClientImportStrategy> delegates;

    @Autowired
    private ExternalSystemRepository externalSystemRepository;

    @Autowired
    private SearchClientService searchClientService;

    private final ObjectMapper objectMapper = new ObjectMapper();


    @Override
    public Map<String, DelegateResponse<Iterable<ClientDefinition>>> queryClientsByExample(ClientDefinition exemplar, boolean includeExternal) {

        Assert.isTrue( exemplar.getPostCode() != null ||
                (isNotEmpty(exemplar.getAddress()) && exemplar.getAddress()[0] != null) ||
                exemplar.getCode() != null ||
                exemplar.getLastName() != null ||
                exemplar.getExternalClientRef() != null , "One of id, last name, address line 1, postcode or external client ref must be provided");

        final ImmutableMap.Builder<String,DelegateResponse<Iterable<ClientDefinition>>> results
                = ImmutableMap.builder();

        populateLocalMatches(exemplar, includeExternal, results);
        populateExternalMatches(exemplar, includeExternal, results);

        return results.build();
    }

    private void populateLocalMatches(ClientDefinition exemplar, boolean includeExternal, ImmutableMap.Builder<String,
                                      DelegateResponse<Iterable<ClientDefinition>>> results) {
        if (shouldPopulateLocalMatches(exemplar, includeExternal)) {

            List<ClientDefinition> localClientMatches;
            try {
                // NB this only matches on exemplar properties of: code, lastName, firstName, birthDate, gender, addressLine1, postCode
                // (also see other external and demo sources of queryClientsByExample's)
                // and returns a limited projection which is then padded out in ClientDetail::toClientDefinition
                // except the padding misses some details like firstLanguageKey, ethnicity etc for the view
                // (the controller then converts it back to a ClientViewModel via ClientDefinitionToViewModel)
                localClientMatches = searchClientService.getLocalClientMatches(exemplar);
            }
            catch (Exception e) {
                results.put(DEFAULT_CLIENT_SOURCE_SYSTEM, DelegateResponse.fail(e.getMessage()));
                return;
            }

            // security check outside the catch, since here we will be a system error that needs throwing, not a user error that needs catching

            // would be nice to have 'too many matches' check before the below 'in' query, but we can't do that
            // if the user only has one match in their security permissions - so we'd need to implement option 2 in DEV-641
                /* if (localClientMatches.size() > localLimit) {
                    results.put(DEFAULT_CLIENT_SOURCE_SYSTEM, DelegateResponse.fail("too many matches"));
                    return;
                }*/
            final List<ClientDefinition> secureLocalClientMatches = searchClientService.secureLocalClientMatches(localClientMatches);

            results.put(DEFAULT_CLIENT_SOURCE_SYSTEM, DelegateResponse.ok(secureLocalClientMatches));
        }
    }

    /** Search ECCO contacts if:
     * - no external ref provided (REALLY!?  What about wanting to see if the client is already in ECCO?)
     * - or, is local only
     * - or, a code (i.e. ecco c-id) is provided
     */
    private boolean shouldPopulateLocalMatches(ClientDefinition exemplar, boolean includeExternal) {
        return exemplar.getExternalClientRef() == null || !includeExternal || exemplar.getCode() != null;
    }

    private void populateExternalMatches(ClientDefinition exemplar, boolean includeExternal, ImmutableMap.Builder<String, DelegateResponse<Iterable<ClientDefinition>>> results) {
        if (shouldPopulateExternalMatches(exemplar, includeExternal)) {
            validateExemplarForSearch(exemplar);
            List<ExternalSystem> clientSources = externalSystemRepository.findByClientSourceTrue();
            for (ExternalSystem clientSource : clientSources) {
                for (ClientImportStrategy delegate : delegates) {
                    if (delegate.supports(clientSource.getApiType())) {
                        final String name = clientSource.getName();
                        try {
                            final Iterable<ClientDefinition> elements = delegate.queryClients(name, clientSource.getApiType(),
                                    clientSource.getUri(), exemplar);
                            results.put(name, DelegateResponse.ok(elements));
                        } catch (ResourceAccessException e) {
                            results.put(name, DelegateResponse.fail(
                                "This datasource is currently unavailable - please contact support if the problem persists."));
                        } catch (HttpStatusCodeException e) {
                            String message = getMessageText(e, this.objectMapper);
                            results.put(name, DelegateResponse.fail(message));
                        }
                    }
                }
            }
        }
    }

    /** Also search external sources if, include external and no c-id is provided */
    private boolean shouldPopulateExternalMatches(ClientDefinition exemplar, boolean includeExternal) {
        return exemplar.getCode() == null && includeExternal;
    }

    private void validateExemplarForSearch(ClientDefinition exemplar) {
        if (exemplar.getExternalClientRef() != null) {
            Assert.hasText(exemplar.getExternalClientSource());
        }
        else {
            Assert.hasText(exemplar.getLastName(), "last name must be specified");
            Assert.hasText(exemplar.getFirstName(), "first name must be specified");
        }
    }

    protected static String getMessageText(HttpStatusCodeException e, ObjectMapper mapper) {
        try {
            String body = e.getResponseBodyAsString();
            JsonNode tree = mapper.readTree(body);
            return tree.get("message").asText();
        // NB an NPE can occur on 'tree', i.e. when there is a problem when e.getMessage is "408 Request Timeout: [no body]"
        // NB see error source at ControllerIntrospectingTunnelledQueryHandler
        } catch (IOException | NullPointerException e1) {
            return e.getMessage();
        }
    }

}
