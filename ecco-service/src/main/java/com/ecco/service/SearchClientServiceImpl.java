package com.ecco.service;

import com.ecco.config.repositories.ListDefinitionRepository;
import com.ecco.config.service.SettingsService;
import com.ecco.dao.ClientRepository;
import com.ecco.dao.querydsl.EntityRestrictionCommonPredicates;
import com.ecco.dom.*;
import com.ecco.dom.contacts.Address;
import com.ecco.dto.ClientDefinition;
import com.ecco.infrastructure.annotations.WriteableTransaction;
import com.ecco.infrastructure.config.root.EccoEnvironment;
import com.ecco.infrastructure.util.Sortable;
import com.ecco.serviceConfig.EntityRestrictionService;
import com.ecco.calendar.dom.MedDate;
import com.ecco.serviceConfig.service.RepositoryBasedServiceCategorisationService;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.jpa.impl.JPAQuery;
import org.apache.commons.lang3.ArrayUtils;
import org.hibernate.Session;
import org.hibernate.criterion.DetachedCriteria;
import org.hibernate.criterion.Example;
import org.hibernate.criterion.MatchMode;
import org.hibernate.criterion.Order;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

import static com.ecco.dom.QReferral.referral;
import static java.util.stream.Collectors.toList;

@Service("searchClientService")
@WriteableTransaction
public class SearchClientServiceImpl implements SearchClientService {

    @PersistenceContext
    protected EntityManager em;

    private final EccoEnvironment env;
    private final EntityRestrictionService entityRestrictionService;
    private final SettingsService settingsService;
    private final ListDefinitionRepository listDefinitionRepository;
    private final RepositoryBasedServiceCategorisationService serviceCategorisationService;
    private final ClientRepository clientRepository;

    SearchClientServiceImpl() {
        this(null, null, null, null, null, null);
    }

    @Autowired
    public SearchClientServiceImpl(EccoEnvironment env,
                                   EntityRestrictionService entityRestrictionService,
                                   SettingsService settingsService,
                                   ListDefinitionRepository listDefinitionRepository,
                                   RepositoryBasedServiceCategorisationService serviceCategorisationService,
                                   ClientRepository clientRepository) {
        super();
        this.env = env;
        this.entityRestrictionService = entityRestrictionService;
        this.settingsService = settingsService;
        this.listDefinitionRepository = listDefinitionRepository;
        this.clientRepository = clientRepository;
        this.serviceCategorisationService = serviceCategorisationService;
    }

    /**
     * @return ClientDefinition, where the controller then converts it back to a ClientViewModel via ClientDefinitionToViewModel
     */
    @Override
    public List<ClientDefinition> getLocalClientMatches(ClientDefinition client) {

        // code-based search - this wasn't the priority in EncryptedClientDetailByExampleFilter, but was practically
        if (StringUtils.hasText(client.getCode())) {
            return clientRepository.findAllByCode(client.getCode()).stream().map(ClientDetail::toClientDefinition).collect(toList());
        }

        // address-based search
        final String addressLine1 = ArrayUtils.isNotEmpty(client.getAddress()) ? client.getAddress()[0] : null;
        if (StringUtils.hasText(addressLine1) || StringUtils.hasText(client.getPostCode())) {
            return StreamSupport.stream(getResultsFromAddressSearch(addressLine1, client.getPostCode()).spliterator(), false)
                    .map(ClientDetail::toClientDefinition).collect(toList());
        }

        // name-based search, which initially coped with a more advanced search including gender, dob etc
        Assert.hasText(client.getLastName(), "last name is required");
        if (env.shouldEncryptDatabaseFields()) {
            String keyLastName = client.getLastName().substring(0, 1);
            var lastNameStart = Sortable.start(keyLastName);
            var lastNameEnd = Sortable.end(keyLastName);
            BooleanExpression key = QClientDetail.clientDetail.contact.key.goe(lastNameStart)
                    .and(QClientDetail.clientDetail.contact.key.lt(lastNameEnd));

            var results = clientRepository.findAll(key, QClientDetail.clientDetail.contact.key.asc());
            return postProcessResult(client.getFirstName(), client.getLastName(), (List<ClientDetail>) results)
                    .stream().map(ClientDetail::toClientDefinition).collect(toList());
        } else {
            return getResultsFromNameSearch(client.getFirstName(), client.getLastName(), client.getBirthDate(), client.getGenderKey(), addressLine1, client.getPostCode());
        }
    }

    private Iterable<ClientDetail> getResultsFromAddressSearch(String addressLine1, String postcode) {
        // NB Using 'like' assumes the user is supplying the escape characters '%'
        // Using 'containsIgnoreCase' would be better, but using wrapping like with '%' allows users to add their own '%'.
        BooleanExpression line1 = StringUtils.hasText(addressLine1) ? QClientDetail.clientDetail.contact.address.line1.likeIgnoreCase("%" + addressLine1 + "%") : null;
        BooleanExpression pc = StringUtils.hasText(postcode) ? QClientDetail.clientDetail.contact.address.postCode.likeIgnoreCase("%" + postcode + "%") : null;
        BooleanBuilder exp = new BooleanBuilder().or(line1).or(pc);
        var orderBy = QClientDetail.clientDetail.contact.lastName.asc();
        return clientRepository.findAll(exp, orderBy);
    }

    /**
     * We copy this code from the previous filters, and its quite useful perhaps to retain a hibernate criterion example search,
     * but the actual code is only really checking against last name.
     */
    private List<ClientDefinition> getResultsFromNameSearch(String firstName, String lastName, LocalDate birthDateIn, String genderKey, String addressLine1, String postcode) {
        Individual individual = new Individual();
        individual.setFirstName(firstName);
        individual.setLastName(lastName);

        if (postcode != null || addressLine1 != null) {
            Address address = new Address();
            address.setLine1(addressLine1);
            address.setPostCodeNoFormat(postcode);
            individual.setAddress(address);
        }

        var example = new ClientDetail();
        example.setContact(individual);
        MedDate birthDate = MedDate.from(birthDateIn);
        example.setBirthDate(birthDate);
        if (genderKey != null) {
            var gender = listDefinitionRepository.findByBusinessKey(genderKey).orElseThrow(NullPointerException::new);
            example.setGender(gender);
        }

        var results = getCriteria(example)
                .getExecutableCriteria(((Session) em).getSession())
                .list();
        return ((List<ClientDetail>) results).stream().map(ClientDetail::toClientDefinition).collect(toList());
    }

    private DetachedCriteria getCriteria(ClientDetail example) {
        Example example1 = Example.create(example).ignoreCase().enableLike(MatchMode.START)
                .excludeZeroes()
                .excludeProperty(ClientDetail_.dateMap.getName())
                .excludeProperty(ClientDetail_.textMap.getName());

        Example example2 = Example.create(example.getContact()).ignoreCase().enableLike(MatchMode.START)
                .excludeZeroes()
                .excludeProperty(Individual_.key.getName());

        return DetachedCriteria.forClass(ClientDetail.class)
                .add(example1)
                .createCriteria("contact").add(example2)
                .addOrder(Order.asc("lastName"))
                .addOrder(Order.asc("firstName"));
    }

    private List<ClientDetail> postProcessResult(String firstName, String lastName, List<ClientDetail> queryResult) {

        java.util.function.Predicate<ClientDetail> exampleFilter = candidate -> {
            Individual candidateContact = candidate.getContact();

            boolean lastnameMatch = lastName == null || StringUtils.startsWithIgnoreCase(candidateContact.getLastName(), lastName);
            boolean firstnameMatch = firstName == null || StringUtils.startsWithIgnoreCase(candidateContact.getFirstName(), firstName);
            //boolean dobMatch = birthDate == null || birthDate.equals(candidate.getBirthDate());
            //boolean genderMatch = gender == null || gender.equals(candidate.getGender());
            //return lastnameMatch && firstnameMatch && dobMatch && genderMatch;

            return lastnameMatch && firstnameMatch;
        };

        return queryResult.stream().filter(exampleFilter).collect(toList());
    }

    public List<ClientDefinition> secureLocalClientMatches(List<ClientDefinition> localClientMatches) {

        if (!settingsService.settingFor(SettingsService.SecurityClientSearch.NAMESPACE, "ENABLED").isTrue() || localClientMatches.isEmpty()) {
            return localClientMatches;
        }

        // restrict with security
        BooleanBuilder p = new BooleanBuilder();
        Predicate pSecurity = EntityRestrictionCommonPredicates.applySecurityPredicate(
                referral.serviceRecipient._super._super, entityRestrictionService, serviceCategorisationService);
        p.and(pSecurity);

        // restrict with the existing matches
        // NB we could get too large a query 'in' here - but we'd need to implement option 2 in DEV-641
        List<Long> localClientIds = localClientMatches.stream().map(ClientDefinition::getLocalClientId).collect(Collectors.toList());
        p = p.and(referral.client.id.in(localClientIds));

        // list the ids that are secure and match
        @SuppressWarnings("unchecked")
        JPAQuery<Long> query = (JPAQuery<Long>) new JPAQuery(em) // Shouldn't need this cast AFAICT (NU)
                .select(referral.client.id)
                .from(referral)
                .where(p);
        List<Long> secureClientIds = query
                .fetch();

        // return restricted by security and matching
        return localClientMatches.stream()
                .filter(c -> secureClientIds.contains(c.getLocalClientId())).collect(Collectors.toList());
    }
}
