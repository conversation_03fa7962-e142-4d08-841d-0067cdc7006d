package com.ecco.service;

import com.ecco.config.service.ExternalSystemService.ApiType;
import com.ecco.dto.ClientDefinition;
import com.ecco.dto.FlagsDefinition;
import com.ecco.infrastructure.bus.MessageBus;
import com.ecco.service.event.HttpTunnelEvent;

import org.jspecify.annotations.NonNull;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.lang.reflect.Proxy;
import java.net.URI;
import java.util.Arrays;
import java.util.Map;

/**
 * Client import strategy which communicates with backends using default REST API.
 */
@Component
public class DefaultClientImportStrategy extends WebSocketPrioritisingRestTemplateMultiplexer implements ClientImportStrategy {

    public DefaultClientImportStrategy(MessageBus<HttpTunnelEvent> messageBus) {
        super(messageBus);
    }

    @Override
    public boolean supports(ApiType apiType) {
        return apiType == ApiType.TUNNEL_WITH_FALLBACK || apiType == ApiType.TUNNEL_ONLY;
    }

    @Override
    public Iterable<ClientDefinition> queryBuildings(
            String externalSourceName, ApiType apiType, URI uri, ClientDefinition exemplar
    ) {
        return getClientDefinitions(externalSourceName, apiType, uri, exemplar, "buildings/query");
    }

    /** Make an HTTP POST request to the external API */
    @Override
    public Iterable<ClientDefinition> queryClients(
            String externalSourceName, ApiType apiType, URI uri, ClientDefinition exemplar
    ) {
        return getClientDefinitions(externalSourceName, apiType, uri, exemplar, "clients/query");
    }

    @Override
    public FlagsDefinition queryClientFlags(
            String externalSourceName, ApiType apiType, URI uri, String externalClientRef) {
        final RestTemplate restTemplate = getRestTemplate(externalSourceName, apiType);
        final FlagsDefinition result = restTemplate.getForEntity(uri.toString().concat("client/{externalClientRef}/flags/"),
                FlagsDefinition.class, Map.of("externalClientRef", externalClientRef))
                .getBody();
        assert result != null;
        return result;
    }

    @Override
    public Iterable<ClientDefinition> queryStaff(
            String externalSourceName, ApiType apiType, URI uri, ClientDefinition exemplar
    ) {
        return getClientDefinitions(externalSourceName, apiType, uri, exemplar, "staff/query");
    }

    @NonNull
    private Iterable<ClientDefinition> getClientDefinitions(
            String externalSourceName, ApiType apiType, URI uri, ClientDefinition exemplar, String path
    ) {
        final RestTemplate restTemplate = getRestTemplate(externalSourceName, apiType);
        final ClientDefinition[] result = restTemplate.postForObject(uri.resolve(path),
                externalClientSourceWrapper(exemplar, externalSourceName), ClientDefinition[].class);
        return Arrays.asList(result);
    }

    /** Provides a proxy instance of ClientDefinition immutable so we can override externalClientSource
     * The externalClientSource can't truly be trusted as this is provided by the client browser - so further
     * requests could end up linked to other systems */
    private ClientDefinition externalClientSourceWrapper(final ClientDefinition exemplar, final String externalClientSource) {
        return (ClientDefinition) Proxy.newProxyInstance(DefaultClientImportStrategy.class.getClassLoader(),
                new Class[]{ClientDefinition.class}, (proxy, method, args) -> {
            if (method.getName().equals("getExternalClientSource")) {
                return externalClientSource;
            } else {
                return method.invoke(exemplar, args);
            }
        });
    }
}
