package com.ecco.service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.web.client.ResourceAccessException;
import org.springframework.web.client.RestTemplate;

import com.ecco.config.service.ExternalSystemService;
import com.ecco.infrastructure.bus.MessageBus;
import com.ecco.infrastructure.config.web.ConvertersConfig;
import com.ecco.service.event.HttpTunnelDownEvent;
import com.ecco.service.event.HttpTunnelEvent;
import com.ecco.service.event.HttpTunnelUpEvent;

public class WebSocketPrioritisingRestTemplateMultiplexer {
    private final Map<String, RestTemplate> restTemplates = new ConcurrentHashMap<>();
    private final RestTemplate defaultRestTemplate;

    public WebSocketPrioritisingRestTemplateMultiplexer(MessageBus<HttpTunnelEvent> messageBus) {
        defaultRestTemplate = new RestTemplate();
        configureMessageConverters(defaultRestTemplate);
        WebSocketResponseHandler webSocketResponseHandler = new WebSocketResponseHandler(messageBus);

        messageBus.subscribe(HttpTunnelUpEvent.class, e -> {
            final WebSocketHttpRequestFactory requestFactory = new WebSocketHttpRequestFactory(e.getWebSocketSession(), webSocketResponseHandler);
            final RestTemplate restTemplate = new RestTemplate(requestFactory);
            configureMessageConverters(restTemplate);
            restTemplates.put(e.getName(), restTemplate);
        });
        messageBus.subscribe(HttpTunnelDownEvent.class, e -> restTemplates.remove(e.getName()));
    }

    RestTemplate getRestTemplate(String externalSourceName, ExternalSystemService.ApiType apiType) {
        RestTemplate restTemplate;
        if (apiType == ExternalSystemService.ApiType.TUNNEL_ONLY) {
            restTemplate = restTemplates.get(externalSourceName);
            if (restTemplate == null) {
                throw new ResourceAccessException("Web Socket tunnel not connected for " + externalSourceName);
            }
        } else {
            restTemplate = restTemplates.getOrDefault(externalSourceName, defaultRestTemplate);
        }
        return restTemplate;
    }

    private void configureMessageConverters(final RestTemplate restTemplate) {
        List<HttpMessageConverter<?>> messageConverterList = new ArrayList<>();
        ConvertersConfig.addWebApiConvertersTo(messageConverterList);
        restTemplate.setMessageConverters(messageConverterList);
    }
}
