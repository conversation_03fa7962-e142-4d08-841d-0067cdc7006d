package com.ecco.service;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.extern.slf4j.Slf4j;
import org.jspecify.annotations.NonNull;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.client.AbstractClientHttpResponse;
import org.springframework.http.client.ClientHttpRequest;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;

import java.io.*;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

/**
 * Like an HttpRequest, but executed over a web socket. Use with {@link org.springframework.web.client.RestTemplate}
 * to execute over a web socket rather than over a new HTTP connection.
 *
 * @since 22/08/2016
 */
@Slf4j
class WebSocketHttpRequestFactory implements ClientHttpRequestFactory {
    static int TIMEOUT_SECS = 10; // Package-protected so tests can manipulate it.
    private final ObjectMapper objectMapper = new ObjectMapper();
    private final WebSocketSession webSocketSession;
    private final WebSocketResponseHandler webSocketResponseHandler;

    WebSocketHttpRequestFactory(WebSocketSession webSocketSession, WebSocketResponseHandler responseHandler) {
        this.webSocketSession = webSocketSession;
        this.webSocketResponseHandler = responseHandler;
    }

    @NonNull
    @Override
    public ClientHttpRequest createRequest(@NonNull URI uri, @NonNull HttpMethod httpMethod) {
        return new WebSocketHttpRequest(uri, httpMethod);
    }

    private class WebSocketHttpRequest implements ClientHttpRequest {
        @JsonProperty("correlationId")
        @JsonSerialize(using = ToStringSerializer.class)
        private UUID correlationId = UUID.randomUUID();
        @JsonSerialize(using = ToStringSerializer.class)
        private HttpMethod method;
        @JsonSerialize(using = ToStringSerializer.class)
        private URI url;
        @JsonIgnore
        private final HttpHeaders requestHeaders = new HttpHeaders();
        @JsonIgnore
        private final ByteArrayOutputStream body = new ByteArrayOutputStream(1024);


        WebSocketHttpRequest(URI uri, HttpMethod method) {
            this.url = URI.create(uri.getPath());
            this.method = method;
        }

        @Override
        public HttpMethod getMethod() {
            return method;
        }

        public void setMethod(HttpMethod method) {
            this.method = method;
        }

        @NonNull
        @Override
        public String getMethodValue() {
            return method.name();
        }

        @NonNull
        @JsonIgnore
        public URI getURI() {
            return url;
        }

        public void setURI(URI url) {
            this.url = url;
        }

        @NonNull
        @Override
        @JsonIgnore
        public HttpHeaders getHeaders() {
            return requestHeaders;
        }

        @JsonProperty
        public String getContentType() {
            // @Get can have no ContentType
            var type = requestHeaders.getContentType();
            return (type != null) ? type.toString() : null;
        }

        @JsonProperty
        public String getAcceptType() {
            return requestHeaders.getAccept().get(0).toString();
        }

        @NonNull
        @Override
        @JsonIgnore
        public OutputStream getBody() {
            return body;
        }

        @JsonProperty("body")
        public String getBodyString() {
            return new String(body.toByteArray(), StandardCharsets.UTF_8);
        }

        @NonNull
        @Override
        public ClientHttpResponse execute() throws IOException {
            final String query = objectMapper.writeValueAsString(this);
            final CompletableFuture<ClientHttpResponse> response = new CompletableFuture<>();
            webSocketResponseHandler.registerPendingResponse(correlationId, response);
            log.debug("Sending request with correlation ID: {}", correlationId);
            webSocketSession.sendMessage(new TextMessage(query));
            try {
                return response.get(TIMEOUT_SECS, TimeUnit.SECONDS);
            } catch (InterruptedException | ExecutionException e) {
                return new ErrorResponse(HttpStatus.INTERNAL_SERVER_ERROR);
            } catch (TimeoutException e) {
                return new ErrorResponse(HttpStatus.REQUEST_TIMEOUT);
            }
        }
    }

    private static class ErrorResponse extends AbstractClientHttpResponse {
        private final HttpStatus statusCode;

        ErrorResponse(HttpStatus statusCode) {
            this.statusCode = statusCode;
        }

        @Override
        public int getRawStatusCode() {
            return statusCode.value();
        }

        @NonNull
        @Override
        public String getStatusText() {
            return statusCode.getReasonPhrase();
        }

        @Override
        public void close() {

        }

        @NonNull
        @Override
        public InputStream getBody() {
            return new ByteArrayInputStream(new byte[0]);
        }

        @NonNull
        @Override
        public HttpHeaders getHeaders() {
            return new HttpHeaders();
        }
    }
}
