package com.ecco.service;

import java.util.Map;

import com.ecco.dto.ClientDefinition;
import com.ecco.dto.DelegateResponse;
import com.ecco.infrastructure.annotations.WriteableTransaction;

@WriteableTransaction
public interface ClientDetailService {
    String DEFAULT_CLIENT_SOURCE_SYSTEM = "ecco";

    /**
     * Returns a map of system names to client definition results.
     * Internal clients will be mapped to {@link #DEFAULT_CLIENT_SOURCE_SYSTEM}.
     */
    Map<String, DelegateResponse<Iterable<ClientDefinition>>> queryClientsByExample(ClientDefinition exemplar,
            boolean includeExternal);
}
