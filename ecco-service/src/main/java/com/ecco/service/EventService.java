package com.ecco.service;

import java.time.LocalDate;
import java.util.Collection;
import java.util.List;

import com.ecco.calendar.CombinedEntry;
import com.ecco.dom.CustomEventImpl;
import org.joda.time.DateTime;

import com.ecco.infrastructure.annotations.WriteableTransaction;
import org.jspecify.annotations.Nullable;

/**
 * Combined iCal + Non-iCal calendar service.
 *
 * Loads non-iCal and iCal representations into a CombinedEntry. See EventServiceImpl for documentation.
 * For non-iCal (internal) specific CRUD, also see CustomEventRepository.
 */
@WriteableTransaction
public interface EventService {

    CombinedEntry getEntry(String uuid);

    Collection<CombinedEntry> getCalendars(List<String> calendarIds, DateTime start, DateTime end);

    String getNameFromCalendarId(String calendarId);

    /**
     * Get events for this contact going back 1 week from the beginning of today, forwards to 5 weeks from the
     * end of today.
     */
    Collection<CombinedEntry> getNearbyCalendarEventsForContact(String calendarId, @Nullable LocalDate nearby);

    CustomEventImpl persistAndSyncAddNonRecurringCalendarEntry(CustomEventImpl e);
    void persistAndSyncUpdateNonRecurringCalendarEntry(CustomEventImpl e);
    void persistAndSyncRemoveNonRecurringCalendarEntry(CustomEventImpl e);
}
