package com.ecco.service;

import java.util.List;

import com.ecco.dto.ClientDefinition;

public interface SearchClientService {

    /**
     * Search for clients in local database by example.
     * Results in firstname like N%, lastname like U%, gender =, dob =
     *
     * @param client the client exemplar. Last name must be specified.
     * @return matching clients
     */
    List<ClientDefinition> getLocalClientMatches(ClientDefinition client);

    /**
     * Use the matches local clients and return a list which adheres to usual referral security
     */
    List<ClientDefinition> secureLocalClientMatches(List<ClientDefinition> localClients);
}
