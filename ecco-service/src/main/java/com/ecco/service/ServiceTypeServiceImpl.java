package com.ecco.service;

import com.ecco.infrastructure.annotations.WriteableTransaction;
import com.ecco.serviceConfig.dom.ServiceType;
import com.ecco.serviceConfig.repositories.ServiceTypeRepository;

import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

@org.springframework.stereotype.Service("serviceTypeService")
@WriteableTransaction
public class ServiceTypeServiceImpl implements ServiceTypeService {

    @Autowired
    private ServiceTypeRepository serviceTypeRepository;

    @Override
    public ServiceType getServiceType(long id) {
        return serviceTypeRepository.findById(id).orElseThrow();
    }

    @Override
    public void deleteServiceType(ServiceType serviceType) {
        serviceTypeRepository.delete(serviceType);
    }

    @Override
    public void setServiceType(ServiceType serviceType) {
        serviceTypeRepository.save(serviceType);
    }

    @Override
    public List<ServiceType> getServiceTypes() {
        return StreamSupport.stream(serviceTypeRepository.findAll().spliterator(), false)
                .collect(Collectors.toList());
    }

}
