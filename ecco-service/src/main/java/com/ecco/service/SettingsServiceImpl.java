package com.ecco.service;

import com.ecco.config.dom.Setting;
import com.ecco.config.repositories.SettingsRepository;
import com.ecco.config.service.SettingsService;
import com.ecco.infrastructure.annotations.WriteableTransaction;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.List;

import org.jspecify.annotations.NonNull;

@Service("settingsService")
@WriteableTransaction
public class SettingsServiceImpl implements SettingsService {

    @Autowired
    SettingsRepository settingsRepository;

    @Autowired
    public SettingsServiceImpl() {
        super();
    }

    @Override
    @Cacheable(value = "settings", key = "#namespace + #key")
    public Setting settingFor(String namespace, String key) {
        try {
            return findSetting(namespace, key);

        } catch (IllegalArgumentException e) {
            return null;
        }
    }

    @Override
    @Cacheable("settings")
    @NonNull
    public Setting settingFor(SettingKey key) {
        return findSetting(key.namespace(), key.key());
    }


    @Override @CacheEvict(value="settings", allEntries=true) // evict all entries as we cache multiple ways
    public void setSetting(String namespace, String key, String value) {
        Setting s;
        s = findSetting(namespace, key);
        s.setValue(value);
        settingsRepository.save(s);
    }

    private Setting findSetting(String namespace, String key) {
        List<Setting> result = settingsRepository.findAllByNamespaceAndKey(namespace, key);
        if (result.isEmpty()) {
            throw new IllegalArgumentException("No such setting with namespace=" + namespace + " and key=" + key);
        } else if (result.size() > 1) {
            throw new IllegalArgumentException("Mulitple settings with namespace=" + namespace + " and key=" + key);
        } else {
            return result.get(0);
        }
    }

}
