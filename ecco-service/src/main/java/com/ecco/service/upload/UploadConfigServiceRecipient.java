package com.ecco.service.upload;

import com.ecco.dao.ServiceRecipientAttachmentRepository;
import com.ecco.dom.ServiceRecipientAttachment;
import com.ecco.dom.upload.UploadedFile;
import com.ecco.upload.dao.UploadedFileRepository;
import com.ecco.web.upload.AbstractUploadConfig;
import org.jspecify.annotations.Nullable;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

public class UploadConfigServiceRecipient extends AbstractUploadConfig<ServiceRecipientAttachment> {

    @Nullable // when we want to just get the file by id
    private final Integer serviceRecipientId;
    private final @Nullable String evidencePage;
    private final @Nullable String evidencePageGroup;
    private final ServiceRecipientAttachmentRepository repository;


    public UploadConfigServiceRecipient(@Nullable Integer serviceRecipientId,
                                        MultipartFile file, @Nullable String evidencePage,
                                        @Nullable String evidencePageGroup, ServiceRecipientAttachmentRepository repository) {
        super(file);
        this.serviceRecipientId = serviceRecipientId;
        this.repository = repository;
        this.evidencePage = evidencePage;
        this.evidencePageGroup = evidencePageGroup;
    }

    @Nullable
    public Integer getServiceRecipientId() {
        return serviceRecipientId;
    }

    public @Nullable String getEvidencePage() {
        return evidencePage;
    }
    public @Nullable String getEvidencePageGroup() {
        return evidencePageGroup;
    }

    @Override
    public ServiceRecipientAttachment constructAttachment() throws IOException {
        ServiceRecipientAttachment attachment = populateAttachment(new ServiceRecipientAttachment(serviceRecipientId));
        attachment.setEvidencePage(getEvidencePage());
        attachment.setEvidencePageGroup(getEvidencePageGroup());
        return attachment;
    }

    @Override
    public List<? extends UploadedFile> findFiles() {
        // TODO Auto-generated method stub
        return repository.findFiles(serviceRecipientId);
    }

    @Override
    public UploadedFileRepository<ServiceRecipientAttachment> getRepository() {
        return repository;
    }
}
