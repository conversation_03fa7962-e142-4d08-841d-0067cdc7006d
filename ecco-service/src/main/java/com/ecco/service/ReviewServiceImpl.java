package com.ecco.service;

import java.util.Collections;
import java.util.List;

import org.jspecify.annotations.NonNull;

import com.ecco.calendar.dom.EventType;
import com.ecco.dao.CustomEventRepository;
import com.ecco.dao.ReviewRepository;
import com.ecco.dao.EvidenceSupportWorkRepository;
import com.ecco.dom.*;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ecco.dao.ReferralRepository;
import com.ecco.dto.EvidenceLegacyReviewDto;
import com.ecco.infrastructure.annotations.WriteableTransaction;
import com.ecco.calendar.core.util.DateTimeUtils;

import static java.util.Collections.emptyList;
import static java.util.stream.Collectors.toList;

@Service("reviewService")
@WriteableTransaction
public class ReviewServiceImpl implements ReviewService {

    @Autowired
    private ReferralRepository referralRepository;

    @Autowired
    private CustomEventRepository nonRecurringRepository;

    @Autowired
    private EventService eventService;

    @Autowired
    private ReviewRepository reviewRepository;

    @Autowired
    EvidenceSupportWorkRepository supportWorkRepository;

    @Override
    public EvidenceLegacyReviewDto getReviewDto(int serviceRecipientId) {
        EvidenceLegacyReviewDto rc = new EvidenceLegacyReviewDto();
        List<Review> reviews = getReviews(serviceRecipientId); // loads UTC field into localdate
        rc.setReviews(reviews);

        List<LocalDate> futureReviewDates = getNextReviewDates(serviceRecipientId);
        rc.setFutureReviewDates(futureReviewDates);
        DateTime end = null;
        if ((reviews != null) && (!reviews.isEmpty())) {
            end = reviews.get(0).getStartDate();
            // for validation in the new ts review popup
            // we don't want to create reviews before completed reviews (according to ECCO-1360)
            rc.setLastReviewDate(end);
        }
        List<EvidenceSupportWork> threatsOutstanding = getThreatsOutstanding(serviceRecipientId, end);
        rc.setThreatsOutstanding(threatsOutstanding);

        return rc;
    }

    @Override
    public Review getReview(long id) {
        return reviewRepository.getById(id);
    }

    @Override
    public List<Review> getReviews(int serviceRecipientId) {
        return reviewRepository.findAllByServiceRecipientIdOrderByStartDateDesc(serviceRecipientId);
    }

    // this method showed duplicates since we create events per user (to aid exporting/syncing calendars)
    // and so this lists all the events without filtering just the client
    // see the duplicate which doesn't do this - getReviewDates above
    // however, it is also duplicated...so we refactored the code
    /*
    public List<DateTime> getFutureReviewDates(Long referralId) {
        EntityFilter config = new EventFromReferralFilter(referralId, EventType.Review);
        List entities = entityViewService.getEntities(config);
        List<DateTime> futureReviewDates = new ArrayList<DateTime>();
        DateTime now = new DateTime();
        if ((entities != null) && (!entities.isEmpty())) {
            for (Iterator iterator = entities.iterator(); iterator.hasNext();) {
                CustomEventImpl event = (CustomEventImpl) iterator.next();
                MedDate eventDate = event.getEventDate();
                DateTime eventDateJ = DateTimeUtils.convertToDateTime(eventDate, DateTimeZone.UTC);
                if (eventDateJ.isAfter(now))
                    futureReviewDates.add(eventDateJ);
            }
        }

        // sort asc - first one most likely
        Collections.sort(futureReviewDates); //, Collections.reverseOrder());
        return futureReviewDates;
    }
    */

    @Override
    public void setComplete(long reviewId) {
        var r = reviewRepository.getById(reviewId);
        r.setComplete(true);
        reviewRepository.save(r);
    }

    @Override
    public List<EvidenceSupportWork> getThreatsOutstanding(int serviceRecipientId, DateTime end) {
        return supportWorkRepository.findAllSupportWorkWithRiskManagementNotHandledBefore(serviceRecipientId, end);
    }

    @Override
    public Review setReview(Review review) {
        return reviewRepository.save(review);
    }


    @Override
    public List<LocalDate> getReviewDates(int srId) {
        var now = LocalDate.now();
        var entities = nonRecurringRepository.findAllByTypeGreaterThan(srId, EventType.Review,
                now.year().get(), (short) now.monthOfYear().get(), (short) now.dayOfMonth().get());
        if ((entities != null) && (!entities.isEmpty())) {
            return entities.stream()
                    .map(CustomEventImpl::getEventDate)
                    .map(date -> DateTimeUtils.convertToDateTime(date, DateTimeZone.UTC).toLocalDate())
                    .collect(toList());
        }
        return emptyList();
    }

    @Override
    public List<LocalDate> getNextReviewDates(int srId) {
        return EventUtils.removeDatesBefore(getReviewDates(srId), null);
    }

    @Override
    public void setReviewDates(int serviceRecipientId, @NonNull String reviewSchedule) {
        Referral referral = referralRepository.findByServiceRecipient_Id(serviceRecipientId);

        ReferralServiceImpl.deleteDates(nonRecurringRepository, eventService, serviceRecipientId, EventType.Review, LocalDate.now());

        DateTime startDate = referral.getReceivingServiceDate();
        List<DateTime> reviewDates = Review.calculateNextReviewDates(reviewSchedule, startDate);

        scheduleReviewDates(referral, reviewDates);
    }

    @Override
    public void setCustomReviewDate(int serviceRecipientId, DateTime reviewDate) {

        Referral referral = referralRepository.findByServiceRecipient_Id(serviceRecipientId);

        ReferralServiceImpl.deleteDates(nonRecurringRepository, eventService, serviceRecipientId, EventType.Review, LocalDate.now());

        scheduleReviewDates(referral, Collections.singletonList(reviewDate));
    }

    protected void scheduleReviewDates(Referral referral, List<DateTime> reviewDates) {
        Individual wContact = referral.getSupportWorker();

        for (DateTime reviewDate : reviewDates) {
            ReferralServiceImpl.setDatesForReferralWithContacts(eventService, referral,
                    reviewDate, "", EventType.Review, wContact);
        }
    }

}
