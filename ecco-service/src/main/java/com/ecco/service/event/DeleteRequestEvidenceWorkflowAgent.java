package com.ecco.service.event;

import com.ecco.dom.commands.DeleteRequestEvidenceCommand;
import com.ecco.evidence.event.DeleteRequestEvidenceEvent;
import com.ecco.infrastructure.bus.MessageBus;
import com.ecco.infrastructure.hibernate.EntityUriMapper;
import com.ecco.infrastructure.util.EccoTimeUtils;
import com.ecco.workflow.WorkflowLinkedResource;
import com.ecco.workflow.WorkflowService;
import com.ecco.workflow.WorkflowTaskCreateTemplate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEvent;

import javax.annotation.PostConstruct;
import java.util.Collections;

@RequiredArgsConstructor
@Slf4j
public class DeleteRequestEvidenceWorkflowAgent {

    public static String REL_DATABASEREFERENCE = "ABOUT-DB-REF";

    private final MessageBus<ApplicationEvent> messageBus;

    private final EntityUriMapper entityUriMapper;

    private final WorkflowService workflowService;

    @PostConstruct
    protected void init() {
        messageBus.subscribe(DeleteRequestEvidenceEvent.class, this::createWorkflowTaskForDeleteRequestEvidence);
    }

    private void createWorkflowTaskForDeleteRequestEvidence(DeleteRequestEvidenceEvent message) {
        WorkflowTaskCreateTemplate taskCreateTemplateViewModel = WorkflowTaskCreateTemplate.BuilderFactory.create()
                .name("delete evidence request")
                .dueDate(EccoTimeUtils.getUsersLocalDateTimeNow().plusDays(2).withNano(0))
                .linkedResources(Collections.singletonList(
                        this.constructDatabaseReference("sr",
                                DeleteRequestEvidenceCommand.DISCRIMINATOR,
                                message.getCmdUuid().toString())))
                .build();
        workflowService.createAdhocWorkflowTaskForGroup(taskCreateTemplateViewModel, this.constructTaskGroup(message.getServiceAllocationId()));
    }

    // NB this notation is also used in TaskCommandAjaxRepository.ts
    // The 'ACT_RU_IDENTITYLINK ends up with the group name
    private String constructTaskGroup(int serviceAllocationId) {
        // NB the format here without spaces matches what client side JSON.stringify does - so the groups match
        return "{\"serviceCategorisationId\":"+serviceAllocationId+"}";
        // was this - but removed the 'what' permission to avoid too many requests vs simply filtering client side
        //return "{\"serviceCategorisationId\":"+serviceAllocationId+",\"roles\":[\"ROLE_ADMINEVIDENCE\"]}";
    }

    private WorkflowLinkedResource constructDatabaseReference(String tableReference, String discriminatorReference, String uuid) {
        // command: use the title to indicate the type of db-ref
        // rel: see https://www.iana.org/assignments/link-relations/link-relations.xhtml
        //      and WorkflowTaskToViewModel.REL_ABOUT
        // entityName and entityId get converted into the uri and persisted in the task.attachments[].uri (see ActivitiWorkflowServiceImpl.createAdhocWorkflowTask)
        return WorkflowLinkedResource.BuilderFactory.create()
                .title("command")
                .rel(REL_DATABASEREFERENCE)
                .uri(entityUriMapper.uriForEntity(tableReference+"-"+discriminatorReference, uuid))
                .build();
    }

}
