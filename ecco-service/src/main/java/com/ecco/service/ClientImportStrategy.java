package com.ecco.service;

import com.ecco.config.service.ExternalSystemService.ApiType;
import com.ecco.dto.ClientDefinition;
import com.ecco.dto.FlagsDefinition;

import java.net.URI;

/**
 * Defines a strategy for importing clients from an external system source.
 *
 * @since 08/07/2014
 */
public interface ClientImportStrategy {
    boolean supports(ApiType apiType);

    Iterable<ClientDefinition> queryBuildings(String name, ApiType apiType, URI uri, ClientDefinition exemplar);

    Iterable<ClientDefinition> queryClients(String name, ApiType apiType, URI uri, ClientDefinition exemplar);
    FlagsDefinition queryClientFlags(String name, ApiType apiType, URI uri, String externalClientRef);

    Iterable<ClientDefinition> queryStaff(String name, ApiType apiType, URI uri, ClientDefinition exemplar);
}
