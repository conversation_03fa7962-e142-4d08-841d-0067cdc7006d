package com.ecco.service;

import com.ecco.infrastructure.bus.MessageBus;
import com.ecco.service.event.HttpTunnelEvent;
import com.ecco.service.event.HttpTunnelResponseEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.client.ClientHttpResponse;

import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @since 26/08/2016
 */
@Slf4j
class WebSocketResponseHandler {
    private final Map<UUID, CompletableFuture<ClientHttpResponse>> pendingResponses = new ConcurrentHashMap<>();

    WebSocketResponseHandler(MessageBus<HttpTunnelEvent> messageBus) {
        messageBus.subscribe(HttpTunnelResponseEvent.class, e -> {
            final UUID correlationId = e.getCorrelationId();
            // there is no correlationId when we keep the connection alive in WebSocketConnectionInstigator#checkSession
            if (correlationId == null) {
                log.debug("Received response with no correlation ID - keep-alive heartbeat every minute");
            } else {
                log.debug("Received response with correlation ID: {}", correlationId);
                final CompletableFuture<ClientHttpResponse> response = pendingResponses.remove(correlationId);
                if (response != null) {
                    // Handle the response
                    response.complete(e.getClientHttpResponse());
                } else {
                    log.warn("Received response with unknown correlation ID: " + correlationId + " - " + e.getBody());
                }
            }
        });
    }

    void registerPendingResponse(UUID correlationId, CompletableFuture<ClientHttpResponse> response) {
        pendingResponses.put(correlationId, response);
    }
}
