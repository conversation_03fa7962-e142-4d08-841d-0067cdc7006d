package com.ecco.service.encryption;

import java.util.UUID;

/**
 * Represents the encrypted fields of an object being transferred to be stored in an assumed insecure database,
 * usually IndexedDB in a web browser.
 */
public class EncryptedSecretFieldsDto {

    /** User-device UUID from which this command came and will be used to look up the key to decrypt with */
    public UUID userDeviceId;

    /** Encrypted payload */
    public String base64Payload;
}
