package com.ecco.service;

import com.ecco.calendar.core.util.DateTimeUtils;
import com.ecco.calendar.dom.EventType;
import com.ecco.dom.*;
import com.ecco.calendar.dom.MedDate;

import org.joda.time.DateTime;
import org.joda.time.LocalDate;

import java.util.List;

import static java.util.stream.Collectors.toList;

class EventUtils {

    public static List<LocalDate> removeDatesBefore(List<LocalDate> dates, LocalDate before) {
        if (before == null) {
            before = LocalDate.now();
        }

        LocalDate finalBefore = before;
        return dates.stream().filter(partial -> !partial.isBefore(finalBefore)).collect(toList());
    }

    // no ContactLocation is set because its just specified - so this is interviews/reviews etc
    public static CustomEventWithServiceRecipient createEventForIndividual(int srId, Individual contactOwner, DateTime date, EventType type, String location) {
        // create a CEWSR event from nextMeetingDates and Interviews and Reviews
        // construct the event
        CustomEventWithServiceRecipient event = new CustomEventWithServiceRecipient(srId);

        // find the calendarId
        // this statement doesn't cause hibernate to load the user - for whatever reason
        String calendarId = contactOwner.getCalendarId();
        // add the calendarId to a transient String collection to be picked up by calendar support handler
        event.setOwnerCalendarIdForNewEvent(calendarId);

        // set the referral, action and type
        event.setEventType(type);
        switch (type) {
            case Meeting:   // fallthru
            case Interview: // fallthru
            case Review:
                event.setGenerated(true);
                break;
        }

        // set the date
        boolean useTime = EventType.Interview.equals(type) || EventType.Meeting.equals(type);
        MedDate decisionDateMed = DateTimeUtils.convertToMedDate(date, useTime);
        event.setEventDate(decisionDateMed);
        // we also need to set a duration if there is an end time...
        if (useTime) {
            DateTime end = date.plusHours(2);
            event.setEventEndDate(DateTimeUtils.convertToMedDate(end, useTime));
        }

        // attach the interviewer to be consistent
        Contact_Event attendee = new Contact_Event();
        attendee.setEvent(event);
        attendee.setContact(contactOwner);
        event.addContact_Event(attendee);

        event.generateNameForEvent(type, null);
        event.setLocation(location);
        return event;
    }

}
