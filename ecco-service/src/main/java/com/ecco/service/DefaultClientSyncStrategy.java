package com.ecco.service;

import java.net.URI;

import com.ecco.dto.ClientDefinitionCommandDto;
import com.ecco.dto.ClientEvent;
import com.ecco.dto.FlagsDefinitionCommandDto;
import com.ecco.evidence.event.EvidenceCommentSavedEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import com.ecco.config.service.ExternalSystemService;
import com.ecco.dto.ClientDefinition;
import com.ecco.infrastructure.bus.MessageBus;
import com.ecco.service.event.HttpTunnelEvent;

/**
 * Client sync strategy which communicates with backends using default REST API.
 */
@Component
@Slf4j
public class DefaultClientSyncStrategy extends WebSocketPrioritisingRestTemplateMultiplexer implements ClientSyncStrategy {

    public DefaultClientSyncStrategy(MessageBus<HttpTunnelEvent> messageBus) {
        super(messageBus);
    }

    @Override
    public boolean supports(ExternalSystemService.ApiType apiType) {
        return apiType == ExternalSystemService.ApiType.TUNNEL_WITH_FALLBACK || apiType == ExternalSystemService.ApiType.TUNNEL_ONLY;
    }

    @Override
    public ClientDefinition syncClient(String externalSourceName,
                                       ExternalSystemService.ApiType apiType,
                                       URI uri,
                                       ClientDefinition client) {

        log.debug("Starting sync client (create) in rest api for externalSourceName {}", externalSourceName);

        RestTemplate restTemplate = getRestTemplate(externalSourceName, apiType);
        return restTemplate.postForObject(uri.resolve("clients/"),
                client, ClientDefinition.class);
    }

    @Override
    public String syncClientUpdate(String externalSourceName,
                                   ExternalSystemService.ApiType apiType,
                                   URI uri,
                                   ClientDefinitionCommandDto cmd) {

        log.debug("Starting sync client (update) in rest api for externalSourceName {}", externalSourceName);

        RestTemplate restTemplate = getRestTemplate(externalSourceName, apiType);
        return restTemplate.postForObject(uri.resolve("client/update"),
                cmd, String.class);
    }

    @Override
    public String syncEvidenceComment(String systemName, ExternalSystemService.ApiType apiType, URI uri,
                                      ClientDefinition client, EvidenceCommentSavedEvent event, ClientEvent.NoteType noteType) {
        log.debug("Starting sync evidence comment in rest api for externalSourceName {}", systemName);

        RestTemplate restTemplate = getRestTemplate(systemName, apiType);
        ClientEvent clientEvent = new ClientEvent(client.getExternalClientRef(),
                noteType,
                "ECCO contact: " + (event.getCommentDetails().getType() != null
                        ? event.getCommentDetails().getType().getName()
                        : ""),
                event.getCommentDetails().getComment(),
                event.getCommentDetails().getRelevantInstant(),
                event.getCommentDetails().getWorkId());
        return restTemplate.postForObject(uri.resolve("events/"),
                clientEvent, String.class);
    }

    @Override
    public Boolean syncEvidenceFlags(String externalSourceName,
                            ExternalSystemService.ApiType apiType,
                            URI uri,
                            FlagsDefinitionCommandDto cmd) {

        log.debug("Starting sync flags in rest api for externalSourceName {}", externalSourceName);

        RestTemplate restTemplate = getRestTemplate(externalSourceName, apiType);
        return restTemplate.postForObject(uri.resolve("events/flags/"),
                cmd, Boolean.class);
    }

}
