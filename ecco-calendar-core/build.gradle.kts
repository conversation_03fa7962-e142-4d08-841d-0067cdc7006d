/*
 * This file was generated by the Gradle 'init' task.
 *
 * This project uses @Incubating APIs which are subject to change.
 */

plugins {
    id("org.eccosolutions.querydsl-java-library")
}

dependencies {
    implementation(project(":ecco-infrastructure"))
    implementation(project(":ecco-contacts"))
    implementation("org.springframework:spring-core")
    implementation("org.springframework.hateoas:spring-hateoas")
    implementation("joda-time:joda-time:2.10.8")
    implementation("com.google.guava:guava")
}

description = "ecco-calendar-core"