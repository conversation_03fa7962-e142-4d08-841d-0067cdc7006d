package com.ecco.infrastructure.annotations;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Inherited;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import javax.servlet.ServletException;

import org.springframework.core.annotation.AliasFor;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.ecco.exceptions.ApplicationException;

/**
 * Used to mark implementations (not the interfaces as they are not applied in all circumstances) of service classes
 * and/or methods where read-only transactions are required.
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Inherited
@Documented
@Transactional(readOnly = true, propagation = Propagation.REQUIRED,
        value = "transactionManager",
        rollbackFor = { ApplicationException.class, ServletException.class })
public @interface ReadOnlyTransaction {

    @AliasFor(annotation = Transactional.class, value = "timeout")
    int timeout() default 60;


}
