import {Encrypted} from "@eccosolutions/ecco-common";
import { WorkflowSecretFields, WorkflowPlainFields } from 'ecco-dto/workflow-dto';
import {ApiClient} from "ecco-dto";

export interface EncryptedWorkflow extends Encrypted<WorkflowSecretFields, WorkflowPlainFields> { }

export class SecureWorkflowAjaxRepository {
    constructor(private apiClient: ApiClient) {
    }

    /** See WorkflowAjaxRepository.findOneWorkflowByServiceRecipientId */
    findOneWorkflowByServiceRecipientId(userDeviceId: string, srId: number): Promise<EncryptedWorkflow> {
        return this.apiClient.secureGet<EncryptedWorkflow>(userDeviceId, `service-recipients/${srId}/workflow/`);
    }
}
