import {createContext, FC, ReactElement, useContext} from "react";
import * as React from "react";
import {EccoAPI} from "../EccoAPI";
import {usePromise} from "./entityLoadHooks";
import {LoadingOrError} from "../Loading";
import {useServicesContext} from "../ServicesContext";
import {
    Building,
    Client,
    ReferralSummaryWithEntities,
    ServiceRecipientWithEntities,
    ServiceType,
    StaffDto,
    StaffJobDto,
    IncidentDto,
    RepairDto,
    ManagedVoidDto
} from "ecco-dto";
import {StringToObjectMap} from "@eccosolutions/ecco-common";
import {translateTaskName} from "../tasks/TaskAudit";
import {ServiceRecipientTaskBaseCommandDto} from "ecco-dto/evidence/evidence-command-dto";
import {commandQueueFlushedEventBus} from "ecco-commands";

export interface ServiceRecipientContext {
    serviceRecipient: ServiceRecipientWithEntities;
    serviceType: ServiceType;
}

export interface ServiceRecipientWithEntitiesContext extends ServiceRecipientContext {
    referral?: ReferralSummaryWithEntities | undefined;
    client?: Client | null | undefined;
    building?: Building | null | undefined;
    workerJob?: StaffJobDto | null | undefined;
    worker?: StaffDto | null | undefined;
    incident?: IncidentDto | null | undefined;
    repair?: RepairDto | null | undefined;
    managedVoid?: ManagedVoidDto | null | undefined;
}

export type AsyncServiceRecipientParams = {
    srId: number | null;
};

export type AsyncServiceRecipientParamsWithServices = {
    services: EccoAPI;
} & AsyncServiceRecipientParams;

export interface ReloadableContext<T> {
    resolved: T;
    reload: () => void;
}

/** This is the top level for then using useServiceRecipientWithEntities() */
const SRWithEntitiesContext =
    createContext<ReloadableContext<ServiceRecipientWithEntitiesContext> | null>(null);

export const LoadSRWithEntitiesContext: FC<{srId: number}> = ({srId, children}) => {
    const services = useServicesContext();
    const {resolved, error, reload} = usePromise(
        () => loadSrWithEntities({srId, services}),
        [srId]
    );
    if (!resolved) return <LoadingOrError error={error} />;
    return (
        <SRWithEntitiesContext.Provider value={{resolved, reload}}>
            {children}
        </SRWithEntitiesContext.Provider>
    );
};

/** Resolve SRWE that has already been loaded by a parent component */
export function useCurrentServiceRecipientWithEntities() {
    // Force non-null as we should use after useServiceRecipientWithEntities() as noted above
    const context = useContext(SRWithEntitiesContext);
    if (!context) throw new Error("Developer error: No SWRE loaded. Please report");
    return context;
}

/** Should be called below <LoadSRWithEntitiesContext> but will fetch and warn if missed */
export function useServiceRecipientWithEntities(srId: number | undefined) {
    const services = useServicesContext();
    const srweFromContext = useContext(SRWithEntitiesContext);
    const {resolved, error, loading, reload} = usePromise(() => {
        if (srweFromContext) return Promise.resolve(null);
        if (srId == undefined) return Promise.resolve(null);
        console.log("Fallback load of SRWE: Should use LoadSRWithEntitiesContext above this");
        return loadSrWithEntities({srId, services});
    }, [srId, !srweFromContext]); // i.e. we're dep on whether context is null or not
    return {context: srweFromContext?.resolved || resolved || undefined, error, loading, reload};
}

/**
 * Load serviceRecipient then populate worker, referral or building based on on what we got
 * @param props
 */
export const loadSrWithEntities: (
    props: AsyncServiceRecipientParamsWithServices
) => Promise<ServiceRecipientWithEntitiesContext> = props => {
    const {srId, services} = props;
    return loadSrWithEntitiesPromise(srId!, services);
};
export function loadSrWithEntitiesPromise(
    srId: number,
    services: EccoAPI
): Promise<ServiceRecipientWithEntitiesContext> {
    return services
        .referralRepository()
        .findOneServiceRecipientWithEntities(srId)
        .then(sr => {
            let srweQ: Promise<ServiceRecipientWithEntitiesContext>;
            switch (sr.prefix) {
                case "r":
                    srweQ = services
                        .referralRepository()
                        .findOneReferralSummaryWithEntitiesUsingDto(srId)
                        .then(referral =>
                            services.clientRepository
                                .findOneClient(referral.clientId)
                                .then(client => ({
                                    serviceRecipient: sr,
                                    serviceType: sr.configResolver.getServiceType(),
                                    referral,
                                    client
                                }))
                        );
                    break;

                case "w":
                    srweQ = services.workersRepository
                        .findOneWorkerJob(sr.parentId!)
                        .then(workerJob =>
                            services.workersRepository
                                .findOneWorker(workerJob.workerId)
                                .then(worker => {
                                    return {
                                        serviceRecipient: sr,
                                        serviceType: sr.configResolver.getServiceType(),
                                        workerJob,
                                        worker
                                    };
                                })
                        );
                    break;

                case "b":
                    srweQ = services
                        .getBuildingRepository()
                        .findOneBuilding(sr.parentId!)
                        .then(building => ({
                            serviceRecipient: sr,
                            serviceType: sr.configResolver.getServiceType(),
                            building
                        }));
                    break;

                case "i":
                    srweQ = services.incidentsRepository
                        .findByServiceRecipientId(sr.serviceRecipientId)
                        .then(incident => ({
                            serviceRecipient: sr,
                            serviceType: sr.configResolver.getServiceType(),
                            incident
                        }));
                    break;

                case "m":
                    srweQ = services.repairsRepository
                        .findByServiceRecipientId(sr.serviceRecipientId)
                        .then(repair => ({
                            serviceRecipient: sr,
                            serviceType: sr.configResolver.getServiceType(),
                            repair
                        }));
                    break;

                case "mv":
                    srweQ = services.managedVoidsRepository
                        .findByServiceRecipientId(sr.serviceRecipientId)
                        .then(managedVoid => ({
                            serviceRecipient: sr,
                            serviceType: sr.configResolver.getServiceType(),
                            managedVoid
                        }));
                    break;

                default:
                    srweQ = Promise.resolve({
                        serviceRecipient: sr,
                        serviceType: sr.configResolver.getServiceType()
                    });
            }

            return srweQ;
        });
}


/** @deprecated Use LoadSRWithEntitiesContext which uses the same loadSrWithEntities() source */
export const AsyncServiceRecipientWithEntities: FC<AsyncServiceRecipientParams> & {
    Resolved: (props: {
        children: (data: ServiceRecipientWithEntitiesContext) => ReactElement;
    }) => ReactElement;
} = props => {
    if (!props.srId) {
        console.error("srId not specified in AsyncServiceRecipientWithEntities");
        return <h2>Error: srId not specified</h2>;
    }
    console.debug(props.children);
    return (
        <LoadSRWithEntitiesContext srId={props.srId}>{props.children}</LoadSRWithEntitiesContext>
    );
};
/** @deprecated Use useCurrentServiceRecipientWithEntities() */
AsyncServiceRecipientWithEntities.Resolved = (props) => {
    const context = useCurrentServiceRecipientWithEntities();
    if (!context?.resolved) return <></>;
    return props.children(context.resolved)!;
};


function useLatestCommands(srId: number) {
    const referralRepository = useServicesContext().referralRepository();
    return usePromise(
        () =>
            referralRepository.findLatestCommandPerTaskName(srId).then(cmds => {
                const data: StringToObjectMap<ServiceRecipientTaskBaseCommandDto> = {};
                cmds.forEach(c => (data[translateTaskName(c.taskName)] = c));
                return data;
            }),
        [srId],
        commandQueueFlushedEventBus
    );
}

interface LastestCommandsContext {
    audits: StringToObjectMap<ServiceRecipientTaskBaseCommandDto>;
}

const LatestCommandsContext = createContext<LastestCommandsContext>({audits: {}});

export const WithLatestCommands: FC<{srId: number}> = ({srId, children}) => {
    const {resolved, error} = useLatestCommands(srId);
    if (!resolved) {
        return <LoadingOrError error={error} />;
    }
    return (
        <LatestCommandsContext.Provider value={{audits: resolved}}>
            {children}
        </LatestCommandsContext.Provider>
    );
};

export function useCurrentLatestCommands() {
    return useContext(LatestCommandsContext);
}