import * as React from "react";
import {FC, useEffect, useState} from "react";

import {useCommandSourceRegistration, withCommandForm} from "../cmd-queue/CommandForm";
import {createTextInput, possiblyModalForm, dropdownList} from "ecco-components-core";
import {BuildingChangeCommand, CommandQueue, CommandSource} from "ecco-commands";
import {Building as BuildingDto, RateCardDto, ChargeCategoryCombination} from "ecco-dto";
import {Checkbox, FormControlLabel, Grid} from "@eccosolutions/ecco-mui";
import {useServicesContext} from "../ServicesContext";
import {useServiceRecipientWithEntities} from "../data/serviceRecipientHooks";
import {LoadingSpinner} from "../Loading";
import {useCommandSourceStateHolder, useRateCards} from "../data/entityLoadHooks";
// import {SelectList as SelectListMui} from "../SelectList";
import {AddressValidation, AddressRenderer} from "../address/AddressDetail";

class BuildingTypes {
    public static CARERUN_RESOURCE_ID = 158;
    public static BUILDING_RESOURCE_ID = 132;
    public static ROOM_RESOURCE_ID = 134;
}
enum BuildingTypesEnum {
    CARE_RUN,
    BUILDING,
    ROOM
}

/**
 * @param newParentBuildingIds - choose a building parent from a list
 *     arrays used by CareRunEditFromSvcRecIds which is the services based rota (svccats:), which needs the BuildingSelector
 *     for other usages (ie rooms) we just extract the single id
 */
// TEST
export const CareRunEditor: FC<{
    serviceRecipientId?: number | undefined;
    newParentBuildingIds?: number[] | undefined;
    modalHide?: (() => void) | undefined;
}> = props => {
    // choose a single id OR undefined
    // ** this means service-based careruns will fail **
    const newParentBuildingId =
        props.newParentBuildingIds && props.newParentBuildingIds.length == 1
            ? props.newParentBuildingIds[0]
            : undefined;

    return (
        <BuildingEditor
            serviceRecipientId={props.serviceRecipientId}
            newParentBuildingId={newParentBuildingId}
            modal={true}
            modalHide={props.modalHide}
            typeIn={BuildingTypesEnum.CARE_RUN}
        />
    );
};

export const BuildingEditor: FC<{
    serviceRecipientId?: number | undefined;
    newParentBuildingId?: number | undefined;
    modal: boolean;
    typeIn?: BuildingTypesEnum | undefined; // when we want to force the type
    modalHide?: (() => void) | undefined;
}> = props => {
    // NB reload after a save should try to reload any saved srId
    const {context, loading, reload} = useServiceRecipientWithEntities(props.serviceRecipientId);
    const {sessionData} = useServicesContext();
    if (loading) {
        return <LoadingSpinner />;
    }

    const type =
        context?.building?.resourceTypeId == BuildingTypes.CARERUN_RESOURCE_ID ||
        props.typeIn == BuildingTypesEnum.CARE_RUN
            ? BuildingTypesEnum.CARE_RUN
            : context?.building?.parentId || props.newParentBuildingId
            ? BuildingTypesEnum.ROOM
            : BuildingTypesEnum.BUILDING;

    let title = "";
    switch (type) {
        case BuildingTypesEnum.CARE_RUN:
            title = "care run";
            break;
        case BuildingTypesEnum.ROOM:
            title = "room";
            break;
        case BuildingTypesEnum.BUILDING:
            title = "building";
            break;
    }

    return withCommandForm(commandForm =>
        possiblyModalForm(
            title,
            props.modal,
            true,
            () => {
                commandForm.cancelForm();
                props.modalHide && props.modalHide();
            },
            () =>
                commandForm.submitForm().then(() => {
                    reload();
                    props.modalHide && props.modalHide();
                }),
            false, // TODO could emitChangesTo and see if there are any commands
            false,
            <Building
                building={context?.building}
                newParentBuildingId={props.newParentBuildingId}
                type={type}
                readOnly={!sessionData.hasRoleBuildingAdmin()}
            />,
            undefined,
            undefined,
            undefined,
            "xs"
        )
    );
};

/**
 * formRef must extend CommandSource.  Used to emitChangesTo(cmdQ) when submitting. Could also ask hasChanges()...
 * newParentBuildingIds - pre-populate a new child building with parentId, but choose from this list
 * newResourcesTypeId - pre-populate a new child building
 */
// TEST - service rota related?
/*export const BuildingParentSelector: FC<{
    building: BuildingDto | null | undefined;
    newParentBuildingIds: number[] | undefined;
    newResourceTypeId: number | undefined;
    commandForm: CommandForm;
}> = props => {
    const {building, commandForm, newParentBuildingIds, newResourceTypeId} = props;
    const {sessionData} = useServicesContext();

    const [newParentBuildingId, setNewParentBuildingId] = useState(
        newParentBuildingIds && newParentBuildingIds.length == 1 ? newParentBuildingIds[0] : null
    );

    // NB load all parent buildings IF we have a parent building
    // but this is not really used as we simply create units(rooms) from inside the building
    const selectParentBuilding = newParentBuildingIds && newParentBuildingIds.length > 1;
    const {buildings, loading} = useBuildings(true, selectParentBuilding);

    if (loading) {
        return null;
    }

    const selectParentBuildings =
        newParentBuildingIds && buildings
            ? buildings.filter(b => newParentBuildingIds.indexOf(b.buildingId) > -1)
            : null;
    return (
        <>
            {selectParentBuildings && (
                <div className="container-fluid v-gap-15">
                    <Grid container>
                        <Entry>
                            <SelectBuilding
                                buildingId={newParentBuildingId ?? null}
                                onChange={setNewParentBuildingId}
                                buildings={selectParentBuildings}
                            />
                        </Entry>
                    </Grid>
                </div>
            )}

            <Building
                building={building}
                newParentBuildingId={newParentBuildingId || undefined}
                newResourceTypeId={newResourceTypeId}
                readOnly={!sessionData.hasRoleBuildingAdmin()}
            />

            {/!*{addressable && <AddressDetail address={address} ref={ref} onValid={setSaveEnabled} />}*!/}
        </>
    );
};*/

interface BuildingState extends BuildingDto {
    requiredFields: BuildingField[];
    optionalFields: BuildingField[];
}

type BuildingField = keyof BuildingDto;

const Entry: FC = props => (
    <>
        <Grid item md={1} />
        <Grid item xs={12} md={10}>
            {props.children}
        </Grid>
        <Grid item md={1} />
    </>
);

const emitCommand = (cmdQ: CommandQueue, init: BuildingDto, buildingCurr: BuildingDto) => {
    const building = init;
    const cmd = new BuildingChangeCommand(
        building.serviceRecipientId ? "update" : "add",
        building.serviceRecipientId
    );
    cmd.changeParentId(building.parentId, buildingCurr.parentId)
        .changeName(building.name, buildingCurr.name)
        .changeResourceType(building.resourceTypeId, buildingCurr.resourceTypeId)
        .changeAddressLocation(building.locationId, buildingCurr.locationId)
        .changeAddress(building.address || null, buildingCurr.address || null)
        .changeDisabled(building.disabled, buildingCurr.disabled)
        .changeChargeCategoryCombinations(
            building.chargeCategoryCombinations,
            buildingCurr.chargeCategoryCombinations
        );

    if (cmd.hasChanges()) {
        cmdQ.addCommand(cmd);
    }
};

const Building: FC<{
    readOnly: boolean;
    building?: BuildingDto | null | undefined;
    newParentBuildingId?: number | undefined;
    type: BuildingTypesEnum;
}> = props => {
    return props.type == BuildingTypesEnum.CARE_RUN ? (
        <CareRunLayout
            careRun={props.building!}
            newParentBuildingId={props.newParentBuildingId}
            readOnly={props.readOnly}
        />
    ) : (
        <BuildingWithRateCards
            building={props.building}
            newParentBuildingId={props.newParentBuildingId}
            type={props.type}
            readOnly={props.readOnly}
        />
    );
};

/**
 * Intermediate component that loads rate cards for a building and passes them to BuildingLayout
 */
const BuildingWithRateCards: FC<{
    readOnly: boolean;
    building?: BuildingDto | null | undefined;
    newParentBuildingId?: number | undefined;
    type: BuildingTypesEnum;
}> = props => {
    // Load rate cards for hardcoded contract ID of 1
    const {rateCards, error: rateCardsError, loading: rateCardsLoading} = useRateCards(1);

    if (rateCardsLoading) {
        return <LoadingSpinner />;
    }

    if (rateCardsError) {
        console.warn("Error loading rate cards:", rateCardsError);
    }

    return (
        <BuildingLayout
            building={props.building}
            newParentBuildingId={props.newParentBuildingId}
            type={props.type}
            readOnly={props.readOnly}
            rateCards={rateCards || []}
        />
    );
};

// careruns have a parentId of the building and resourceTypeId of carerun
// ** NB we've lost the use of services based rota selecting a parent building - see doc on BuildingEditor **
// ** where a selection of buildings could be chosen **
//  so currently, service based rotas will fail to create
const CareRunLayout: FC<{
    readOnly: boolean;
    careRun: BuildingDto;
    newParentBuildingId?: number | undefined;
}> = props => {
    // CMD INIT state only, as we need an empty bldg to then emit a change cmd
    // NB we need to specify the initial props now, as a useEffect doesn't survive the re-render
    const initStateBldg: BuildingDto = props.careRun || ({} as BuildingDto);
    const initState: BuildingState = {
        ...initStateBldg,
        requiredFields: ["name"],
        optionalFields: []
    };
    const {stateHolderMemo, stateHolder, dispatchHolder} = useCommandSourceStateHolder(initState);

    useEffect(() => {
        if (!props.careRun) {
            dispatchHolder({
                resourceTypeId: BuildingTypes.CARERUN_RESOURCE_ID,
                parentId: props.newParentBuildingId
            });
        }
    }, []);

    const commandSource: CommandSource = {
        // Note: The closure will freeze whatever values are seen here, so state needs to be in a holder
        emitChangesTo: function (cmdQ: CommandQueue): void {
            emitCommand(cmdQ, initState, stateHolderMemo.state!);
        },
        getErrors(): string[] {
            if (!stateHolderMemo.state.name) {
                return ["name required"];
            }
            return [];
        }
    };
    useCommandSourceRegistration(commandSource);

    return (
        stateHolder && (
            <div className="container-fluid v-gap-15">
                <Grid container>
                    <Entry>
                        {createTextInput(
                            "name",
                            "name",
                            stateHolder.name,
                            val => dispatchHolder({name: val}),
                            "text",
                            {
                                placeholder: "nick name",
                                required: true
                            }
                        )}
                    </Entry>
                    <Entry>
                        <FormControlLabel
                            control={
                                <Checkbox
                                    name={`bldg-disable`}
                                    checked={stateHolder.disabled}
                                    onChange={(_, checked) => {
                                        dispatchHolder({disabled: checked});
                                    }}
                                />
                            }
                            label={"disabled"}
                        />
                    </Entry>
                </Grid>
            </div>
        )
    );
};

const BuildingLayout: FC<{
    readOnly: boolean;
    building?: BuildingDto | null | undefined;
    newParentBuildingId?: number | undefined;
    type: BuildingTypesEnum;
    rateCards?: RateCardDto[];
}> = props => {
    const {sessionData} = useServicesContext();
    const requiredFields = sessionData.getSettingAsArray(
        "com.ecco.forms:BUILDING_REQUIRED_FIELDS"
    ) as BuildingField[];
    const optionalFields = sessionData.getSettingAsArray(
        "com.ecco.forms:BUILDING_OPTIONAL_FIELDS"
    ) as BuildingField[];

    const resourceTypeId =
        props.type == BuildingTypesEnum.ROOM
            ? BuildingTypes.ROOM_RESOURCE_ID
            : BuildingTypes.BUILDING_RESOURCE_ID;
    const showAddress = props.type == BuildingTypesEnum.BUILDING;

    // CMD INIT state only, as we need an empty bldg to then emit a change cmd
    // NB we need to specify the initial props now, as a useEffect doesn't survive the re-render
    const initStateBldg: BuildingDto = props.building || ({} as BuildingDto);
    const initState: BuildingState = {
        ...initStateBldg,
        address:
            initStateBldg.address || (showAddress ? AddressValidation.getNewAddress() : undefined),
        requiredFields,
        optionalFields
    };

    const {stateHolderMemo, stateHolder, dispatchHolder} = useCommandSourceStateHolder(initState);

    useEffect(() => {
        if (!props.building) {
            dispatchHolder({resourceTypeId: resourceTypeId, parentId: props.newParentBuildingId});
        }
    }, []);

    const commandSource: CommandSource = {
        // Note: The closure will freeze whatever values are seen here, so state needs to be in a holder
        emitChangesTo: function (cmdQ: CommandQueue): void {
            emitCommand(cmdQ, initState, stateHolderMemo.state!);
        },
        getErrors(): string[] {
            if (showAddress) {
                const valid = AddressValidation.isValid(
                    AddressValidation.validate(stateHolderMemo.state.address)
                );
                if (!valid) {
                    return ["address required"];
                }
            }

            if (!stateHolderMemo.state.resourceTypeId) {
                return ["type required"];
            }
            if (!stateHolderMemo.state.name) {
                return ["name required"];
            }
            return [];
        }
    };
    useCommandSourceRegistration(commandSource);

    return (
        stateHolder && (
            <div className="container-fluid v-gap-15">
                <Grid container>
                    <Entry>
                        {createTextInput(
                            "name",
                            "name",
                            stateHolder.name,
                            val => dispatchHolder({name: val}),
                            "text",
                            {
                                placeholder: "nick name",
                                required: true
                            }
                        )}
                    </Entry>
                    {/*{showResourceType && (
                        <Entry>
                             TODO required={true} and readOnly=this.props.readOnly
                             may need to use https://codesandbox.io/s/react-select-v2-required-input-3xvvb from https://github.com/JedWatson/react-select/issues/3140
                            <SelectListMui
                                isMulti={false}
                                placeholder={"type"}
                                createNew={false}
                                getOptionLabel={l => (l as ListDefinitionEntry).getDisplayName()}
                                getOptionValue={l => (l as ListDefinitionEntry).getId().toString()}
                                value={
                                    stateHolder.resourceTypeId
                                        ? sessionData.getListDefinitionEntryById(
                                              stateHolder.resourceTypeId
                                          )
                                        : undefined
                                }
                                options={sessionData.getListDefinitionEntriesUnderParentRecursively(
                                    LIST_DEF_IDS.BUILDING_RESOURCETYPE_ID
                                )}
                                onChange={value =>
                                    dispatchHolder({
                                        resourceTypeId: (value as ListDefinitionEntry)?.getId()
                                    })
                                }
                            />
                        </Entry>
                    )}*/}
                    {showAddress && (
                        <AddressRenderer
                            address={stateHolder.address}
                            errors={AddressValidation.validate(stateHolder.address)}
                            stateSetter={adr => dispatchHolder({address: adr})}
                        />
                    )}
                    {props.rateCards && props.rateCards.length > 0 && (
                        <RateCardSelector
                            rateCards={props.rateCards}
                            building={stateHolder}
                            onChargeCategoryChange={(chargeNameId, chargeCategoryId) => {
                                // Create new charge category combination
                                const newCombination: ChargeCategoryCombination = {
                                    chargeNameId,
                                    chargeCategoryId
                                };

                                // Update building's charge category combinations
                                const currentCombinations =
                                    stateHolder.chargeCategoryCombinations || [];

                                // Remove any existing combinations with the same chargeNameId
                                const filteredCombinations = currentCombinations.filter(
                                    existing => existing.chargeNameId !== chargeNameId
                                );

                                // Add the new combination
                                const updatedCombinations = [
                                    ...filteredCombinations,
                                    newCombination
                                ];

                                dispatchHolder({
                                    chargeCategoryCombinations: updatedCombinations
                                });
                            }}
                        />
                    )}
                </Grid>
            </div>
        )
    );
};

/**
 * Component that displays rate cards as a dropdown showing charge names
 */
const RateCardSelector: FC<{
    rateCards: RateCardDto[];
    building: BuildingDto;
    onChargeCategoryChange: (chargeNameId: number | undefined, chargeCategoryId: number | undefined) => void;
}> = props => {
    const {sessionData} = useServicesContext();
    const [selectedRateCardId, setSelectedRateCardId] = useState<number | null>(null);
    const [selectedRateCardEntryId, setSelectedRateCardEntryId] = useState<number | null>(null);

    // Convert rate cards to dropdown options with charge names
    const rateCardOptions = props.rateCards.map(rateCard => {
        const chargeName = rateCard.chargeNameId
            ? sessionData.getListDefinitionEntryById(rateCard.chargeNameId).getDisplayName()
            : rateCard.name;

        return {
            id: rateCard.rateCardId,
            name: chargeName,
            disabled: false
        };
    });

    // Find the selected rate card and its entries
    const selectedRateCard = selectedRateCardId
        ? props.rateCards.find(rc => rc.rateCardId === selectedRateCardId)
        : null;

    // Convert rate card entries to dropdown options showing charge category names
    const rateCardEntryOptions = selectedRateCard?.rateCardEntries?.map(entry => {
        const chargeCategoryName = entry.matchingChargeCategoryId
            ? sessionData.getListDefinitionEntryById(entry.matchingChargeCategoryId).getDisplayName()
            : `Entry ${entry.rateCardEntryId}`;

        return {
            id: entry.rateCardEntryId,
            name: chargeCategoryName,
            disabled: false
        };
    }) || [];

    const handleRateCardChange = (newState: {selectedRateCardId: number | null}) => {
        setSelectedRateCardId(newState.selectedRateCardId);
        // Reset the entry selection when rate card changes
        setSelectedRateCardEntryId(null);
    };

    const handleRateCardEntryChange = (newState: {selectedRateCardEntryId: number | null}) => {
        const newEntryId = newState.selectedRateCardEntryId;
        setSelectedRateCardEntryId(newEntryId);

        // When both rate card and entry are selected, update the building's charge category combinations
        if (selectedRateCard && newEntryId) {
            const selectedEntry = selectedRateCard.rateCardEntries?.find(entry => entry.rateCardEntryId === newEntryId);
            if (selectedEntry) {
                // Call the callback with chargeNameId from rate card and chargeCategoryId from entry
                props.onChargeCategoryChange(
                    selectedRateCard.chargeNameId,
                    selectedEntry.matchingChargeCategoryId
                );
            }
        }
    };

    return (
        <>
            <Entry>
                {dropdownList(
                    "Rate Card",
                    handleRateCardChange,
                    {selectedRateCardId},
                    "selectedRateCardId",
                    rateCardOptions,
                    {},
                    false,
                    false
                )}
            </Entry>
            {selectedRateCard && rateCardEntryOptions.length > 0 && (
                <Entry>
                    {dropdownList(
                        "Rate Card Entry",
                        handleRateCardEntryChange,
                        {selectedRateCardEntryId},
                        "selectedRateCardEntryId",
                        rateCardEntryOptions,
                        {},
                        false,
                        false
                    )}
                </Entry>
            )}
        </>
    );
};
